# 实时模式重构说明

## 重构目标

删除非实时模式和模拟功能，系统只保留真正的实时模式实现。

## 主要改动

### 1. app/stores/gemini.js

#### 删除的功能
- `isRealTimeMode` 状态变量（系统只有实时模式）
- 非实时模式的音频处理逻辑
- 模拟响应功能（`handleMockResponse`）
- 模拟语音识别结果

#### 修改的功能
- **disconnect()**: 修复了 `close()` 方法调用错误
- **sendAudio()**: 简化为只处理实时音频流，移除模拟逻辑
- **speakText()**: 播放完成后直接回到 'active' 状态
- **sendInterrupt()**: 打断后直接回到 'active' 状态
- **方法重命名**: `startRealTimeMode/endRealTimeMode` → `startInterview/endInterview`

#### 保留的功能
- 连接管理（connect/disconnect）
- 语音合成播放
- 打断功能
- 音频播放

### 2. app/pages/interview/interview.vue

#### 删除的功能
- 非实时模式的录音处理逻辑
- 完整音频文件的读取和发送
- 模式切换相关的复杂逻辑

#### 修改的功能
- **变量重命名**: `isRealTimeActive` → `isInterviewActive`
- **方法重命名**: 
  - `toggleRealTimeMode` → `toggleInterview`
  - `startRealTimeInterview` → `startInterview`
  - `endRealTimeInterview` → `endInterview`
  - `startRealTimeRecording` → `startRecording`
- **录音停止处理**: 简化为只记录日志，不处理文件
- **音频帧处理**: 保持实时发送逻辑
- **状态管理**: 简化状态转换逻辑

#### 保留的功能
- 实时音频帧处理（onFrameRecorded）
- 录音权限管理
- 静音功能
- 打断功能
- 音频播放控制

## 当前实现状态

### ✅ 已完成
1. 删除所有非实时模式代码
2. 删除模拟功能
3. 修复 close() 方法错误
4. 简化状态管理
5. 统一命名规范

### ⚠️ 待实现
1. **真正的语音识别集成**
   - 需要集成语音识别API（如百度、阿里云、腾讯云等）
   - 将音频帧实时转换为文本

2. **Gemini Live API集成**
   - 使用 ephemeral tokens 建立 WebSocket 连接
   - 实现真正的实时流式对话
   - 处理实时音频输入和输出

3. **音频格式处理**
   - 确保音频格式与API要求匹配
   - 可能需要音频格式转换

## 技术要点

### 实时音频流处理
```javascript
// 音频帧实时处理
recorderManager.onFrameRecorded((res) => {
  if (isInterviewActive.value && !isMuted.value && geminiStore.isConnected) {
    const audioData = res.frameBuffer
    if (audioData && audioData.byteLength > 0) {
      geminiStore.sendAudio(audioData) // 发送到语音识别API
    }
  }
})
```

### 状态管理简化
- 只有 'idle', 'connecting', 'active', 'speaking' 四种状态
- 移除了实时/非实时模式的复杂切换逻辑

### 错误处理
- 保持了录音权限检查
- 保持了连接错误处理
- 简化了状态恢复逻辑

## 下一步开发建议

1. **集成语音识别服务**
   - 选择合适的语音识别API
   - 实现音频流到文本的实时转换

2. **实现Gemini Live API**
   - 参考官方文档实现 WebSocket 连接
   - 使用 ephemeral tokens 进行身份验证

3. **优化音频处理**
   - 调整音频采样率和格式
   - 实现音频缓冲和流控制

4. **测试和调试**
   - 添加详细的调试日志
   - 测试各种网络条件下的表现
   - 优化延迟和音质

## 注意事项

- 当前 `sendAudio()` 方法只是占位符，需要实现真正的API调用
- 音频格式可能需要根据选择的语音识别服务进行调整
- 需要处理网络延迟和断线重连
- 考虑添加音频质量检测和自适应调整
