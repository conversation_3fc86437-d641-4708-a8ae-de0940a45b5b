import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { GoogleGenerativeAI } from '@google/generative-ai'

export const useGeminiStore = defineStore('gemini', () => {
  // 状态
  const session = ref(null)
  const isConnected = ref(false)
  const connectionStatus = ref('disconnected')
  const lastMessage = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(3)

  // 面试状态
  const interviewStatus = ref('idle')
  const ephemeralToken = ref('')
  const currentDomain = ref('技术面试')
  // 系统只有实时模式，删除 isRealTimeMode 状态

  // 计算属性
  const canSendMessage = computed(() => isConnected.value && session.value)

  // 设置临时令牌
  const setEphemeralToken = (token, domain) => {
    ephemeralToken.value = token
    currentDomain.value = domain
  }

  // 连接Gemini API
  const connect = async () => {
    if (session.value && isConnected.value) {
      return
    }

    if (!ephemeralToken.value) {
      throw new Error('临时令牌不存在')
    }

    connectionStatus.value = 'connecting'

    try {
      const genAI = new GoogleGenerativeAI(ephemeralToken.value)
      const model = genAI.getGenerativeModel({ model: 'gemini-pro' })
      
      session.value = model
      isConnected.value = true
      connectionStatus.value = 'connected'
      reconnectAttempts.value = 0
      interviewStatus.value = 'idle'
      
      console.log('Gemini API连接成功')
    } catch (error) {
      console.error('Gemini API连接失败:', error)
      connectionStatus.value = 'error'
      scheduleReconnect()
      throw error
    }
  }

  // 断开连接
  const disconnect = () => {
    if (session.value) {
      // GoogleGenerativeAI 的模型对象没有 close 方法，直接设置为 null
      session.value = null
    }
    isConnected.value = false
    connectionStatus.value = 'disconnected'
    interviewStatus.value = 'idle'
    ephemeralToken.value = ''
  }

  // 处理内容响应
  const handleResponse = (response) => {
    try {
      const text = response.response.text()
      lastMessage.value = { text }
      
      // 使用语音合成播放回答
      if (text) {
        speakText(text)
        interviewStatus.value = 'speaking'
      }
    } catch (error) {
      console.error('处理响应失败:', error)
    }
  }

  // 语音合成播放文本
  const speakText = (text) => {
    try {
      // 使用uni-app的语音合成API
      uni.createSpeechSynthesisUtterance && uni.createSpeechSynthesisUtterance({
        text: text,
        lang: 'zh-CN',
        rate: 1.0,
        pitch: 1.0,
        volume: 1.0,
        success: () => {
          console.log('语音播放成功')
          interviewStatus.value = 'active' // 系统只有实时模式，播放完成后回到active状态
        },
        fail: (error) => {
          console.error('语音播放失败:', error)
          interviewStatus.value = 'active' // 系统只有实时模式，播放失败后也回到active状态
        }
      })
    } catch (error) {
      console.error('语音合成失败:', error)
      interviewStatus.value = 'active' // 系统只有实时模式，异常后也回到active状态
    }
  }

  // 发送音频（实时流式传输）
  const sendAudio = async (audioData) => {
    if (!canSendMessage.value) return false

    try {
      console.log('实时发送音频数据，大小:', audioData.byteLength || audioData.length || 'unknown')

      // TODO: 这里需要实现真正的实时语音识别和Gemini Live API集成
      // 1. 将音频数据发送到语音识别服务
      // 2. 获取实时转录文本
      // 3. 将文本发送到Gemini Live API
      // 4. 接收实时AI响应

      console.warn('实时音频处理功能待实现 - 需要集成语音识别API和Gemini Live API')
      return true
    } catch (error) {
      console.error('发送音频失败:', error)
      return false
    }
  }

  // 发送文本
  const sendText = async (text) => {
    if (!canSendMessage.value) return false

    try {
      const prompt = `你是一个专业的${currentDomain.value}面试官。请用自然、友好的语调回答以下问题或进行面试对话：${text}`
      const result = await session.value.generateContent(prompt)
      handleResponse(result)
      return true
    } catch (error) {
      console.error('发送文本失败:', error)
      return false
    }
  }

  // 发送打断信号
  const sendInterrupt = () => {
    try {
      // 停止当前语音播放
      uni.stopSpeechSynthesis && uni.stopSpeechSynthesis()
      interviewStatus.value = 'active' // 系统只有实时模式，打断后回到active状态
      return true
    } catch (error) {
      console.error('发送打断信号失败:', error)
      return false
    }
  }

  // 播放音频
  const playAudio = (audioData) => {
    try {
      const innerAudioContext = uni.createInnerAudioContext()
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.wav`

      uni.getFileSystemManager().writeFile({
        filePath: tempFilePath,
        data: audioData,
        encoding: 'base64',
        success: () => {
          innerAudioContext.src = tempFilePath
          innerAudioContext.play()

          innerAudioContext.onEnded(() => {
            uni.getFileSystemManager().unlink({
              filePath: tempFilePath,
              success: () => console.log('临时音频文件已删除'),
              fail: (err) => console.warn('删除临时音频文件失败:', err)
            })
            innerAudioContext.destroy()
          })

          innerAudioContext.onError((err) => {
            console.error('音频播放失败:', err)
            innerAudioContext.destroy()
          })
        },
        fail: (err) => {
          console.error('写入音频文件失败:', err)
        }
      })
    } catch (error) {
      console.error('播放音频异常:', error)
    }
  }

  // 开始面试（系统只有实时模式）
  const startInterview = () => {
    interviewStatus.value = 'active'
    console.log('Gemini Store: 面试已开始（实时模式）')
  }

  // 结束面试
  const endInterview = () => {
    interviewStatus.value = 'idle'
    console.log('Gemini Store: 面试已结束')
  }

  // 重连机制
  const scheduleReconnect = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts.value) {
      console.log('达到最大重连次数，停止重连')
      return
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 10000)
    
    setTimeout(() => {
      reconnectAttempts.value++
      connect().catch(err => {
        console.error('重连失败:', err)
      })
    }, delay)
  }

  return {
    // 状态
    session,
    isConnected,
    connectionStatus,
    lastMessage,
    interviewStatus,
    ephemeralToken,
    currentDomain,

    // 计算属性
    canSendMessage,

    // 方法
    setEphemeralToken,
    connect,
    disconnect,
    sendAudio,
    sendText,
    sendInterrupt,
    startInterview,
    endInterview,
    playAudio
  }
})