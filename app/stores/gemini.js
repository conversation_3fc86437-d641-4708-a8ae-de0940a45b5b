import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { GoogleGenAI, Modality } from '@google/genai/web'

// uni-app环境下的Web API polyfills
if (typeof globalThis.URL === 'undefined') {
  globalThis.URL = class URL {
    constructor(url, base) {
      // 简单的URL解析
      if (base) {
        // 如果有base URL，进行简单的拼接
        if (url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//')) {
          this.href = url
        } else {
          const baseUrl = base.endsWith('/') ? base.slice(0, -1) : base
          const path = url.startsWith('/') ? url : '/' + url
          this.href = baseUrl + path
        }
      } else {
        this.href = url
      }

      // 解析URL组件
      try {
        const match = this.href.match(/^(https?:)\/\/([^\/]+)(\/.*)?$/)
        if (match) {
          this.protocol = match[1]
          this.host = match[2]
          this.pathname = match[3] || '/'
          this.origin = this.protocol + '//' + this.host
        }
      } catch (e) {
        console.warn('URL parsing failed:', e)
      }
    }

    static createObjectURL(blob) {
      // uni-app环境下不支持createObjectURL，返回空字符串
      console.warn('URL.createObjectURL not supported in uni-app environment, blob size:', blob?.size || 'unknown')
      return ''
    }

    static revokeObjectURL(url) {
      // uni-app环境下不支持revokeObjectURL
      console.warn('URL.revokeObjectURL not supported in uni-app environment, url:', url)
    }
  }
}

// Headers API polyfill
if (typeof globalThis.Headers === 'undefined') {
  globalThis.Headers = class Headers {
    constructor(init) {
      this._headers = new Map()

      if (init) {
        if (init instanceof Headers) {
          // 从另一个Headers实例复制
          for (const [key, value] of init._headers) {
            this._headers.set(key.toLowerCase(), value)
          }
        } else if (Array.isArray(init)) {
          // 从数组初始化
          for (const [key, value] of init) {
            this._headers.set(key.toLowerCase(), String(value))
          }
        } else if (typeof init === 'object') {
          // 从对象初始化
          for (const [key, value] of Object.entries(init)) {
            this._headers.set(key.toLowerCase(), String(value))
          }
        }
      }
    }

    append(name, value) {
      const key = name.toLowerCase()
      const existing = this._headers.get(key)
      if (existing) {
        this._headers.set(key, existing + ', ' + String(value))
      } else {
        this._headers.set(key, String(value))
      }
    }

    delete(name) {
      this._headers.delete(name.toLowerCase())
    }

    get(name) {
      return this._headers.get(name.toLowerCase()) || null
    }

    has(name) {
      return this._headers.has(name.toLowerCase())
    }

    set(name, value) {
      this._headers.set(name.toLowerCase(), String(value))
    }

    forEach(callback, thisArg) {
      for (const [key, value] of this._headers) {
        callback.call(thisArg, value, key, this)
      }
    }

    keys() {
      return this._headers.keys()
    }

    values() {
      return this._headers.values()
    }

    entries() {
      return this._headers.entries()
    }

    [Symbol.iterator]() {
      return this._headers.entries()
    }
  }
}

// 添加其他可能需要的Web API polyfills
if (typeof globalThis.WebSocket === 'undefined' && typeof uni !== 'undefined') {
  // 如果uni-app环境没有WebSocket，尝试使用uni的WebSocket
  globalThis.WebSocket = uni.connectSocket ? class WebSocket {
    constructor(url, protocols) {
      // 确保URL是字符串格式
      let urlString

      console.log('Original URL object type:', typeof url)
      console.log('Original URL object:', url)
      console.log('URL constructor name:', url?.constructor?.name)

      if (typeof url === 'string') {
        urlString = url
      } else if (url && url.href) {
        urlString = url.href
      } else if (url && typeof url.toString === 'function') {
        try {
          urlString = url.toString()
          // 如果toString()返回[object Object]，尝试其他方法
          if (urlString === '[object Object]') {
            // 尝试访问URL的内部属性
            if (url.protocol && url.host && url.pathname) {
              urlString = `${url.protocol}//${url.host}${url.pathname}${url.search || ''}${url.hash || ''}`
            } else {
              urlString = JSON.stringify(url)
            }
          }
        } catch (e) {
          urlString = String(url)
        }
      } else {
        urlString = String(url)
      }

      console.log('Final WebSocket URL:', urlString)
      this.url = urlString
      this.protocols = protocols
      this.readyState = 0 // CONNECTING

      // 使用uni-app的WebSocket
      this.socketTask = uni.connectSocket({
        url: urlString,
        protocols: protocols,
        success: () => {
          this.readyState = 1 // OPEN
          if (this.onopen) this.onopen()
        },
        fail: (error) => {
          this.readyState = 3 // CLOSED
          if (this.onerror) this.onerror(error)
        }
      })

      if (this.socketTask) {
        this.socketTask.onMessage((res) => {
          if (this.onmessage) this.onmessage({ data: res.data })
        })

        this.socketTask.onClose((res) => {
          this.readyState = 3 // CLOSED
          if (this.onclose) this.onclose(res)
        })

        this.socketTask.onError((error) => {
          if (this.onerror) this.onerror(error)
        })
      }
    }

    send(data) {
      if (this.socketTask && this.readyState === 1) {
        this.socketTask.send({ data })
      }
    }

    close(code, reason) {
      if (this.socketTask) {
        this.socketTask.close({ code, reason })
      }
    }
  } : globalThis.WebSocket
}

// Request API polyfill (简化版本)
if (typeof globalThis.Request === 'undefined') {
  globalThis.Request = class Request {
    constructor(input, init = {}) {
      this.url = typeof input === 'string' ? input : input.url
      this.method = init.method || 'GET'
      this.headers = new Headers(init.headers)
      this.body = init.body
    }
  }
}

// Response API polyfill (简化版本)
if (typeof globalThis.Response === 'undefined') {
  globalThis.Response = class Response {
    constructor(body, init = {}) {
      this.body = body
      this.status = init.status || 200
      this.statusText = init.statusText || 'OK'
      this.headers = new Headers(init.headers)
      this.ok = this.status >= 200 && this.status < 300
    }

    async json() {
      return JSON.parse(this.body)
    }

    async text() {
      return String(this.body)
    }
  }
}

// AbortController polyfill (简化版本)
if (typeof globalThis.AbortController === 'undefined') {
  globalThis.AbortController = class AbortController {
    constructor() {
      this.signal = {
        aborted: false,
        addEventListener: () => {},
        removeEventListener: () => {}
      }
    }

    abort() {
      this.signal.aborted = true
    }
  }
}

// Blob polyfill (简化版本)
if (typeof globalThis.Blob === 'undefined') {
  globalThis.Blob = class Blob {
    constructor(parts = [], options = {}) {
      this.parts = parts
      this.type = options.type || ''
      this.size = 0

      // 计算大小
      for (const part of parts) {
        if (part instanceof ArrayBuffer) {
          this.size += part.byteLength
        } else if (typeof part === 'string') {
          this.size += part.length
        } else if (part?.byteLength !== undefined) {
          this.size += part.byteLength
        }
      }
    }

    // 简化的arrayBuffer方法
    async arrayBuffer() {
      if (this.parts.length === 1 && this.parts[0] instanceof ArrayBuffer) {
        return this.parts[0]
      }
      // 简化处理，直接返回第一个part
      return this.parts[0] || new ArrayBuffer(0)
    }
  }
}

/**
 * Gemini Live API Store - 基于官方示例适配
 *
 * 实现功能：
 * 1. 实时语音流传输 - 基于官方 listen_audio 方法
 * 2. 实时音频接收和播放 - 基于官方 receive_audio 和 play_audio 方法
 * 3. 连续对话模式 - 无需按钮，持续录音和播放
 * 4. 中断处理 - 基于官方示例的中断机制
 * 5. 使用 ephemeral tokens 进行安全连接
 *
 * 使用方法：
 * 1. 先调用 setEphemeralToken() 设置临时令牌
 * 2. 调用 connect() 连接到 Gemini Live API
 * 3. 调用 startInterview() 开始实时面试（自动开始录音）
 * 4. 系统会自动处理音频流的发送和接收
 * 5. 调用 endInterview() 结束面试
 * 6. 调用 disconnect() 断开连接
 */
export const useGeminiStore = defineStore('gemini', () => {
  // 状态
  const session = ref(null)
  const isConnected = ref(false)
  const connectionStatus = ref('disconnected')
  const lastMessage = ref(null)
  const reconnectAttempts = ref(0)
  const maxReconnectAttempts = ref(3)

  // 面试状态
  const interviewStatus = ref('idle')
  const ephemeralToken = ref('')
  const currentDomain = ref('技术面试')

  // 实时音频流状态
  const audioInQueue = ref([])
  const audioOutQueue = ref([])
  const isRecording = ref(false)
  const isPlaying = ref(false)
  const recorderManager = ref(null)
  const innerAudioContext = ref(null)

  // 语音活动检测状态
  const audioBuffer = ref([])
  const silenceCount = ref(0)
  const isUserSpeaking = ref(false)
  const lastAudioTime = ref(0)

  // 音频配置 - 基于官方示例
  const AUDIO_CONFIG = {
    format: 'PCM_16',
    channels: 1,
    sendSampleRate: 16000,
    receiveSampleRate: 24000,
    chunkSize: 1024
  }

  // 语音活动检测配置
  const VAD_CONFIG = {
    silenceThreshold: 3, // 连续3帧静音认为说话暂停
    volumeThreshold: 0.01, // 音量阈值
    maxBufferTime: 10000, // 最大缓冲时间10秒
    sendMode: 'continuous' // 'continuous' | 'vad' | 'manual'
  }

  // Live API配置 - 基于官方JavaScript示例
  const LIVE_CONFIG = {
    model: 'gemini-2.0-flash-live-001',
    config: {
      responseModalities: [Modality.AUDIO] // 使用正确的Modality枚举
    }
  }

  // 计算属性
  const canSendMessage = computed(() => isConnected.value && session.value)

  // 🎤 音频处理函数

  // 计算音频帧的音量（简单的RMS计算）
  const calculateVolume = (audioData) => {
    if (!audioData || audioData.byteLength === 0) return 0

    try {
      const samples = new Int16Array(audioData)
      let sum = 0
      for (let i = 0; i < samples.length; i++) {
        sum += samples[i] * samples[i]
      }
      const rms = Math.sqrt(sum / samples.length)
      return rms / 32768 // 归一化到0-1
    } catch (error) {
      console.warn('计算音量失败:', error)
      return 0
    }
  }

  // 智能音频发送策略
  const processAudioFrame = (frameBuffer) => {
    const currentTime = Date.now()
    const volume = calculateVolume(frameBuffer)

    console.log(`🎤 音频帧 - 音量: ${volume.toFixed(4)}, 时间: ${currentTime}`)

    // 根据发送模式处理音频
    switch (VAD_CONFIG.sendMode) {
      case 'continuous':
        // 连续发送模式：每帧都发送
        console.log('📤 [连续模式] 立即发送音频帧')
        sendAudioFrame(frameBuffer)
        break

      case 'vad':
        // VAD模式：检测语音活动
        if (volume > VAD_CONFIG.volumeThreshold) {
          // 有声音
          isUserSpeaking.value = true
          audioBuffer.value.push(frameBuffer)
          silenceCount.value = 0
          lastAudioTime.value = currentTime

          console.log(`🗣️  [VAD模式] 检测到语音，缓冲帧数: ${audioBuffer.value.length}`)
        } else {
          // 静音
          silenceCount.value++

          if (isUserSpeaking.value && silenceCount.value >= VAD_CONFIG.silenceThreshold) {
            // 检测到说话结束
            console.log(`🤐 [VAD模式] 检测到静音，发送缓冲的 ${audioBuffer.value.length} 帧音频`)
            sendBufferedAudio()
            isUserSpeaking.value = false
          }
        }
        break

      case 'manual':
        // 手动模式：累积音频，等待手动触发
        audioBuffer.value.push(frameBuffer)
        console.log(`📝 [手动模式] 缓冲音频帧，总数: ${audioBuffer.value.length}`)
        break
    }
  }

  // 发送缓冲的音频数据
  const sendBufferedAudio = () => {
    if (audioBuffer.value.length === 0) return

    try {
      // 合并所有音频帧
      const totalLength = audioBuffer.value.reduce((sum, buffer) => sum + buffer.byteLength, 0)
      const mergedBuffer = new ArrayBuffer(totalLength)
      const mergedView = new Uint8Array(mergedBuffer)

      let offset = 0
      for (const buffer of audioBuffer.value) {
        const view = new Uint8Array(buffer)
        mergedView.set(view, offset)
        offset += buffer.byteLength
      }

      console.log(`📤 发送合并的音频数据，大小: ${totalLength} 字节`)
      sendAudioFrame(mergedBuffer)

      // 清空缓冲区
      audioBuffer.value = []
      silenceCount.value = 0
    } catch (error) {
      console.error('合并音频数据失败:', error)
    }
  }

  // 设置临时令牌
  const setEphemeralToken = (token, domain) => {
    ephemeralToken.value = token
    currentDomain.value = domain
  }

  // 连接Gemini Live API
  const connect = async () => {
    if (session.value && isConnected.value) {
      return
    }

    if (!ephemeralToken.value) {
      throw new Error('临时令牌不存在')
    }

    connectionStatus.value = 'connecting'

    try {
      // 使用正确的JavaScript SDK - 基于官方示例
      console.log('尝试连接Gemini Live API，临时令牌:', ephemeralToken.value.substring(0, 20) + '...')

      // 根据官方文档，在浏览器环境中使用临时令牌的正确方式
      // 方法1：尝试直接使用临时令牌作为API Key
      let ai
      try {
        ai = new GoogleGenAI({
          apiKey: ephemeralToken.value // 直接使用临时令牌作为API Key
        })
      } catch (error) {
        console.log('方法1失败，尝试方法2:', error.message)
        // 方法2：使用占位符API Key，通过其他方式传递临时令牌
        ai = new GoogleGenAI({
          apiKey: 'placeholder' // 占位符
        })
      }

      // 检查ai.live是否存在
      if (!ai.live) {
        throw new Error('GoogleGenAI SDK不支持live API，请检查SDK版本')
      }

      console.log('GoogleGenAI实例创建成功，开始连接Live API...')

      // 使用官方示例的连接方法
      // 根据类型定义，LiveConnectParameters需要model, callbacks, 和可选的config
      const liveSession = await ai.live.connect({
        model: LIVE_CONFIG.model,
        callbacks: {
          onopen: () => {
            console.log('Live API WebSocket连接已打开')
          },
          onmessage: (message) => {
            console.log('收到Live API消息:', message)
            handleLiveMessage(message)
          },
          onclose: (event) => {
            console.log('Live API WebSocket连接已关闭:', event)
            isConnected.value = false
          },
          onerror: (error) => {
            console.error('Live API WebSocket错误:', error)
          }
        },
        config: {
          ...LIVE_CONFIG.config,
          // 添加系统指令，让AI知道这是面试场景
          systemInstruction: `你是一个专业的${currentDomain.value}面试官。请用自然、友好的语调进行面试对话。
          - 请主动提问相关的技术问题
          - 根据候选人的回答进行深入追问
          - 保持对话的连贯性和专业性
          - 用语音回复，语调要自然友好`,
          // 根据官方文档，临时令牌可能需要通过多种方式传递
          httpOptions: {
            // 尝试通过查询参数传递临时令牌
            params: {
              access_token: ephemeralToken.value
            },
            headers: {
              // 同时尝试通过Authorization头传递
              'Authorization': `Token ${ephemeralToken.value}`
            }
          }
        }
      })

      session.value = liveSession
      isConnected.value = true
      connectionStatus.value = 'connected'
      reconnectAttempts.value = 0
      interviewStatus.value = 'idle'

      // 启动音频接收任务
      startReceiveAudioTask()

      console.log('Gemini Live API连接成功')
    } catch (error) {
      console.error('Gemini Live API连接失败:', error)
      console.error('错误详情:', error.message)
      connectionStatus.value = 'error'
      scheduleReconnect()
      throw error
    }
  }

  // 处理Live API消息 - 基于官方示例
  const handleLiveMessage = (message) => {
    try {
      console.log('处理Live API消息:', message)

      if (message.setupComplete) {
        console.log('Live API设置完成，会话ID:', message.setupComplete.sessionId)
      }

      if (message.serverContent) {
        const content = message.serverContent

        // 处理模型回复的内容
        if (content.modelTurn) {
          const modelTurn = content.modelTurn

          // 处理文本内容
          if (modelTurn.parts) {
            for (const part of modelTurn.parts) {
              if (part.text) {
                console.log('AI回复文本:', part.text)
                lastMessage.value = { text: part.text }
              }

              // 处理音频内容
              if (part.inlineData && part.inlineData.mimeType?.includes('audio')) {
                console.log('收到AI音频回复')
                audioInQueue.value.push(part.inlineData.data)
                playReceivedAudio(part.inlineData.data)
              }
            }
          }
        }

        // 处理回合完成
        if (content.turnComplete) {
          console.log('AI回合完成')
          interviewStatus.value = 'active'
        }

        // 处理中断信号
        if (content.interrupted) {
          console.log('检测到中断信号，清空音频队列')
          while (audioInQueue.value.length > 0) {
            audioInQueue.value.shift()
          }
        }
      }

      if (message.toolCall) {
        console.log('收到工具调用请求:', message.toolCall)
        // TODO: 处理工具调用
      }

    } catch (error) {
      console.error('处理Live API消息失败:', error)
    }
  }

  // 启动音频接收任务 - 现在通过回调处理
  const startReceiveAudioTask = async () => {
    // Live API通过回调处理消息，不需要单独的接收任务
    console.log('Live API消息接收通过回调处理')
  }

  // 断开连接
  const disconnect = () => {
    // 停止录音和播放
    stopRecording()
    stopPlaying()

    if (session.value) {
      // Live API session 需要正确关闭
      try {
        session.value.close && session.value.close()
      } catch (error) {
        console.warn('关闭Live API session失败:', error)
      }
      session.value = null
    }

    isConnected.value = false
    connectionStatus.value = 'disconnected'
    interviewStatus.value = 'idle'
    ephemeralToken.value = ''

    // 清空音频队列
    audioInQueue.value = []
    audioOutQueue.value = []
  }

  // 处理内容响应
  const handleResponse = (response) => {
    try {
      const text = response.response.text()
      lastMessage.value = { text }
      
      // 使用语音合成播放回答
      if (text) {
        speakText(text)
        interviewStatus.value = 'speaking'
      }
    } catch (error) {
      console.error('处理响应失败:', error)
    }
  }

  // 语音合成播放文本
  const speakText = (text) => {
    try {
      // 使用uni-app的语音合成API
      uni.createSpeechSynthesisUtterance && uni.createSpeechSynthesisUtterance({
        text: text,
        lang: 'zh-CN',
        rate: 1.0,
        pitch: 1.0,
        volume: 1.0,
        success: () => {
          console.log('语音播放成功')
          interviewStatus.value = 'active' // 系统只有实时模式，播放完成后回到active状态
        },
        fail: (error) => {
          console.error('语音播放失败:', error)
          interviewStatus.value = 'active' // 系统只有实时模式，播放失败后也回到active状态
        }
      })
    } catch (error) {
      console.error('语音合成失败:', error)
      interviewStatus.value = 'active' // 系统只有实时模式，异常后也回到active状态
    }
  }

  // 开始录音 - 基于官方示例的listen_audio方法
  const startRecording = async () => {
    if (isRecording.value || !isConnected.value) return false

    try {
      recorderManager.value = uni.getRecorderManager()

      const options = {
        duration: 600000, // 10分钟最大录音时长
        sampleRate: AUDIO_CONFIG.sendSampleRate,
        numberOfChannels: AUDIO_CONFIG.channels,
        encodeBitRate: 48000,
        format: 'PCM',
        frameSize: AUDIO_CONFIG.chunkSize,
        // 尝试不同的实时回调配置
        enableFrameCallback: true,
        // 一些平台可能需要这些参数
        enableFrameRecorded: true,
        frameRecordedCallback: true
      }

      console.log('录音配置:', options)

      recorderManager.value.onStart(() => {
        isRecording.value = true
        console.log('开始录音')
      })

      // 🔥 关键：设置实时音频帧回调 - 这是实现实时语音交互的核心
      console.log('🔍 检查录音管理器支持的方法:')
      console.log('onFrameRecorded存在:', typeof recorderManager.value.onFrameRecorded)
      console.log('录音管理器所有方法:', Object.getOwnPropertyNames(recorderManager.value))

      // 尝试设置实时音频帧回调
      if (typeof recorderManager.value.onFrameRecorded === 'function') {
        recorderManager.value.onFrameRecorded((res) => {
          console.log('🎤 收到实时音频帧，大小:', res.frameBuffer?.byteLength || 'unknown')
          console.log('🎤 音频帧数据类型:', typeof res.frameBuffer)

          // 🔥 使用智能音频处理策略
          processAudioFrame(res.frameBuffer)
        })
        console.log(`✅ 已设置onFrameRecorded回调 - 发送模式: ${VAD_CONFIG.sendMode}`)
      } else {
        console.error('❌ onFrameRecorded方法不存在或不是函数')
        console.error('❌ 这意味着无法实现真正的实时语音交互')
        console.error('❌ 当前平台可能不支持实时音频帧获取')

        // 尝试其他可能的实时回调方法
        const alternativeMethods = ['onAudioFrame', 'onData', 'onFrame', 'onChunk']
        let foundAlternative = false

        for (const method of alternativeMethods) {
          if (typeof recorderManager.value[method] === 'function') {
            console.log(`✅ 找到替代方法: ${method}`)
            recorderManager.value[method]((res) => {
              console.log(`🎤 收到音频数据（${method}）:`, res)
              const audioData = res.data || res.frameBuffer || res.buffer || res
              sendAudioFrame(audioData)
            })
            foundAlternative = true
            break
          }
        }

        if (!foundAlternative) {
          console.error('❌ 无法找到任何实时音频回调方法')
          console.error('❌ 无法实现真正的实时语音交互，只能使用录音完成后处理')
        }
      }

      // 录音停止回调 - 仅用于清理，不处理音频数据
      recorderManager.value.onStop((res) => {
        console.log('🛑 录音已停止，文件路径:', res.tempFilePath)
        console.log('⚠️  注意：实时语音交互应该在录音过程中完成，而不是录音结束后')
        // 实时交互不需要处理录音完成后的文件
        // 所有音频数据应该已经通过onFrameRecorded实时发送了
      })

      recorderManager.value.onError((error) => {
        console.error('录音错误:', error)
        isRecording.value = false
      })

      recorderManager.value.start(options)

      // 添加一个定时器来测试录音状态
      setTimeout(() => {
        console.log('录音状态检查 - isRecording:', isRecording.value)
        console.log('录音管理器状态:', recorderManager.value)

        // 检查实时音频流是否正常工作
        if (isRecording.value) {
          if (typeof recorderManager.value.onFrameRecorded === 'function') {
            console.log('✅ 实时音频流已配置，请开始说话进行实时交互')
            console.log('🎤 您的语音将实时发送给AI，AI会立即回复')
          } else {
            console.error('❌ 实时音频流不可用')
            console.error('❌ 无法实现真正的实时语音交互')
            console.error('❌ 建议检查uni-app平台是否支持onFrameRecorded')

            // 发送一个测试消息来验证连接
            console.log('🧪 发送测试消息验证AI连接')
            sendTestMessage()
          }
        }
      }, 3000)

      return true
    } catch (error) {
      console.error('开始录音失败:', error)
      console.error('错误详情:', error.message)
      return false
    }
  }

  // 停止录音
  const stopRecording = () => {
    if (recorderManager.value && isRecording.value) {
      recorderManager.value.stop()
      isRecording.value = false
      console.log('停止录音')
    }
  }

  // 发送音频帧到Gemini Live API - 基于官方示例
  const sendAudioFrame = async (frameBuffer) => {
    if (!canSendMessage.value) {
      console.warn('无法发送音频帧：连接未建立或session不存在')
      return false
    }

    if (!frameBuffer) {
      console.warn('音频帧数据为空')
      return false
    }

    try {
      console.log('🎤 [实时] 准备发送音频帧，大小:', frameBuffer.byteLength || frameBuffer.length || 'unknown')

      // 将ArrayBuffer转换为Blob
      const audioBlob = new Blob([frameBuffer], { type: 'audio/pcm' })
      console.log('🎤 [实时] 音频Blob创建成功，大小:', audioBlob.size)

      // 检查session是否有sendRealtimeInput方法
      if (!session.value.sendRealtimeInput) {
        console.error('❌ session.sendRealtimeInput方法不存在，无法发送实时音频')
        return false
      }

      // 🔥 关键：使用Live API的sendRealtimeInput方法实时发送音频
      session.value.sendRealtimeInput({
        audio: audioBlob
      })

      console.log('✅ [实时] 音频帧发送成功！AI应该会实时处理并回复')
      return true
    } catch (error) {
      console.error('❌ [实时] 发送音频帧失败:', error)
      console.error('❌ 错误详情:', error.message)
      return false
    }
  }

  // 发送音频（实时流式传输）- 兼容旧接口
  const sendAudio = async (audioData) => {
    return sendAudioFrame(audioData)
  }

  // 切换音频发送模式
  const setAudioSendMode = (mode) => {
    if (['continuous', 'vad', 'manual'].includes(mode)) {
      VAD_CONFIG.sendMode = mode
      console.log(`🔄 音频发送模式已切换为: ${mode}`)

      // 清空缓冲区
      audioBuffer.value = []
      silenceCount.value = 0
      isUserSpeaking.value = false

      return true
    } else {
      console.error('❌ 无效的音频发送模式:', mode)
      return false
    }
  }

  // 手动发送缓冲的音频（仅在manual模式下使用）
  const manualSendAudio = () => {
    if (VAD_CONFIG.sendMode === 'manual') {
      console.log('📤 [手动模式] 手动发送缓冲的音频')
      sendBufferedAudio()
      return true
    } else {
      console.warn('⚠️  手动发送仅在manual模式下可用')
      return false
    }
  }

  // 发送测试消息（当实时音频不可用时）
  const sendTestMessage = async () => {
    if (!canSendMessage.value) {
      console.warn('无法发送测试消息：连接未建立')
      return false
    }

    try {
      console.log('发送测试消息给AI')

      // 使用Live API的sendClientContent方法发送文本
      if (session.value.sendClientContent) {
        session.value.sendClientContent({
          turns: [{
            role: 'user',
            parts: [{ text: '你好，我准备开始面试了，请问第一个问题是什么？' }]
          }],
          turnComplete: true
        })
        console.log('测试消息发送成功')
        return true
      } else {
        console.error('session.sendClientContent方法不存在')
        return false
      }
    } catch (error) {
      console.error('发送测试消息失败:', error)
      return false
    }
  }

  // 发送文本
  const sendText = async (text) => {
    if (!canSendMessage.value) return false

    try {
      const prompt = `你是一个专业的${currentDomain.value}面试官。请用自然、友好的语调回答以下问题或进行面试对话：${text}`
      const result = await session.value.generateContent(prompt)
      handleResponse(result)
      return true
    } catch (error) {
      console.error('发送文本失败:', error)
      return false
    }
  }

  // 发送打断信号 - 基于官方示例的中断处理
  const sendInterrupt = () => {
    try {
      // 停止当前语音播放
      stopPlaying()

      // 停止语音合成（兼容旧版本）
      uni.stopSpeechSynthesis && uni.stopSpeechSynthesis()

      // 清空音频输入队列，实现中断效果
      while (audioInQueue.value.length > 0) {
        audioInQueue.value.shift()
      }

      interviewStatus.value = 'active' // 系统只有实时模式，打断后回到active状态
      console.log('发送打断信号成功')
      return true
    } catch (error) {
      console.error('发送打断信号失败:', error)
      return false
    }
  }

  // 播放接收到的音频 - 基于官方示例的play_audio方法，适配uni-app
  const playReceivedAudio = async (audioData) => {
    try {
      if (!innerAudioContext.value) {
        innerAudioContext.value = uni.createInnerAudioContext()

        innerAudioContext.value.onEnded(() => {
          isPlaying.value = false
        })

        innerAudioContext.value.onError((error) => {
          console.error('音频播放失败:', error)
          isPlaying.value = false
        })
      }

      // 在uni-app中，需要将音频数据写入临时文件
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_received_audio_${Date.now()}.pcm`

      // 将ArrayBuffer转换为base64
      const base64Audio = uni.arrayBufferToBase64(audioData)

      uni.getFileSystemManager().writeFile({
        filePath: tempFilePath,
        data: base64Audio,
        encoding: 'base64',
        success: () => {
          innerAudioContext.value.src = tempFilePath
          innerAudioContext.value.play()
          isPlaying.value = true

          // 播放完成后清理临时文件
          innerAudioContext.value.onEnded(() => {
            isPlaying.value = false
            uni.getFileSystemManager().unlink({
              filePath: tempFilePath,
              success: () => console.log('临时接收音频文件已删除'),
              fail: (err) => console.warn('删除临时接收音频文件失败:', err)
            })
          })
        },
        fail: (err) => {
          console.error('写入接收音频文件失败:', err)
          isPlaying.value = false
        }
      })

    } catch (error) {
      console.error('播放接收音频失败:', error)
      isPlaying.value = false
    }
  }

  // 停止播放
  const stopPlaying = () => {
    if (innerAudioContext.value && isPlaying.value) {
      innerAudioContext.value.stop()
      isPlaying.value = false
      console.log('停止播放')
    }
  }

  // 播放音频 - 兼容旧接口
  const playAudio = (audioData) => {
    try {
      const tempFilePath = `${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.wav`

      uni.getFileSystemManager().writeFile({
        filePath: tempFilePath,
        data: audioData,
        encoding: 'base64',
        success: () => {
          const audioContext = uni.createInnerAudioContext()
          audioContext.src = tempFilePath
          audioContext.play()

          audioContext.onEnded(() => {
            uni.getFileSystemManager().unlink({
              filePath: tempFilePath,
              success: () => console.log('临时音频文件已删除'),
              fail: (err) => console.warn('删除临时音频文件失败:', err)
            })
            audioContext.destroy()
          })

          audioContext.onError((err) => {
            console.error('音频播放失败:', err)
            audioContext.destroy()
          })
        },
        fail: (err) => {
          console.error('写入音频文件失败:', err)
        }
      })
    } catch (error) {
      console.error('播放音频异常:', error)
    }
  }

  // 开始面试（系统只有实时模式）- 基于官方示例
  const startInterview = async () => {
    try {
      interviewStatus.value = 'active'

      // 开始录音，实现连续对话模式
      await startRecording()

      console.log('Gemini Store: 面试已开始（实时模式）')
      return true
    } catch (error) {
      console.error('开始面试失败:', error)
      interviewStatus.value = 'idle'
      return false
    }
  }

  // 结束面试
  const endInterview = () => {
    try {
      // 停止录音和播放
      stopRecording()
      stopPlaying()

      interviewStatus.value = 'idle'
      console.log('Gemini Store: 面试已结束')
      return true
    } catch (error) {
      console.error('结束面试失败:', error)
      return false
    }
  }

  // 重连机制
  const scheduleReconnect = () => {
    if (reconnectAttempts.value >= maxReconnectAttempts.value) {
      console.log('达到最大重连次数，停止重连')
      return
    }

    const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.value), 10000)
    
    setTimeout(() => {
      reconnectAttempts.value++
      connect().catch(err => {
        console.error('重连失败:', err)
      })
    }, delay)
  }

  return {
    // 状态
    session,
    isConnected,
    connectionStatus,
    lastMessage,
    interviewStatus,
    ephemeralToken,
    currentDomain,

    // 实时音频流状态
    audioInQueue,
    audioOutQueue,
    isRecording,
    isPlaying,

    // 音频配置
    AUDIO_CONFIG,
    LIVE_CONFIG,

    // 计算属性
    canSendMessage,

    // 方法
    setEphemeralToken,
    connect,
    disconnect,
    startRecording,
    stopRecording,
    sendAudio,
    sendAudioFrame,
    sendTestMessage,
    sendText,
    sendInterrupt,
    playReceivedAudio,
    stopPlaying,
    startInterview,
    endInterview,
    playAudio
  }
})