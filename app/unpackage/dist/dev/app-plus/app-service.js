if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...n){uni.__log__&&uni.__log__(e,t,...n)}const n=t=>(n,o=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,n,o)},o=n("onShow"),i=n("onHide"),s=n("onLaunch");function r(e,t,n){return Array.isArray(e)?(e.length=Math.max(e.length,t),e.splice(t,1,n),n):(e[t]=n,n)}function a(e,t){Array.isArray(e)?e.splice(t,1):delete e[t]}function l(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}const c="function"==typeof Proxy;let u,d,p;function m(){return void 0!==u||("undefined"!=typeof window&&window.performance?(u=!0,d=window.performance):"undefined"!=typeof global&&(null===(e=global.perf_hooks)||void 0===e?void 0:e.performance)?(u=!0,d=global.perf_hooks.performance):u=!1),u?d.now():Date.now();var e}class f{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const r in e.settings){const t=e.settings[r];n[r]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let i=Object.assign({},n);try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(i,t)}catch(s){}this.fallbacks={getSettings:()=>i,setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(s){}i=e},now:()=>m()},t&&t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function h(e,t){const n=e,o=l(),i=l().__VUE_DEVTOOLS_GLOBAL_HOOK__,s=c&&n.enableEarlyProxy;if(!i||!o.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&s){const e=s?new f(n,i):null;(o.__VUE_DEVTOOLS_PLUGINS__=o.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:n,setupFn:t,proxy:e}),e&&t(e.proxiedTarget)}else i.emit("devtools-plugin:setup",e,t)}
/*!
   * pinia v2.1.7
   * (c) 2023 Eduardo San Martin Morote
   * @license MIT
   */const g=e=>p=e,v=Symbol("pinia");function y(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var _,E;(E=_||(_={})).direct="direct",E.patchObject="patch object",E.patchFunction="patch function";const C="undefined"!=typeof window,b=C,T=(()=>"object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof global&&global.global===global?global:"object"==typeof globalThis?globalThis:{HTMLElement:null})();function S(e,t,n){const o=new XMLHttpRequest;o.open("GET",e),o.responseType="blob",o.onload=function(){N(o.response,t,n)},o.onerror=function(){},o.send()}function w(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(n){}return t.status>=200&&t.status<=299}function A(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(t){const n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(n)}}const I="object"==typeof navigator?navigator:{userAgent:""},O=(()=>/Macintosh/.test(I.userAgent)&&/AppleWebKit/.test(I.userAgent)&&!/Safari/.test(I.userAgent))(),N=C?"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype&&!O?function(e,t="download",n){const o=document.createElement("a");o.download=t,o.rel="noopener","string"==typeof e?(o.href=e,o.origin!==location.origin?w(o.href)?S(e,t,n):(o.target="_blank",A(o)):A(o)):(o.href=URL.createObjectURL(e),setTimeout((function(){URL.revokeObjectURL(o.href)}),4e4),setTimeout((function(){A(o)}),0))}:"msSaveOrOpenBlob"in I?function(e,t="download",n){if("string"==typeof e)if(w(e))S(e,t,n);else{const t=document.createElement("a");t.href=e,t.target="_blank",setTimeout((function(){A(t)}))}else navigator.msSaveOrOpenBlob(function(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}(e,n),t)}:function(e,t,n,o){(o=o||open("","_blank"))&&(o.document.title=o.document.body.innerText="downloading...");if("string"==typeof e)return S(e,t,n);const i="application/octet-stream"===e.type,s=/constructor/i.test(String(T.HTMLElement))||"safari"in T,r=/CriOS\/[\d]+/.test(navigator.userAgent);if((r||i&&s||O)&&"undefined"!=typeof FileReader){const t=new FileReader;t.onloadend=function(){let e=t.result;if("string"!=typeof e)throw o=null,new Error("Wrong reader.result type");e=r?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=e:location.assign(e),o=null},t.readAsDataURL(e)}else{const t=URL.createObjectURL(e);o?o.location.assign(t):location.href=t,o=null,setTimeout((function(){URL.revokeObjectURL(t)}),4e4)}}:()=>{};function x(e,t){"function"==typeof __VUE_DEVTOOLS_TOAST__&&__VUE_DEVTOOLS_TOAST__("🍍 "+e,t)}function k(e){return"_a"in e&&"install"in e}function P(){if(!("clipboard"in navigator))return x("Your browser doesn't support the Clipboard API","error"),!0}function R(e){return!!(e instanceof Error&&e.message.toLowerCase().includes("document is not focused"))&&(x('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0)}let M;async function D(e){try{const t=(M||(M=document.createElement("input"),M.type="file",M.accept=".json"),function(){return new Promise(((e,t)=>{M.onchange=async()=>{const t=M.files;if(!t)return e(null);const n=t.item(0);return e(n?{text:await n.text(),file:n}:null)},M.oncancel=()=>e(null),M.onerror=t,M.click()}))}),n=await t();if(!n)return;const{text:o,file:i}=n;V(e,JSON.parse(o)),x(`Global state imported from "${i.name}".`)}catch(t){x("Failed to import the state from JSON. Check the console for more details.","error")}}function V(e,t){for(const n in t){const o=e.state.value[n];o?Object.assign(o,t[n]):e.state.value[n]=t[n]}}function U(e){return{_custom:{display:e}}}const L="🍍 Pinia (root)",j="_root";function q(e){return k(e)?{id:j,label:L}:{id:e.$id,label:e.$id}}function F(e){return e?Array.isArray(e)?e.reduce(((e,t)=>(e.keys.push(t.key),e.operations.push(t.type),e.oldValue[t.key]=t.oldValue,e.newValue[t.key]=t.newValue,e)),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:U(e.type),key:U(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function B(e){switch(e){case _.direct:return"mutation";case _.patchFunction:case _.patchObject:return"$patch";default:return"unknown"}}let G=!0;const $=[],J="pinia:mutations",H="pinia",{assign:Z}=Object,Y=e=>"🍍 "+e;function K(t,n){h({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:$,app:t},(o=>{"function"!=typeof o.now&&x("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),o.addTimelineLayer({id:J,label:"Pinia 🍍",color:15064968}),o.addInspector({id:H,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{!async function(e){if(!P())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),x("Global state copied to clipboard.")}catch(t){if(R(t))return;x("Failed to serialize the state. Check the console for more details.","error")}}(n)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await async function(e){if(!P())try{V(e,JSON.parse(await navigator.clipboard.readText())),x("Global state pasted from clipboard.")}catch(t){if(R(t))return;x("Failed to deserialize the state from clipboard. Check the console for more details.","error")}}(n),o.sendInspectorTree(H),o.sendInspectorState(H)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{!async function(e){try{N(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){x("Failed to export the state as JSON. Check the console for more details.","error")}}(n)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await D(n),o.sendInspectorTree(H),o.sendInspectorState(H)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:e=>{const t=n._s.get(e);t?"function"!=typeof t.$reset?x(`Cannot reset "${e}" store because it doesn't have a "$reset" method implemented.`,"warn"):(t.$reset(),x(`Store "${e}" reset.`)):x(`Cannot reset "${e}" store because it wasn't found.`,"warn")}}]}),o.on.inspectComponent(((t,n)=>{const o=t.componentInstance&&t.componentInstance.proxy;if(o&&o._pStores){const n=t.componentInstance.proxy._pStores;Object.values(n).forEach((n=>{t.instanceData.state.push({type:Y(n.$id),key:"state",editable:!0,value:n._isOptionsAPI?{_custom:{value:e.toRaw(n.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>n.$reset()}]}}:Object.keys(n.$state).reduce(((e,t)=>(e[t]=n.$state[t],e)),{})}),n._getters&&n._getters.length&&t.instanceData.state.push({type:Y(n.$id),key:"getters",editable:!1,value:n._getters.reduce(((e,t)=>{try{e[t]=n[t]}catch(o){e[t]=o}return e}),{})})}))}})),o.on.getInspectorTree((e=>{if(e.app===t&&e.inspectorId===H){let t=[n];t=t.concat(Array.from(n._s.values())),e.rootNodes=(e.filter?t.filter((t=>"$id"in t?t.$id.toLowerCase().includes(e.filter.toLowerCase()):L.toLowerCase().includes(e.filter.toLowerCase()))):t).map(q)}})),o.on.getInspectorState((e=>{if(e.app===t&&e.inspectorId===H){const t=e.nodeId===j?n:n._s.get(e.nodeId);if(!t)return;t&&(e.state=function(e){if(k(e)){const t=Array.from(e._s.keys()),n=e._s;return{state:t.map((t=>({editable:!0,key:t,value:e.state.value[t]}))),getters:t.filter((e=>n.get(e)._getters)).map((e=>{const t=n.get(e);return{editable:!1,key:e,value:t._getters.reduce(((e,n)=>(e[n]=t[n],e)),{})}}))}}const t={state:Object.keys(e.$state).map((t=>({editable:!0,key:t,value:e.$state[t]})))};return e._getters&&e._getters.length&&(t.getters=e._getters.map((t=>({editable:!1,key:t,value:e[t]})))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map((t=>({editable:!0,key:t,value:e[t]})))),t}(t))}})),o.on.editInspectorState(((e,o)=>{if(e.app===t&&e.inspectorId===H){const t=e.nodeId===j?n:n._s.get(e.nodeId);if(!t)return x(`store "${e.nodeId}" not found`,"error");const{path:o}=e;k(t)?o.unshift("state"):1===o.length&&t._customProperties.has(o[0])&&!(o[0]in t.$state)||o.unshift("$state"),G=!1,e.set(t,o,e.state.value),G=!0}})),o.on.editComponentState((e=>{if(e.type.startsWith("🍍")){const t=e.type.replace(/^🍍\s*/,""),o=n._s.get(t);if(!o)return x(`store "${t}" not found`,"error");const{path:i}=e;if("state"!==i[0])return x(`Invalid path for store "${t}":\n${i}\nOnly state can be modified.`);i[0]="$state",G=!1,e.set(o,i,e.state.value),G=!0}}))}))}let W,z=0;function X(t,n,o){const i=n.reduce(((n,o)=>(n[o]=e.toRaw(t)[o],n)),{});for(const e in i)t[e]=function(){const n=z,s=o?new Proxy(t,{get:(...e)=>(W=n,Reflect.get(...e)),set:(...e)=>(W=n,Reflect.set(...e))}):t;W=n;const r=i[e].apply(s,arguments);return W=void 0,r}}function Q({app:t,store:n,options:o}){if(n.$id.startsWith("__hot:"))return;n._isOptionsAPI=!!o.state,X(n,Object.keys(o.actions),n._isOptionsAPI);const i=n._hotUpdate;e.toRaw(n)._hotUpdate=function(e){i.apply(this,arguments),X(n,Object.keys(e._hmrPayload.actions),!!n._isOptionsAPI)},function(t,n){$.includes(Y(n.$id))||$.push(Y(n.$id)),h({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:$,app:t,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},(t=>{const o="function"==typeof t.now?t.now.bind(t):Date.now;n.$onAction((({after:e,onError:i,name:s,args:r})=>{const a=z++;t.addTimelineEvent({layerId:J,event:{time:o(),title:"🛫 "+s,subtitle:"start",data:{store:U(n.$id),action:U(s),args:r},groupId:a}}),e((e=>{W=void 0,t.addTimelineEvent({layerId:J,event:{time:o(),title:"🛬 "+s,subtitle:"end",data:{store:U(n.$id),action:U(s),args:r,result:e},groupId:a}})})),i((e=>{W=void 0,t.addTimelineEvent({layerId:J,event:{time:o(),logType:"error",title:"💥 "+s,subtitle:"end",data:{store:U(n.$id),action:U(s),args:r,error:e},groupId:a}})}))}),!0),n._customProperties.forEach((i=>{e.watch((()=>e.unref(n[i])),((e,n)=>{t.notifyComponentUpdate(),t.sendInspectorState(H),G&&t.addTimelineEvent({layerId:J,event:{time:o(),title:"Change",subtitle:i,data:{newValue:e,oldValue:n},groupId:W}})}),{deep:!0})})),n.$subscribe((({events:e,type:i},s)=>{if(t.notifyComponentUpdate(),t.sendInspectorState(H),!G)return;const r={time:o(),title:B(i),data:Z({store:U(n.$id)},F(e)),groupId:W};i===_.patchFunction?r.subtitle="⤵️":i===_.patchObject?r.subtitle="🧩":e&&!Array.isArray(e)&&(r.subtitle=e.type),e&&(r.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:e}}),t.addTimelineEvent({layerId:J,event:r})}),{detached:!0,flush:"sync"});const i=n._hotUpdate;n._hotUpdate=e.markRaw((e=>{i(e),t.addTimelineEvent({layerId:J,event:{time:o(),title:"🔥 "+n.$id,subtitle:"HMR update",data:{store:U(n.$id),info:U("HMR update")}}}),t.notifyComponentUpdate(),t.sendInspectorTree(H),t.sendInspectorState(H)}));const{$dispose:s}=n;n.$dispose=()=>{s(),t.notifyComponentUpdate(),t.sendInspectorTree(H),t.sendInspectorState(H),t.getSettings().logStoreChanges&&x(`Disposed "${n.$id}" store 🗑`)},t.notifyComponentUpdate(),t.sendInspectorTree(H),t.sendInspectorState(H),t.getSettings().logStoreChanges&&x(`"${n.$id}" store installed 🆕`)}))}(t,n)}function ee(t,n){for(const o in n){const i=n[o];if(!(o in t))continue;const s=t[o];y(s)&&y(i)&&!e.isRef(i)&&!e.isReactive(i)?t[o]=ee(s,i):t[o]=i}return t}const te=()=>{};function ne(t,n,o,i=te){t.push(n);const s=()=>{const e=t.indexOf(n);e>-1&&(t.splice(e,1),i())};return!o&&e.getCurrentScope()&&e.onScopeDispose(s),s}function oe(e,...t){e.slice().forEach((e=>{e(...t)}))}const ie=e=>e();function se(t,n){t instanceof Map&&n instanceof Map&&n.forEach(((e,n)=>t.set(n,e))),t instanceof Set&&n instanceof Set&&n.forEach(t.add,t);for(const o in n){if(!n.hasOwnProperty(o))continue;const i=n[o],s=t[o];y(s)&&y(i)&&t.hasOwnProperty(o)&&!e.isRef(i)&&!e.isReactive(i)?t[o]=se(s,i):t[o]=i}return t}const re=Symbol("pinia:skipHydration");const{assign:ae}=Object;function le(t){return!(!e.isRef(t)||!t.effect)}function ce(t,n,o,i){const{state:s,actions:r,getters:a}=n,l=o.state.value[t];let c;return c=ue(t,(function(){l||i||(o.state.value[t]=s?s():{});const n=i?e.toRefs(e.ref(s?s():{}).value):e.toRefs(o.state.value[t]);return ae(n,r,Object.keys(a||{}).reduce(((n,i)=>(n[i]=e.markRaw(e.computed((()=>{g(o);const e=o._s.get(t);return a[i].call(e,e)}))),n)),{}))}),n,o,i,!0),c}function ue(t,n,o={},i,s,l){let c;const u=ae({actions:{}},o);if(!i._e.active)throw new Error("Pinia destroyed");const d={deep:!0};let p,m;d.onTrigger=e=>{p?f=e:0!=p||x._hotUpdating||Array.isArray(f)&&f.push(e)};let f,h=[],v=[];const E=i.state.value[t];l||E||s||(i.state.value[t]={});const T=e.ref({});let S;function w(n){let o;p=m=!1,f=[],"function"==typeof n?(n(i.state.value[t]),o={type:_.patchFunction,storeId:t,events:f}):(se(i.state.value[t],n),o={type:_.patchObject,payload:n,storeId:t,events:f});const s=S=Symbol();e.nextTick().then((()=>{S===s&&(p=!0)})),m=!0,oe(h,o,i.state.value[t])}const A=l?function(){const{state:e}=o,t=e?e():{};this.$patch((e=>{ae(e,t)}))}:()=>{throw new Error(`🍍: Store "${t}" is built using the setup syntax and does not implement $reset().`)};function I(e,n){return function(){g(i);const o=Array.from(arguments),s=[],r=[];function a(e){s.push(e)}function l(e){r.push(e)}let c;oe(v,{args:o,name:e,store:x,after:a,onError:l});try{c=n.apply(this&&this.$id===t?this:x,o)}catch(u){throw oe(r,u),u}return c instanceof Promise?c.then((e=>(oe(s,e),e))).catch((e=>(oe(r,e),Promise.reject(e)))):(oe(s,c),c)}}const O=e.markRaw({actions:{},getters:{},state:[],hotState:T}),N={_p:i,$id:t,$onAction:ne.bind(null,v),$patch:w,$reset:A,$subscribe(n,o={}){const s=ne(h,n,o.detached,(()=>r())),r=c.run((()=>e.watch((()=>i.state.value[t]),(e=>{("sync"===o.flush?m:p)&&n({storeId:t,type:_.direct,events:f},e)}),ae({},d,o))));return s},$dispose:function(){c.stop(),h=[],v=[],i._s.delete(t)}},x=e.reactive(ae({_hmrPayload:O,_customProperties:e.markRaw(new Set)},N));i._s.set(t,x);const k=(i._a&&i._a.runWithContext||ie)((()=>i._e.run((()=>(c=e.effectScope()).run(n)))));for(const a in k){const n=k[a];if(e.isRef(n)&&!le(n)||e.isReactive(n))s?r(T.value,a,e.toRef(k,a)):l||(!E||y(P=n)&&P.hasOwnProperty(re)||(e.isRef(n)?n.value=E[a]:se(n,E[a])),i.state.value[t][a]=n),O.state.push(a);else if("function"==typeof n){const e=s?n:I(a,n);k[a]=e,O.actions[a]=n,u.actions[a]=n}else if(le(n)&&(O.getters[a]=l?o.getters[a]:n,C)){(k._getters||(k._getters=e.markRaw([]))).push(a)}}var P;if(ae(x,k),ae(e.toRaw(x),k),Object.defineProperty(x,"$state",{get:()=>s?T.value:i.state.value[t],set:e=>{if(s)throw new Error("cannot set hotState");w((t=>{ae(t,e)}))}}),x._hotUpdate=e.markRaw((n=>{x._hotUpdating=!0,n._hmrPayload.state.forEach((t=>{if(t in x.$state){const e=n.$state[t],o=x.$state[t];"object"==typeof e&&y(e)&&y(o)?ee(e,o):n.$state[t]=o}r(x,t,e.toRef(n.$state,t))})),Object.keys(x.$state).forEach((e=>{e in n.$state||a(x,e)})),p=!1,m=!1,i.state.value[t]=e.toRef(n._hmrPayload,"hotState"),m=!0,e.nextTick().then((()=>{p=!0}));for(const e in n._hmrPayload.actions){const t=n[e];r(x,e,I(e,t))}for(const t in n._hmrPayload.getters){const o=n._hmrPayload.getters[t],s=l?e.computed((()=>(g(i),o.call(x,x)))):o;r(x,t,s)}Object.keys(x._hmrPayload.getters).forEach((e=>{e in n._hmrPayload.getters||a(x,e)})),Object.keys(x._hmrPayload.actions).forEach((e=>{e in n._hmrPayload.actions||a(x,e)})),x._hmrPayload=n._hmrPayload,x._getters=n._getters,x._hotUpdating=!1})),b){const e={writable:!0,configurable:!0,enumerable:!1};["_p","_hmrPayload","_getters","_customProperties"].forEach((t=>{Object.defineProperty(x,t,ae({value:x[t]},e))}))}return i._p.forEach((e=>{if(b){const t=c.run((()=>e({store:x,app:i._a,pinia:i,options:u})));Object.keys(t||{}).forEach((e=>x._customProperties.add(e))),ae(x,t)}else ae(x,c.run((()=>e({store:x,app:i._a,pinia:i,options:u}))))})),x.$state&&"object"==typeof x.$state&&"function"==typeof x.$state.constructor&&x.$state.constructor.toString().includes("[native code]"),E&&l&&o.hydrate&&o.hydrate(x.$state,E),p=!0,m=!0,x}function de(t,n,o){let i,s;const r="function"==typeof n;if("string"==typeof t)i=t,s=r?o:n;else if(s=t,i=t.id,"string"!=typeof i)throw new Error('[🍍]: "defineStore()" must be passed a store id as its first argument.');function a(t,o){const l=e.hasInjectionContext();if((t=t||(l?e.inject(v,null):null))&&g(t),!p)throw new Error('[🍍]: "getActivePinia()" was called but there was no active Pinia. Are you trying to use a store before calling "app.use(pinia)"?\nSee https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\nThis will fail in production.');(t=p)._s.has(i)||(r?ue(i,n,s,t):ce(i,s,t),a._pinia=t);const c=t._s.get(i);if(o){const e="__hot:"+i,a=r?ue(e,n,s,t,!0):ce(e,ae({},s),t,!0);o._hotUpdate(a),delete t.state.value[e],t._s.delete(e)}if(C){const t=e.getCurrentInstance();if(t&&t.proxy&&!o){const e=t.proxy;("_pStores"in e?e._pStores:e._pStores={})[i]=c}}return c}return a.$id=i,a}const pe="http://192.168.101.18:8090";var me,fe,he;(fe=me||(me={})).assertEqual=e=>{},fe.assertIs=function(e){},fe.assertNever=function(e){throw new Error},fe.arrayToEnum=e=>{const t={};for(const n of e)t[n]=n;return t},fe.getValidEnumValues=e=>{const t=fe.objectKeys(e).filter((t=>"number"!=typeof e[e[t]])),n={};for(const o of t)n[o]=e[o];return fe.objectValues(n)},fe.objectValues=e=>fe.objectKeys(e).map((function(t){return e[t]})),fe.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{const t=[];for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&t.push(n);return t},fe.find=(e,t)=>{for(const n of e)if(t(n))return n},fe.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&Number.isFinite(e)&&Math.floor(e)===e,fe.joinValues=function(e,t=" | "){return e.map((e=>"string"==typeof e?`'${e}'`:e)).join(t)},fe.jsonStringifyReplacer=(e,t)=>"bigint"==typeof t?t.toString():t,(he||(he={})).mergeShapes=(e,t)=>({...e,...t});const ge=me.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),ve=e=>{switch(typeof e){case"undefined":return ge.undefined;case"string":return ge.string;case"number":return Number.isNaN(e)?ge.nan:ge.number;case"boolean":return ge.boolean;case"function":return ge.function;case"bigint":return ge.bigint;case"symbol":return ge.symbol;case"object":return Array.isArray(e)?ge.array:null===e?ge.null:e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch?ge.promise:"undefined"!=typeof Map&&e instanceof Map?ge.map:"undefined"!=typeof Set&&e instanceof Set?ge.set:"undefined"!=typeof Date&&e instanceof Date?ge.date:ge.object;default:return ge.unknown}},ye=me.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class _e extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};const t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}format(e){const t=e||function(e){return e.message},n={_errors:[]},o=e=>{for(const i of e.issues)if("invalid_union"===i.code)i.unionErrors.map(o);else if("invalid_return_type"===i.code)o(i.returnTypeError);else if("invalid_arguments"===i.code)o(i.argumentsError);else if(0===i.path.length)n._errors.push(t(i));else{let e=n,o=0;for(;o<i.path.length;){const n=i.path[o];o===i.path.length-1?(e[n]=e[n]||{_errors:[]},e[n]._errors.push(t(i))):e[n]=e[n]||{_errors:[]},e=e[n],o++}}};return o(this),n}static assert(e){if(!(e instanceof _e))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,me.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=(e=>e.message)){const t={},n=[];for(const o of this.issues)if(o.path.length>0){const n=o.path[0];t[n]=t[n]||[],t[n].push(e(o))}else n.push(e(o));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}}_e.create=e=>new _e(e);const Ee=(e,t)=>{let n;switch(e.code){case ye.invalid_type:n=e.received===ge.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case ye.invalid_literal:n=`Invalid literal value, expected ${JSON.stringify(e.expected,me.jsonStringifyReplacer)}`;break;case ye.unrecognized_keys:n=`Unrecognized key(s) in object: ${me.joinValues(e.keys,", ")}`;break;case ye.invalid_union:n="Invalid input";break;case ye.invalid_union_discriminator:n=`Invalid discriminator value. Expected ${me.joinValues(e.options)}`;break;case ye.invalid_enum_value:n=`Invalid enum value. Expected ${me.joinValues(e.options)}, received '${e.received}'`;break;case ye.invalid_arguments:n="Invalid function arguments";break;case ye.invalid_return_type:n="Invalid function return type";break;case ye.invalid_date:n="Invalid date";break;case ye.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(n=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(n=`${n} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?n=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?n=`Invalid input: must end with "${e.validation.endsWith}"`:me.assertNever(e.validation):n="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case ye.too_small:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type||"bigint"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case ye.too_big:n="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case ye.custom:n="Invalid input";break;case ye.invalid_intersection_types:n="Intersection results could not be merged";break;case ye.not_multiple_of:n=`Number must be a multiple of ${e.multipleOf}`;break;case ye.not_finite:n="Number must be finite";break;default:n=t.defaultError,me.assertNever(e)}return{message:n}};let Ce=Ee;function be(e,t){const n=Ce,o=(e=>{const{data:t,path:n,errorMaps:o,issueData:i}=e,s=[...n,...i.path||[]],r={...i,path:s};if(void 0!==i.message)return{...i,path:s,message:i.message};let a="";const l=o.filter((e=>!!e)).slice().reverse();for(const c of l)a=c(r,{data:t,defaultError:a}).message;return{...i,path:s,message:a}})({issueData:t,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,n,n===Ee?void 0:Ee].filter((e=>!!e))});e.common.issues.push(o)}class Te{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,t){const n=[];for(const o of t){if("aborted"===o.status)return Se;"dirty"===o.status&&e.dirty(),n.push(o.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){const n=[];for(const o of t){const e=await o.key,t=await o.value;n.push({key:e,value:t})}return Te.mergeObjectSync(e,n)}static mergeObjectSync(e,t){const n={};for(const o of t){const{key:t,value:i}=o;if("aborted"===t.status)return Se;if("aborted"===i.status)return Se;"dirty"===t.status&&e.dirty(),"dirty"===i.status&&e.dirty(),"__proto__"===t.value||void 0===i.value&&!o.alwaysSet||(n[t.value]=i.value)}return{status:e.value,value:n}}}const Se=Object.freeze({status:"aborted"}),we=e=>({status:"dirty",value:e}),Ae=e=>({status:"valid",value:e}),Ie=e=>"aborted"===e.status,Oe=e=>"dirty"===e.status,Ne=e=>"valid"===e.status,xe=e=>"undefined"!=typeof Promise&&e instanceof Promise;var ke,Pe;(Pe=ke||(ke={})).errToObj=e=>"string"==typeof e?{message:e}:e||{},Pe.toString=e=>"string"==typeof e?e:null==e?void 0:e.message;class Re{constructor(e,t,n,o){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=o}get path(){return this._cachedPath.length||(Array.isArray(this._key)?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}const Me=(e,t)=>{if(Ne(t))return{success:!0,data:t.value};if(!e.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;const t=new _e(e.common.issues);return this._error=t,this._error}}};function De(e){if(!e)return{};const{errorMap:t,invalid_type_error:n,required_error:o,description:i}=e;if(t&&(n||o))throw new Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');if(t)return{errorMap:t,description:i};return{errorMap:(t,i)=>{const{message:s}=e;return"invalid_enum_value"===t.code?{message:s??i.defaultError}:void 0===i.data?{message:s??o??i.defaultError}:"invalid_type"!==t.code?{message:i.defaultError}:{message:s??n??i.defaultError}},description:i}}class Ve{get description(){return this._def.description}_getType(e){return ve(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:ve(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new Te,ctx:{common:e.parent.common,data:e.data,parsedType:ve(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){const t=this._parse(e);if(xe(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){const t=this._parse(e);return Promise.resolve(t)}parse(e,t){const n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){const n={common:{issues:[],async:(null==t?void 0:t.async)??!1,contextualErrorMap:null==t?void 0:t.errorMap},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ve(e)},o=this._parseSync({data:e,path:n.path,parent:n});return Me(n,o)}"~validate"(e){var t,n;const o={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ve(e)};if(!this["~standard"].async)try{const t=this._parseSync({data:e,path:[],parent:o});return Ne(t)?{value:t.value}:{issues:o.common.issues}}catch(i){(null==(n=null==(t=null==i?void 0:i.message)?void 0:t.toLowerCase())?void 0:n.includes("encountered"))&&(this["~standard"].async=!0),o.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:o}).then((e=>Ne(e)?{value:e.value}:{issues:o.common.issues}))}async parseAsync(e,t){const n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){const n={common:{issues:[],contextualErrorMap:null==t?void 0:t.errorMap,async:!0},path:(null==t?void 0:t.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:ve(e)},o=this._parse({data:e,path:n.path,parent:n}),i=await(xe(o)?o:Promise.resolve(o));return Me(n,i)}refine(e,t){const n=e=>"string"==typeof t||void 0===t?{message:t}:"function"==typeof t?t(e):t;return this._refinement(((t,o)=>{const i=e(t),s=()=>o.addIssue({code:ye.custom,...n(t)});return"undefined"!=typeof Promise&&i instanceof Promise?i.then((e=>!!e||(s(),!1))):!!i||(s(),!1)}))}refinement(e,t){return this._refinement(((n,o)=>!!e(n)||(o.addIssue("function"==typeof t?t(n,o):t),!1)))}_refinement(e){return new Rt({schema:this,typeName:Bt.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return Mt.create(this,this._def)}nullable(){return Dt.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return vt.create(this)}promise(){return Pt.create(this,this._def)}or(e){return Et.create([this,e],this._def)}and(e){return bt.create(this,e,this._def)}transform(e){return new Rt({...De(this._def),schema:this,typeName:Bt.ZodEffects,effect:{type:"transform",transform:e}})}default(e){const t="function"==typeof e?e:()=>e;return new Vt({...De(this._def),innerType:this,defaultValue:t,typeName:Bt.ZodDefault})}brand(){return new jt({typeName:Bt.ZodBranded,type:this,...De(this._def)})}catch(e){const t="function"==typeof e?e:()=>e;return new Ut({...De(this._def),innerType:this,catchValue:t,typeName:Bt.ZodCatch})}describe(e){return new(0,this.constructor)({...this._def,description:e})}pipe(e){return qt.create(this,e)}readonly(){return Ft.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}const Ue=/^c[^\s-]{8,}$/i,Le=/^[0-9a-z]+$/,je=/^[0-9A-HJKMNP-TV-Z]{26}$/i,qe=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Fe=/^[a-z0-9_-]{21}$/i,Be=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,Ge=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,$e=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i;let Je;const He=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ze=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,Ye=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,Ke=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,We=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,ze=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Xe="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",Qe=new RegExp(`^${Xe}$`);function et(e){let t="[0-5]\\d";e.precision?t=`${t}\\.\\d{${e.precision}}`:null==e.precision&&(t=`${t}(\\.\\d+)?`);return`([01]\\d|2[0-3]):[0-5]\\d(:${t})${e.precision?"+":"?"}`}function tt(e){let t=`${Xe}T${et(e)}`;const n=[];return n.push(e.local?"Z?":"Z"),e.offset&&n.push("([+-]\\d{2}:?\\d{2})"),t=`${t}(${n.join("|")})`,new RegExp(`^${t}$`)}function nt(e,t){if(!Be.test(e))return!1;try{const[n]=e.split(".");if(!n)return!1;const o=n.replace(/-/g,"+").replace(/_/g,"/").padEnd(n.length+(4-n.length%4)%4,"="),i=JSON.parse(atob(o));return"object"==typeof i&&null!==i&&((!("typ"in i)||"JWT"===(null==i?void 0:i.typ))&&(!!i.alg&&(!t||i.alg===t)))}catch{return!1}}function ot(e,t){return!("v4"!==t&&t||!Ze.test(e))||!("v6"!==t&&t||!Ke.test(e))}class it extends Ve{_parse(e){this._def.coerce&&(e.data=String(e.data));if(this._getType(e)!==ge.string){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.string,received:t.parsedType}),Se}const t=new Te;let n;for(const s of this._def.checks)if("min"===s.kind)e.data.length<s.value&&(n=this._getOrReturnCtx(e,n),be(n,{code:ye.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),t.dirty());else if("max"===s.kind)e.data.length>s.value&&(n=this._getOrReturnCtx(e,n),be(n,{code:ye.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),t.dirty());else if("length"===s.kind){const o=e.data.length>s.value,i=e.data.length<s.value;(o||i)&&(n=this._getOrReturnCtx(e,n),o?be(n,{code:ye.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):i&&be(n,{code:ye.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),t.dirty())}else if("email"===s.kind)$e.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"email",code:ye.invalid_string,message:s.message}),t.dirty());else if("emoji"===s.kind)Je||(Je=new RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),Je.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"emoji",code:ye.invalid_string,message:s.message}),t.dirty());else if("uuid"===s.kind)qe.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"uuid",code:ye.invalid_string,message:s.message}),t.dirty());else if("nanoid"===s.kind)Fe.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"nanoid",code:ye.invalid_string,message:s.message}),t.dirty());else if("cuid"===s.kind)Ue.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"cuid",code:ye.invalid_string,message:s.message}),t.dirty());else if("cuid2"===s.kind)Le.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"cuid2",code:ye.invalid_string,message:s.message}),t.dirty());else if("ulid"===s.kind)je.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"ulid",code:ye.invalid_string,message:s.message}),t.dirty());else if("url"===s.kind)try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),be(n,{validation:"url",code:ye.invalid_string,message:s.message}),t.dirty()}else if("regex"===s.kind){s.regex.lastIndex=0;s.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"regex",code:ye.invalid_string,message:s.message}),t.dirty())}else if("trim"===s.kind)e.data=e.data.trim();else if("includes"===s.kind)e.data.includes(s.value,s.position)||(n=this._getOrReturnCtx(e,n),be(n,{code:ye.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),t.dirty());else if("toLowerCase"===s.kind)e.data=e.data.toLowerCase();else if("toUpperCase"===s.kind)e.data=e.data.toUpperCase();else if("startsWith"===s.kind)e.data.startsWith(s.value)||(n=this._getOrReturnCtx(e,n),be(n,{code:ye.invalid_string,validation:{startsWith:s.value},message:s.message}),t.dirty());else if("endsWith"===s.kind)e.data.endsWith(s.value)||(n=this._getOrReturnCtx(e,n),be(n,{code:ye.invalid_string,validation:{endsWith:s.value},message:s.message}),t.dirty());else if("datetime"===s.kind){tt(s).test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{code:ye.invalid_string,validation:"datetime",message:s.message}),t.dirty())}else if("date"===s.kind){Qe.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{code:ye.invalid_string,validation:"date",message:s.message}),t.dirty())}else if("time"===s.kind){new RegExp(`^${et(s)}$`).test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{code:ye.invalid_string,validation:"time",message:s.message}),t.dirty())}else"duration"===s.kind?Ge.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"duration",code:ye.invalid_string,message:s.message}),t.dirty()):"ip"===s.kind?(o=e.data,("v4"!==(i=s.version)&&i||!He.test(o))&&("v6"!==i&&i||!Ye.test(o))&&(n=this._getOrReturnCtx(e,n),be(n,{validation:"ip",code:ye.invalid_string,message:s.message}),t.dirty())):"jwt"===s.kind?nt(e.data,s.alg)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"jwt",code:ye.invalid_string,message:s.message}),t.dirty()):"cidr"===s.kind?ot(e.data,s.version)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"cidr",code:ye.invalid_string,message:s.message}),t.dirty()):"base64"===s.kind?We.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"base64",code:ye.invalid_string,message:s.message}),t.dirty()):"base64url"===s.kind?ze.test(e.data)||(n=this._getOrReturnCtx(e,n),be(n,{validation:"base64url",code:ye.invalid_string,message:s.message}),t.dirty()):me.assertNever(s);var o,i;return{status:t.value,value:e.data}}_regex(e,t,n){return this.refinement((t=>e.test(t)),{validation:t,code:ye.invalid_string,...ke.errToObj(n)})}_addCheck(e){return new it({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...ke.errToObj(e)})}url(e){return this._addCheck({kind:"url",...ke.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...ke.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...ke.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...ke.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...ke.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...ke.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...ke.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...ke.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...ke.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...ke.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...ke.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...ke.errToObj(e)})}datetime(e){return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:(null==e?void 0:e.offset)??!1,local:(null==e?void 0:e.local)??!1,...ke.errToObj(null==e?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,...ke.errToObj(null==e?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...ke.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...ke.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:null==t?void 0:t.position,...ke.errToObj(null==t?void 0:t.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...ke.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...ke.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...ke.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...ke.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...ke.errToObj(t)})}nonempty(e){return this.min(1,ke.errToObj(e))}trim(){return new it({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new it({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new it({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find((e=>"datetime"===e.kind))}get isDate(){return!!this._def.checks.find((e=>"date"===e.kind))}get isTime(){return!!this._def.checks.find((e=>"time"===e.kind))}get isDuration(){return!!this._def.checks.find((e=>"duration"===e.kind))}get isEmail(){return!!this._def.checks.find((e=>"email"===e.kind))}get isURL(){return!!this._def.checks.find((e=>"url"===e.kind))}get isEmoji(){return!!this._def.checks.find((e=>"emoji"===e.kind))}get isUUID(){return!!this._def.checks.find((e=>"uuid"===e.kind))}get isNANOID(){return!!this._def.checks.find((e=>"nanoid"===e.kind))}get isCUID(){return!!this._def.checks.find((e=>"cuid"===e.kind))}get isCUID2(){return!!this._def.checks.find((e=>"cuid2"===e.kind))}get isULID(){return!!this._def.checks.find((e=>"ulid"===e.kind))}get isIP(){return!!this._def.checks.find((e=>"ip"===e.kind))}get isCIDR(){return!!this._def.checks.find((e=>"cidr"===e.kind))}get isBase64(){return!!this._def.checks.find((e=>"base64"===e.kind))}get isBase64url(){return!!this._def.checks.find((e=>"base64url"===e.kind))}get minLength(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}function st(e,t){const n=(e.toString().split(".")[1]||"").length,o=(t.toString().split(".")[1]||"").length,i=n>o?n:o;return Number.parseInt(e.toFixed(i).replace(".",""))%Number.parseInt(t.toFixed(i).replace(".",""))/10**i}it.create=e=>new it({checks:[],typeName:Bt.ZodString,coerce:(null==e?void 0:e.coerce)??!1,...De(e)});class rt extends Ve{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){this._def.coerce&&(e.data=Number(e.data));if(this._getType(e)!==ge.number){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.number,received:t.parsedType}),Se}let t;const n=new Te;for(const o of this._def.checks)if("int"===o.kind)me.isInteger(e.data)||(t=this._getOrReturnCtx(e,t),be(t,{code:ye.invalid_type,expected:"integer",received:"float",message:o.message}),n.dirty());else if("min"===o.kind){(o.inclusive?e.data<o.value:e.data<=o.value)&&(t=this._getOrReturnCtx(e,t),be(t,{code:ye.too_small,minimum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),n.dirty())}else if("max"===o.kind){(o.inclusive?e.data>o.value:e.data>=o.value)&&(t=this._getOrReturnCtx(e,t),be(t,{code:ye.too_big,maximum:o.value,type:"number",inclusive:o.inclusive,exact:!1,message:o.message}),n.dirty())}else"multipleOf"===o.kind?0!==st(e.data,o.value)&&(t=this._getOrReturnCtx(e,t),be(t,{code:ye.not_multiple_of,multipleOf:o.value,message:o.message}),n.dirty()):"finite"===o.kind?Number.isFinite(e.data)||(t=this._getOrReturnCtx(e,t),be(t,{code:ye.not_finite,message:o.message}),n.dirty()):me.assertNever(o);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,ke.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ke.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ke.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ke.toString(t))}setLimit(e,t,n,o){return new rt({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:ke.toString(o)}]})}_addCheck(e){return new rt({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:ke.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:ke.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:ke.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:ke.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:ke.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ke.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:ke.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:ke.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:ke.toString(e)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find((e=>"int"===e.kind||"multipleOf"===e.kind&&me.isInteger(e.value)))}get isFinite(){let e=null,t=null;for(const n of this._def.checks){if("finite"===n.kind||"int"===n.kind||"multipleOf"===n.kind)return!0;"min"===n.kind?(null===t||n.value>t)&&(t=n.value):"max"===n.kind&&(null===e||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}}rt.create=e=>new rt({checks:[],typeName:Bt.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...De(e)});class at extends Ve{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce)try{e.data=BigInt(e.data)}catch{return this._getInvalidInput(e)}if(this._getType(e)!==ge.bigint)return this._getInvalidInput(e);let t;const n=new Te;for(const o of this._def.checks)if("min"===o.kind){(o.inclusive?e.data<o.value:e.data<=o.value)&&(t=this._getOrReturnCtx(e,t),be(t,{code:ye.too_small,type:"bigint",minimum:o.value,inclusive:o.inclusive,message:o.message}),n.dirty())}else if("max"===o.kind){(o.inclusive?e.data>o.value:e.data>=o.value)&&(t=this._getOrReturnCtx(e,t),be(t,{code:ye.too_big,type:"bigint",maximum:o.value,inclusive:o.inclusive,message:o.message}),n.dirty())}else"multipleOf"===o.kind?e.data%o.value!==BigInt(0)&&(t=this._getOrReturnCtx(e,t),be(t,{code:ye.not_multiple_of,multipleOf:o.value,message:o.message}),n.dirty()):me.assertNever(o);return{status:n.value,value:e.data}}_getInvalidInput(e){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.bigint,received:t.parsedType}),Se}gte(e,t){return this.setLimit("min",e,!0,ke.toString(t))}gt(e,t){return this.setLimit("min",e,!1,ke.toString(t))}lte(e,t){return this.setLimit("max",e,!0,ke.toString(t))}lt(e,t){return this.setLimit("max",e,!1,ke.toString(t))}setLimit(e,t,n,o){return new at({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:ke.toString(o)}]})}_addCheck(e){return new at({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:ke.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:ke.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:ke.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:ke.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:ke.toString(t)})}get minValue(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return e}}at.create=e=>new at({checks:[],typeName:Bt.ZodBigInt,coerce:(null==e?void 0:e.coerce)??!1,...De(e)});class lt extends Ve{_parse(e){this._def.coerce&&(e.data=Boolean(e.data));if(this._getType(e)!==ge.boolean){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.boolean,received:t.parsedType}),Se}return Ae(e.data)}}lt.create=e=>new lt({typeName:Bt.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...De(e)});class ct extends Ve{_parse(e){this._def.coerce&&(e.data=new Date(e.data));if(this._getType(e)!==ge.date){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.date,received:t.parsedType}),Se}if(Number.isNaN(e.data.getTime())){return be(this._getOrReturnCtx(e),{code:ye.invalid_date}),Se}const t=new Te;let n;for(const o of this._def.checks)"min"===o.kind?e.data.getTime()<o.value&&(n=this._getOrReturnCtx(e,n),be(n,{code:ye.too_small,message:o.message,inclusive:!0,exact:!1,minimum:o.value,type:"date"}),t.dirty()):"max"===o.kind?e.data.getTime()>o.value&&(n=this._getOrReturnCtx(e,n),be(n,{code:ye.too_big,message:o.message,inclusive:!0,exact:!1,maximum:o.value,type:"date"}),t.dirty()):me.assertNever(o);return{status:t.value,value:new Date(e.data.getTime())}}_addCheck(e){return new ct({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:ke.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:ke.toString(t)})}get minDate(){let e=null;for(const t of this._def.checks)"min"===t.kind&&(null===e||t.value>e)&&(e=t.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(const t of this._def.checks)"max"===t.kind&&(null===e||t.value<e)&&(e=t.value);return null!=e?new Date(e):null}}ct.create=e=>new ct({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:Bt.ZodDate,...De(e)});class ut extends Ve{_parse(e){if(this._getType(e)!==ge.symbol){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.symbol,received:t.parsedType}),Se}return Ae(e.data)}}ut.create=e=>new ut({typeName:Bt.ZodSymbol,...De(e)});class dt extends Ve{_parse(e){if(this._getType(e)!==ge.undefined){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.undefined,received:t.parsedType}),Se}return Ae(e.data)}}dt.create=e=>new dt({typeName:Bt.ZodUndefined,...De(e)});class pt extends Ve{_parse(e){if(this._getType(e)!==ge.null){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.null,received:t.parsedType}),Se}return Ae(e.data)}}pt.create=e=>new pt({typeName:Bt.ZodNull,...De(e)});class mt extends Ve{constructor(){super(...arguments),this._any=!0}_parse(e){return Ae(e.data)}}mt.create=e=>new mt({typeName:Bt.ZodAny,...De(e)});class ft extends Ve{constructor(){super(...arguments),this._unknown=!0}_parse(e){return Ae(e.data)}}ft.create=e=>new ft({typeName:Bt.ZodUnknown,...De(e)});class ht extends Ve{_parse(e){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.never,received:t.parsedType}),Se}}ht.create=e=>new ht({typeName:Bt.ZodNever,...De(e)});class gt extends Ve{_parse(e){if(this._getType(e)!==ge.undefined){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.void,received:t.parsedType}),Se}return Ae(e.data)}}gt.create=e=>new gt({typeName:Bt.ZodVoid,...De(e)});class vt extends Ve{_parse(e){const{ctx:t,status:n}=this._processInputParams(e),o=this._def;if(t.parsedType!==ge.array)return be(t,{code:ye.invalid_type,expected:ge.array,received:t.parsedType}),Se;if(null!==o.exactLength){const e=t.data.length>o.exactLength.value,i=t.data.length<o.exactLength.value;(e||i)&&(be(t,{code:e?ye.too_big:ye.too_small,minimum:i?o.exactLength.value:void 0,maximum:e?o.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:o.exactLength.message}),n.dirty())}if(null!==o.minLength&&t.data.length<o.minLength.value&&(be(t,{code:ye.too_small,minimum:o.minLength.value,type:"array",inclusive:!0,exact:!1,message:o.minLength.message}),n.dirty()),null!==o.maxLength&&t.data.length>o.maxLength.value&&(be(t,{code:ye.too_big,maximum:o.maxLength.value,type:"array",inclusive:!0,exact:!1,message:o.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map(((e,n)=>o.type._parseAsync(new Re(t,e,t.path,n))))).then((e=>Te.mergeArray(n,e)));const i=[...t.data].map(((e,n)=>o.type._parseSync(new Re(t,e,t.path,n))));return Te.mergeArray(n,i)}get element(){return this._def.type}min(e,t){return new vt({...this._def,minLength:{value:e,message:ke.toString(t)}})}max(e,t){return new vt({...this._def,maxLength:{value:e,message:ke.toString(t)}})}length(e,t){return new vt({...this._def,exactLength:{value:e,message:ke.toString(t)}})}nonempty(e){return this.min(1,e)}}function yt(e){if(e instanceof _t){const t={};for(const n in e.shape){const o=e.shape[n];t[n]=Mt.create(yt(o))}return new _t({...e._def,shape:()=>t})}return e instanceof vt?new vt({...e._def,type:yt(e.element)}):e instanceof Mt?Mt.create(yt(e.unwrap())):e instanceof Dt?Dt.create(yt(e.unwrap())):e instanceof Tt?Tt.create(e.items.map((e=>yt(e)))):e}vt.create=(e,t)=>new vt({type:e,minLength:null,maxLength:null,exactLength:null,typeName:Bt.ZodArray,...De(t)});class _t extends Ve{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;const e=this._def.shape(),t=me.objectKeys(e);return this._cached={shape:e,keys:t},this._cached}_parse(e){if(this._getType(e)!==ge.object){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.object,received:t.parsedType}),Se}const{status:t,ctx:n}=this._processInputParams(e),{shape:o,keys:i}=this._getCached(),s=[];if(!(this._def.catchall instanceof ht&&"strip"===this._def.unknownKeys))for(const a in n.data)i.includes(a)||s.push(a);const r=[];for(const a of i){const e=o[a],t=n.data[a];r.push({key:{status:"valid",value:a},value:e._parse(new Re(n,t,n.path,a)),alwaysSet:a in n.data})}if(this._def.catchall instanceof ht){const e=this._def.unknownKeys;if("passthrough"===e)for(const t of s)r.push({key:{status:"valid",value:t},value:{status:"valid",value:n.data[t]}});else if("strict"===e)s.length>0&&(be(n,{code:ye.unrecognized_keys,keys:s}),t.dirty());else if("strip"!==e)throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{const e=this._def.catchall;for(const t of s){const o=n.data[t];r.push({key:{status:"valid",value:t},value:e._parse(new Re(n,o,n.path,t)),alwaysSet:t in n.data})}}return n.common.async?Promise.resolve().then((async()=>{const e=[];for(const t of r){const n=await t.key,o=await t.value;e.push({key:n,value:o,alwaysSet:t.alwaysSet})}return e})).then((e=>Te.mergeObjectSync(t,e))):Te.mergeObjectSync(t,r)}get shape(){return this._def.shape()}strict(e){return ke.errToObj,new _t({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(t,n)=>{var o,i;const s=(null==(i=(o=this._def).errorMap)?void 0:i.call(o,t,n).message)??n.defaultError;return"unrecognized_keys"===t.code?{message:ke.errToObj(e).message??s}:{message:s}}}:{}})}strip(){return new _t({...this._def,unknownKeys:"strip"})}passthrough(){return new _t({...this._def,unknownKeys:"passthrough"})}extend(e){return new _t({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new _t({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Bt.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new _t({...this._def,catchall:e})}pick(e){const t={};for(const n of me.objectKeys(e))e[n]&&this.shape[n]&&(t[n]=this.shape[n]);return new _t({...this._def,shape:()=>t})}omit(e){const t={};for(const n of me.objectKeys(this.shape))e[n]||(t[n]=this.shape[n]);return new _t({...this._def,shape:()=>t})}deepPartial(){return yt(this)}partial(e){const t={};for(const n of me.objectKeys(this.shape)){const o=this.shape[n];e&&!e[n]?t[n]=o:t[n]=o.optional()}return new _t({...this._def,shape:()=>t})}required(e){const t={};for(const n of me.objectKeys(this.shape))if(e&&!e[n])t[n]=this.shape[n];else{let e=this.shape[n];for(;e instanceof Mt;)e=e._def.innerType;t[n]=e}return new _t({...this._def,shape:()=>t})}keyof(){return Nt(me.objectKeys(this.shape))}}_t.create=(e,t)=>new _t({shape:()=>e,unknownKeys:"strip",catchall:ht.create(),typeName:Bt.ZodObject,...De(t)}),_t.strictCreate=(e,t)=>new _t({shape:()=>e,unknownKeys:"strict",catchall:ht.create(),typeName:Bt.ZodObject,...De(t)}),_t.lazycreate=(e,t)=>new _t({shape:e,unknownKeys:"strip",catchall:ht.create(),typeName:Bt.ZodObject,...De(t)});class Et extends Ve{_parse(e){const{ctx:t}=this._processInputParams(e),n=this._def.options;if(t.common.async)return Promise.all(n.map((async e=>{const n={...t,common:{...t.common,issues:[]},parent:null};return{result:await e._parseAsync({data:t.data,path:t.path,parent:n}),ctx:n}}))).then((function(e){for(const t of e)if("valid"===t.result.status)return t.result;for(const o of e)if("dirty"===o.result.status)return t.common.issues.push(...o.ctx.common.issues),o.result;const n=e.map((e=>new _e(e.ctx.common.issues)));return be(t,{code:ye.invalid_union,unionErrors:n}),Se}));{let e;const o=[];for(const s of n){const n={...t,common:{...t.common,issues:[]},parent:null},i=s._parseSync({data:t.data,path:t.path,parent:n});if("valid"===i.status)return i;"dirty"!==i.status||e||(e={result:i,ctx:n}),n.common.issues.length&&o.push(n.common.issues)}if(e)return t.common.issues.push(...e.ctx.common.issues),e.result;const i=o.map((e=>new _e(e)));return be(t,{code:ye.invalid_union,unionErrors:i}),Se}}get options(){return this._def.options}}function Ct(e,t){const n=ve(e),o=ve(t);if(e===t)return{valid:!0,data:e};if(n===ge.object&&o===ge.object){const n=me.objectKeys(t),o=me.objectKeys(e).filter((e=>-1!==n.indexOf(e))),i={...e,...t};for(const s of o){const n=Ct(e[s],t[s]);if(!n.valid)return{valid:!1};i[s]=n.data}return{valid:!0,data:i}}if(n===ge.array&&o===ge.array){if(e.length!==t.length)return{valid:!1};const n=[];for(let o=0;o<e.length;o++){const i=Ct(e[o],t[o]);if(!i.valid)return{valid:!1};n.push(i.data)}return{valid:!0,data:n}}return n===ge.date&&o===ge.date&&+e==+t?{valid:!0,data:e}:{valid:!1}}Et.create=(e,t)=>new Et({options:e,typeName:Bt.ZodUnion,...De(t)});class bt extends Ve{_parse(e){const{status:t,ctx:n}=this._processInputParams(e),o=(e,o)=>{if(Ie(e)||Ie(o))return Se;const i=Ct(e.value,o.value);return i.valid?((Oe(e)||Oe(o))&&t.dirty(),{status:t.value,value:i.data}):(be(n,{code:ye.invalid_intersection_types}),Se)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then((([e,t])=>o(e,t))):o(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}}bt.create=(e,t,n)=>new bt({left:e,right:t,typeName:Bt.ZodIntersection,...De(n)});class Tt extends Ve{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==ge.array)return be(n,{code:ye.invalid_type,expected:ge.array,received:n.parsedType}),Se;if(n.data.length<this._def.items.length)return be(n,{code:ye.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Se;!this._def.rest&&n.data.length>this._def.items.length&&(be(n,{code:ye.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());const o=[...n.data].map(((e,t)=>{const o=this._def.items[t]||this._def.rest;return o?o._parse(new Re(n,e,n.path,t)):null})).filter((e=>!!e));return n.common.async?Promise.all(o).then((e=>Te.mergeArray(t,e))):Te.mergeArray(t,o)}get items(){return this._def.items}rest(e){return new Tt({...this._def,rest:e})}}Tt.create=(e,t)=>{if(!Array.isArray(e))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Tt({items:e,typeName:Bt.ZodTuple,rest:null,...De(t)})};class St extends Ve{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==ge.object)return be(n,{code:ye.invalid_type,expected:ge.object,received:n.parsedType}),Se;const o=[],i=this._def.keyType,s=this._def.valueType;for(const r in n.data)o.push({key:i._parse(new Re(n,r,n.path,r)),value:s._parse(new Re(n,n.data[r],n.path,r)),alwaysSet:r in n.data});return n.common.async?Te.mergeObjectAsync(t,o):Te.mergeObjectSync(t,o)}get element(){return this._def.valueType}static create(e,t,n){return new St(t instanceof Ve?{keyType:e,valueType:t,typeName:Bt.ZodRecord,...De(n)}:{keyType:it.create(),valueType:e,typeName:Bt.ZodRecord,...De(t)})}}class wt extends Ve{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==ge.map)return be(n,{code:ye.invalid_type,expected:ge.map,received:n.parsedType}),Se;const o=this._def.keyType,i=this._def.valueType,s=[...n.data.entries()].map((([e,t],s)=>({key:o._parse(new Re(n,e,n.path,[s,"key"])),value:i._parse(new Re(n,t,n.path,[s,"value"]))})));if(n.common.async){const e=new Map;return Promise.resolve().then((async()=>{for(const n of s){const o=await n.key,i=await n.value;if("aborted"===o.status||"aborted"===i.status)return Se;"dirty"!==o.status&&"dirty"!==i.status||t.dirty(),e.set(o.value,i.value)}return{status:t.value,value:e}}))}{const e=new Map;for(const n of s){const o=n.key,i=n.value;if("aborted"===o.status||"aborted"===i.status)return Se;"dirty"!==o.status&&"dirty"!==i.status||t.dirty(),e.set(o.value,i.value)}return{status:t.value,value:e}}}}wt.create=(e,t,n)=>new wt({valueType:t,keyType:e,typeName:Bt.ZodMap,...De(n)});class At extends Ve{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==ge.set)return be(n,{code:ye.invalid_type,expected:ge.set,received:n.parsedType}),Se;const o=this._def;null!==o.minSize&&n.data.size<o.minSize.value&&(be(n,{code:ye.too_small,minimum:o.minSize.value,type:"set",inclusive:!0,exact:!1,message:o.minSize.message}),t.dirty()),null!==o.maxSize&&n.data.size>o.maxSize.value&&(be(n,{code:ye.too_big,maximum:o.maxSize.value,type:"set",inclusive:!0,exact:!1,message:o.maxSize.message}),t.dirty());const i=this._def.valueType;function s(e){const n=new Set;for(const o of e){if("aborted"===o.status)return Se;"dirty"===o.status&&t.dirty(),n.add(o.value)}return{status:t.value,value:n}}const r=[...n.data.values()].map(((e,t)=>i._parse(new Re(n,e,n.path,t))));return n.common.async?Promise.all(r).then((e=>s(e))):s(r)}min(e,t){return new At({...this._def,minSize:{value:e,message:ke.toString(t)}})}max(e,t){return new At({...this._def,maxSize:{value:e,message:ke.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}}At.create=(e,t)=>new At({valueType:e,minSize:null,maxSize:null,typeName:Bt.ZodSet,...De(t)});class It extends Ve{get schema(){return this._def.getter()}_parse(e){const{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}}It.create=(e,t)=>new It({getter:e,typeName:Bt.ZodLazy,...De(t)});class Ot extends Ve{_parse(e){if(e.data!==this._def.value){const t=this._getOrReturnCtx(e);return be(t,{received:t.data,code:ye.invalid_literal,expected:this._def.value}),Se}return{status:"valid",value:e.data}}get value(){return this._def.value}}function Nt(e,t){return new xt({values:e,typeName:Bt.ZodEnum,...De(t)})}Ot.create=(e,t)=>new Ot({value:e,typeName:Bt.ZodLiteral,...De(t)});class xt extends Ve{_parse(e){if("string"!=typeof e.data){const t=this._getOrReturnCtx(e),n=this._def.values;return be(t,{expected:me.joinValues(n),received:t.parsedType,code:ye.invalid_type}),Se}if(this._cache||(this._cache=new Set(this._def.values)),!this._cache.has(e.data)){const t=this._getOrReturnCtx(e),n=this._def.values;return be(t,{received:t.data,code:ye.invalid_enum_value,options:n}),Se}return Ae(e.data)}get options(){return this._def.values}get enum(){const e={};for(const t of this._def.values)e[t]=t;return e}get Values(){const e={};for(const t of this._def.values)e[t]=t;return e}get Enum(){const e={};for(const t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return xt.create(e,{...this._def,...t})}exclude(e,t=this._def){return xt.create(this.options.filter((t=>!e.includes(t))),{...this._def,...t})}}xt.create=Nt;class kt extends Ve{_parse(e){const t=me.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==ge.string&&n.parsedType!==ge.number){const e=me.objectValues(t);return be(n,{expected:me.joinValues(e),received:n.parsedType,code:ye.invalid_type}),Se}if(this._cache||(this._cache=new Set(me.getValidEnumValues(this._def.values))),!this._cache.has(e.data)){const e=me.objectValues(t);return be(n,{received:n.data,code:ye.invalid_enum_value,options:e}),Se}return Ae(e.data)}get enum(){return this._def.values}}kt.create=(e,t)=>new kt({values:e,typeName:Bt.ZodNativeEnum,...De(t)});class Pt extends Ve{unwrap(){return this._def.type}_parse(e){const{ctx:t}=this._processInputParams(e);if(t.parsedType!==ge.promise&&!1===t.common.async)return be(t,{code:ye.invalid_type,expected:ge.promise,received:t.parsedType}),Se;const n=t.parsedType===ge.promise?t.data:Promise.resolve(t.data);return Ae(n.then((e=>this._def.type.parseAsync(e,{path:t.path,errorMap:t.common.contextualErrorMap}))))}}Pt.create=(e,t)=>new Pt({type:e,typeName:Bt.ZodPromise,...De(t)});class Rt extends Ve{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Bt.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){const{status:t,ctx:n}=this._processInputParams(e),o=this._def.effect||null,i={addIssue:e=>{be(n,e),e.fatal?t.abort():t.dirty()},get path(){return n.path}};if(i.addIssue=i.addIssue.bind(i),"preprocess"===o.type){const e=o.transform(n.data,i);if(n.common.async)return Promise.resolve(e).then((async e=>{if("aborted"===t.value)return Se;const o=await this._def.schema._parseAsync({data:e,path:n.path,parent:n});return"aborted"===o.status?Se:"dirty"===o.status||"dirty"===t.value?we(o.value):o}));{if("aborted"===t.value)return Se;const o=this._def.schema._parseSync({data:e,path:n.path,parent:n});return"aborted"===o.status?Se:"dirty"===o.status||"dirty"===t.value?we(o.value):o}}if("refinement"===o.type){const e=e=>{const t=o.refinement(e,i);if(n.common.async)return Promise.resolve(t);if(t instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1===n.common.async){const o=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===o.status?Se:("dirty"===o.status&&t.dirty(),e(o.value),{status:t.value,value:o.value})}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then((n=>"aborted"===n.status?Se:("dirty"===n.status&&t.dirty(),e(n.value).then((()=>({status:t.value,value:n.value}))))))}if("transform"===o.type){if(!1===n.common.async){const e=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Ne(e))return Se;const s=o.transform(e.value,i);if(s instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:s}}return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then((e=>Ne(e)?Promise.resolve(o.transform(e.value,i)).then((e=>({status:t.value,value:e}))):Se))}me.assertNever(o)}}Rt.create=(e,t,n)=>new Rt({schema:e,typeName:Bt.ZodEffects,effect:t,...De(n)}),Rt.createWithPreprocess=(e,t,n)=>new Rt({schema:t,effect:{type:"preprocess",transform:e},typeName:Bt.ZodEffects,...De(n)});class Mt extends Ve{_parse(e){return this._getType(e)===ge.undefined?Ae(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Mt.create=(e,t)=>new Mt({innerType:e,typeName:Bt.ZodOptional,...De(t)});class Dt extends Ve{_parse(e){return this._getType(e)===ge.null?Ae(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}Dt.create=(e,t)=>new Dt({innerType:e,typeName:Bt.ZodNullable,...De(t)});class Vt extends Ve{_parse(e){const{ctx:t}=this._processInputParams(e);let n=t.data;return t.parsedType===ge.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}}Vt.create=(e,t)=>new Vt({innerType:e,typeName:Bt.ZodDefault,defaultValue:"function"==typeof t.default?t.default:()=>t.default,...De(t)});class Ut extends Ve{_parse(e){const{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},o=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return xe(o)?o.then((e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new _e(n.common.issues)},input:n.data})}))):{status:"valid",value:"valid"===o.status?o.value:this._def.catchValue({get error(){return new _e(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}}Ut.create=(e,t)=>new Ut({innerType:e,typeName:Bt.ZodCatch,catchValue:"function"==typeof t.catch?t.catch:()=>t.catch,...De(t)});class Lt extends Ve{_parse(e){if(this._getType(e)!==ge.nan){const t=this._getOrReturnCtx(e);return be(t,{code:ye.invalid_type,expected:ge.nan,received:t.parsedType}),Se}return{status:"valid",value:e.data}}}Lt.create=e=>new Lt({typeName:Bt.ZodNaN,...De(e)});class jt extends Ve{_parse(e){const{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}}class qt extends Ve{_parse(e){const{status:t,ctx:n}=this._processInputParams(e);if(n.common.async){return(async()=>{const e=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?Se:"dirty"===e.status?(t.dirty(),we(e.value)):this._def.out._parseAsync({data:e.value,path:n.path,parent:n})})()}{const e=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return"aborted"===e.status?Se:"dirty"===e.status?(t.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:n.path,parent:n})}}static create(e,t){return new qt({in:e,out:t,typeName:Bt.ZodPipeline})}}class Ft extends Ve{_parse(e){const t=this._def.innerType._parse(e),n=e=>(Ne(e)&&(e.value=Object.freeze(e.value)),e);return xe(t)?t.then((e=>n(e))):n(t)}unwrap(){return this._def.innerType}}var Bt,Gt;Ft.create=(e,t)=>new Ft({innerType:e,typeName:Bt.ZodReadonly,...De(t)}),_t.lazycreate,(Gt=Bt||(Bt={})).ZodString="ZodString",Gt.ZodNumber="ZodNumber",Gt.ZodNaN="ZodNaN",Gt.ZodBigInt="ZodBigInt",Gt.ZodBoolean="ZodBoolean",Gt.ZodDate="ZodDate",Gt.ZodSymbol="ZodSymbol",Gt.ZodUndefined="ZodUndefined",Gt.ZodNull="ZodNull",Gt.ZodAny="ZodAny",Gt.ZodUnknown="ZodUnknown",Gt.ZodNever="ZodNever",Gt.ZodVoid="ZodVoid",Gt.ZodArray="ZodArray",Gt.ZodObject="ZodObject",Gt.ZodUnion="ZodUnion",Gt.ZodDiscriminatedUnion="ZodDiscriminatedUnion",Gt.ZodIntersection="ZodIntersection",Gt.ZodTuple="ZodTuple",Gt.ZodRecord="ZodRecord",Gt.ZodMap="ZodMap",Gt.ZodSet="ZodSet",Gt.ZodFunction="ZodFunction",Gt.ZodLazy="ZodLazy",Gt.ZodLiteral="ZodLiteral",Gt.ZodEnum="ZodEnum",Gt.ZodEffects="ZodEffects",Gt.ZodNativeEnum="ZodNativeEnum",Gt.ZodOptional="ZodOptional",Gt.ZodNullable="ZodNullable",Gt.ZodDefault="ZodDefault",Gt.ZodCatch="ZodCatch",Gt.ZodPromise="ZodPromise",Gt.ZodBranded="ZodBranded",Gt.ZodPipeline="ZodPipeline",Gt.ZodReadonly="ZodReadonly";const $t=it.create,Jt=rt.create;Lt.create,at.create;const Ht=lt.create;ct.create,ut.create,dt.create,pt.create,mt.create;const Zt=ft.create;ht.create,gt.create;const Yt=vt.create,Kt=_t.create;_t.strictCreate;const Wt=Et.create;bt.create,Tt.create;const zt=St.create;wt.create,At.create;const Xt=It.create;Ot.create;const Qt=xt.create;kt.create,Pt.create,Rt.create,Mt.create,Dt.create,Rt.createWithPreprocess;const en=e=>it.create({...e,coerce:!0});
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */let tn,nn;function on(e,t,n){var o,i,s;if(!(null===(o=e.httpOptions)||void 0===o?void 0:o.baseUrl)){const o={geminiUrl:tn,vertexUrl:nn};return e.vertexai?null!==(i=o.vertexUrl)&&void 0!==i?i:t:null!==(s=o.geminiUrl)&&void 0!==s?s:n}return e.httpOptions.baseUrl}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class sn{}function rn(e,t){return e.replace(/\{([^}]+)\}/g,((e,n)=>{if(Object.prototype.hasOwnProperty.call(t,n)){const e=t[n];return null!=e?String(e):""}throw new Error(`Key '${n}' not found in valueMap.`)}))}function an(e,t,n){for(let s=0;s<t.length-1;s++){const o=t[s];if(o.endsWith("[]")){const i=o.slice(0,-2);if(!(i in e)){if(!Array.isArray(n))throw new Error(`Value must be a list given an array path ${o}`);e[i]=Array.from({length:n.length},(()=>({})))}if(Array.isArray(e[i])){const o=e[i];if(Array.isArray(n))for(let e=0;e<o.length;e++){an(o[e],t.slice(s+1),n[e])}else for(const e of o)an(e,t.slice(s+1),n)}return}if(o.endsWith("[0]")){const i=o.slice(0,-3);i in e||(e[i]=[{}]);return void an(e[i][0],t.slice(s+1),n)}e[o]&&"object"==typeof e[o]||(e[o]={}),e=e[o]}const o=t[t.length-1],i=e[o];if(void 0!==i){if(!n||"object"==typeof n&&0===Object.keys(n).length)return;if(n===i)return;if("object"!=typeof i||"object"!=typeof n||null===i||null===n)throw new Error(`Cannot set value for an existing key. Key: ${o}`);Object.assign(i,n)}else e[o]=n}function ln(e,t){try{if(1===t.length&&"_self"===t[0])return e;for(let n=0;n<t.length;n++){if("object"!=typeof e||null===e)return;const o=t[n];if(o.endsWith("[]")){const i=o.slice(0,-2);if(i in e){const o=e[i];if(!Array.isArray(o))return;return o.map((e=>ln(e,t.slice(n+1))))}return}e=e[o]}return e}catch(n){if(n instanceof TypeError)return;throw n}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */var cn,un,dn,pn,mn,fn,hn,gn,vn,yn,_n,En,Cn,bn,Tn,Sn,wn,An,In,On,Nn,xn,kn,Pn,Rn,Mn,Dn,Vn,Un,Ln,jn,qn,Fn,Bn,Gn,$n,Jn,Hn,Zn,Yn,Kn,Wn,zn,Xn,Qn,eo,to,no,oo,io,so,ro,ao,lo,co,uo,po,mo,fo,ho,go,vo,yo,_o,Eo,Co,bo,To,So,wo,Ao,Io,Oo,No,xo,ko,Po,Ro,Mo,Do,Vo,Uo,Lo,jo;(un=cn||(cn={})).OUTCOME_UNSPECIFIED="OUTCOME_UNSPECIFIED",un.OUTCOME_OK="OUTCOME_OK",un.OUTCOME_FAILED="OUTCOME_FAILED",un.OUTCOME_DEADLINE_EXCEEDED="OUTCOME_DEADLINE_EXCEEDED",(pn=dn||(dn={})).LANGUAGE_UNSPECIFIED="LANGUAGE_UNSPECIFIED",pn.PYTHON="PYTHON",(fn=mn||(mn={})).TYPE_UNSPECIFIED="TYPE_UNSPECIFIED",fn.STRING="STRING",fn.NUMBER="NUMBER",fn.INTEGER="INTEGER",fn.BOOLEAN="BOOLEAN",fn.ARRAY="ARRAY",fn.OBJECT="OBJECT",fn.NULL="NULL",(gn=hn||(hn={})).HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",gn.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",gn.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",gn.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",gn.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",gn.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY",gn.HARM_CATEGORY_IMAGE_HATE="HARM_CATEGORY_IMAGE_HATE",gn.HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT="HARM_CATEGORY_IMAGE_DANGEROUS_CONTENT",gn.HARM_CATEGORY_IMAGE_HARASSMENT="HARM_CATEGORY_IMAGE_HARASSMENT",gn.HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT="HARM_CATEGORY_IMAGE_SEXUALLY_EXPLICIT",(yn=vn||(vn={})).HARM_BLOCK_METHOD_UNSPECIFIED="HARM_BLOCK_METHOD_UNSPECIFIED",yn.SEVERITY="SEVERITY",yn.PROBABILITY="PROBABILITY",(En=_n||(_n={})).HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",En.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",En.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",En.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",En.BLOCK_NONE="BLOCK_NONE",En.OFF="OFF",(bn=Cn||(Cn={})).MODE_UNSPECIFIED="MODE_UNSPECIFIED",bn.MODE_DYNAMIC="MODE_DYNAMIC",(Sn=Tn||(Tn={})).AUTH_TYPE_UNSPECIFIED="AUTH_TYPE_UNSPECIFIED",Sn.NO_AUTH="NO_AUTH",Sn.API_KEY_AUTH="API_KEY_AUTH",Sn.HTTP_BASIC_AUTH="HTTP_BASIC_AUTH",Sn.GOOGLE_SERVICE_ACCOUNT_AUTH="GOOGLE_SERVICE_ACCOUNT_AUTH",Sn.OAUTH="OAUTH",Sn.OIDC_AUTH="OIDC_AUTH",(An=wn||(wn={})).API_SPEC_UNSPECIFIED="API_SPEC_UNSPECIFIED",An.SIMPLE_SEARCH="SIMPLE_SEARCH",An.ELASTIC_SEARCH="ELASTIC_SEARCH",(On=In||(In={})).ENVIRONMENT_UNSPECIFIED="ENVIRONMENT_UNSPECIFIED",On.ENVIRONMENT_BROWSER="ENVIRONMENT_BROWSER",(xn=Nn||(Nn={})).URL_RETRIEVAL_STATUS_UNSPECIFIED="URL_RETRIEVAL_STATUS_UNSPECIFIED",xn.URL_RETRIEVAL_STATUS_SUCCESS="URL_RETRIEVAL_STATUS_SUCCESS",xn.URL_RETRIEVAL_STATUS_ERROR="URL_RETRIEVAL_STATUS_ERROR",(Pn=kn||(kn={})).FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",Pn.STOP="STOP",Pn.MAX_TOKENS="MAX_TOKENS",Pn.SAFETY="SAFETY",Pn.RECITATION="RECITATION",Pn.LANGUAGE="LANGUAGE",Pn.OTHER="OTHER",Pn.BLOCKLIST="BLOCKLIST",Pn.PROHIBITED_CONTENT="PROHIBITED_CONTENT",Pn.SPII="SPII",Pn.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",Pn.IMAGE_SAFETY="IMAGE_SAFETY",Pn.UNEXPECTED_TOOL_CALL="UNEXPECTED_TOOL_CALL",(Mn=Rn||(Rn={})).HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",Mn.NEGLIGIBLE="NEGLIGIBLE",Mn.LOW="LOW",Mn.MEDIUM="MEDIUM",Mn.HIGH="HIGH",(Vn=Dn||(Dn={})).HARM_SEVERITY_UNSPECIFIED="HARM_SEVERITY_UNSPECIFIED",Vn.HARM_SEVERITY_NEGLIGIBLE="HARM_SEVERITY_NEGLIGIBLE",Vn.HARM_SEVERITY_LOW="HARM_SEVERITY_LOW",Vn.HARM_SEVERITY_MEDIUM="HARM_SEVERITY_MEDIUM",Vn.HARM_SEVERITY_HIGH="HARM_SEVERITY_HIGH",(Ln=Un||(Un={})).BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",Ln.SAFETY="SAFETY",Ln.OTHER="OTHER",Ln.BLOCKLIST="BLOCKLIST",Ln.PROHIBITED_CONTENT="PROHIBITED_CONTENT",Ln.IMAGE_SAFETY="IMAGE_SAFETY",(qn=jn||(jn={})).TRAFFIC_TYPE_UNSPECIFIED="TRAFFIC_TYPE_UNSPECIFIED",qn.ON_DEMAND="ON_DEMAND",qn.PROVISIONED_THROUGHPUT="PROVISIONED_THROUGHPUT",(Bn=Fn||(Fn={})).MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",Bn.TEXT="TEXT",Bn.IMAGE="IMAGE",Bn.AUDIO="AUDIO",($n=Gn||(Gn={})).MEDIA_RESOLUTION_UNSPECIFIED="MEDIA_RESOLUTION_UNSPECIFIED",$n.MEDIA_RESOLUTION_LOW="MEDIA_RESOLUTION_LOW",$n.MEDIA_RESOLUTION_MEDIUM="MEDIA_RESOLUTION_MEDIUM",$n.MEDIA_RESOLUTION_HIGH="MEDIA_RESOLUTION_HIGH",(Hn=Jn||(Jn={})).JOB_STATE_UNSPECIFIED="JOB_STATE_UNSPECIFIED",Hn.JOB_STATE_QUEUED="JOB_STATE_QUEUED",Hn.JOB_STATE_PENDING="JOB_STATE_PENDING",Hn.JOB_STATE_RUNNING="JOB_STATE_RUNNING",Hn.JOB_STATE_SUCCEEDED="JOB_STATE_SUCCEEDED",Hn.JOB_STATE_FAILED="JOB_STATE_FAILED",Hn.JOB_STATE_CANCELLING="JOB_STATE_CANCELLING",Hn.JOB_STATE_CANCELLED="JOB_STATE_CANCELLED",Hn.JOB_STATE_PAUSED="JOB_STATE_PAUSED",Hn.JOB_STATE_EXPIRED="JOB_STATE_EXPIRED",Hn.JOB_STATE_UPDATING="JOB_STATE_UPDATING",Hn.JOB_STATE_PARTIALLY_SUCCEEDED="JOB_STATE_PARTIALLY_SUCCEEDED",(Yn=Zn||(Zn={})).ADAPTER_SIZE_UNSPECIFIED="ADAPTER_SIZE_UNSPECIFIED",Yn.ADAPTER_SIZE_ONE="ADAPTER_SIZE_ONE",Yn.ADAPTER_SIZE_TWO="ADAPTER_SIZE_TWO",Yn.ADAPTER_SIZE_FOUR="ADAPTER_SIZE_FOUR",Yn.ADAPTER_SIZE_EIGHT="ADAPTER_SIZE_EIGHT",Yn.ADAPTER_SIZE_SIXTEEN="ADAPTER_SIZE_SIXTEEN",Yn.ADAPTER_SIZE_THIRTY_TWO="ADAPTER_SIZE_THIRTY_TWO",(Wn=Kn||(Kn={})).FEATURE_SELECTION_PREFERENCE_UNSPECIFIED="FEATURE_SELECTION_PREFERENCE_UNSPECIFIED",Wn.PRIORITIZE_QUALITY="PRIORITIZE_QUALITY",Wn.BALANCED="BALANCED",Wn.PRIORITIZE_COST="PRIORITIZE_COST",(Xn=zn||(zn={})).UNSPECIFIED="UNSPECIFIED",Xn.BLOCKING="BLOCKING",Xn.NON_BLOCKING="NON_BLOCKING",(eo=Qn||(Qn={})).MODE_UNSPECIFIED="MODE_UNSPECIFIED",eo.MODE_DYNAMIC="MODE_DYNAMIC",(no=to||(to={})).MODE_UNSPECIFIED="MODE_UNSPECIFIED",no.AUTO="AUTO",no.ANY="ANY",no.NONE="NONE",(io=oo||(oo={})).BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",io.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",io.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",io.BLOCK_NONE="BLOCK_NONE",(ro=so||(so={})).DONT_ALLOW="DONT_ALLOW",ro.ALLOW_ADULT="ALLOW_ADULT",ro.ALLOW_ALL="ALLOW_ALL",(lo=ao||(ao={})).auto="auto",lo.en="en",lo.ja="ja",lo.ko="ko",lo.hi="hi",(uo=co||(co={})).MASK_MODE_DEFAULT="MASK_MODE_DEFAULT",uo.MASK_MODE_USER_PROVIDED="MASK_MODE_USER_PROVIDED",uo.MASK_MODE_BACKGROUND="MASK_MODE_BACKGROUND",uo.MASK_MODE_FOREGROUND="MASK_MODE_FOREGROUND",uo.MASK_MODE_SEMANTIC="MASK_MODE_SEMANTIC",(mo=po||(po={})).CONTROL_TYPE_DEFAULT="CONTROL_TYPE_DEFAULT",mo.CONTROL_TYPE_CANNY="CONTROL_TYPE_CANNY",mo.CONTROL_TYPE_SCRIBBLE="CONTROL_TYPE_SCRIBBLE",mo.CONTROL_TYPE_FACE_MESH="CONTROL_TYPE_FACE_MESH",(ho=fo||(fo={})).SUBJECT_TYPE_DEFAULT="SUBJECT_TYPE_DEFAULT",ho.SUBJECT_TYPE_PERSON="SUBJECT_TYPE_PERSON",ho.SUBJECT_TYPE_ANIMAL="SUBJECT_TYPE_ANIMAL",ho.SUBJECT_TYPE_PRODUCT="SUBJECT_TYPE_PRODUCT",(vo=go||(go={})).EDIT_MODE_DEFAULT="EDIT_MODE_DEFAULT",vo.EDIT_MODE_INPAINT_REMOVAL="EDIT_MODE_INPAINT_REMOVAL",vo.EDIT_MODE_INPAINT_INSERTION="EDIT_MODE_INPAINT_INSERTION",vo.EDIT_MODE_OUTPAINT="EDIT_MODE_OUTPAINT",vo.EDIT_MODE_CONTROLLED_EDITING="EDIT_MODE_CONTROLLED_EDITING",vo.EDIT_MODE_STYLE="EDIT_MODE_STYLE",vo.EDIT_MODE_BGSWAP="EDIT_MODE_BGSWAP",vo.EDIT_MODE_PRODUCT_IMAGE="EDIT_MODE_PRODUCT_IMAGE",(_o=yo||(yo={})).OPTIMIZED="OPTIMIZED",_o.LOSSLESS="LOSSLESS",(Co=Eo||(Eo={})).STATE_UNSPECIFIED="STATE_UNSPECIFIED",Co.PROCESSING="PROCESSING",Co.ACTIVE="ACTIVE",Co.FAILED="FAILED",(To=bo||(bo={})).SOURCE_UNSPECIFIED="SOURCE_UNSPECIFIED",To.UPLOADED="UPLOADED",To.GENERATED="GENERATED",(wo=So||(So={})).MODALITY_UNSPECIFIED="MODALITY_UNSPECIFIED",wo.TEXT="TEXT",wo.IMAGE="IMAGE",wo.VIDEO="VIDEO",wo.AUDIO="AUDIO",wo.DOCUMENT="DOCUMENT",(Io=Ao||(Ao={})).START_SENSITIVITY_UNSPECIFIED="START_SENSITIVITY_UNSPECIFIED",Io.START_SENSITIVITY_HIGH="START_SENSITIVITY_HIGH",Io.START_SENSITIVITY_LOW="START_SENSITIVITY_LOW",(No=Oo||(Oo={})).END_SENSITIVITY_UNSPECIFIED="END_SENSITIVITY_UNSPECIFIED",No.END_SENSITIVITY_HIGH="END_SENSITIVITY_HIGH",No.END_SENSITIVITY_LOW="END_SENSITIVITY_LOW",(ko=xo||(xo={})).ACTIVITY_HANDLING_UNSPECIFIED="ACTIVITY_HANDLING_UNSPECIFIED",ko.START_OF_ACTIVITY_INTERRUPTS="START_OF_ACTIVITY_INTERRUPTS",ko.NO_INTERRUPTION="NO_INTERRUPTION",(Ro=Po||(Po={})).TURN_COVERAGE_UNSPECIFIED="TURN_COVERAGE_UNSPECIFIED",Ro.TURN_INCLUDES_ONLY_ACTIVITY="TURN_INCLUDES_ONLY_ACTIVITY",Ro.TURN_INCLUDES_ALL_INPUT="TURN_INCLUDES_ALL_INPUT",(Do=Mo||(Mo={})).SCHEDULING_UNSPECIFIED="SCHEDULING_UNSPECIFIED",Do.SILENT="SILENT",Do.WHEN_IDLE="WHEN_IDLE",Do.INTERRUPT="INTERRUPT",(Uo=Vo||(Vo={})).SCALE_UNSPECIFIED="SCALE_UNSPECIFIED",Uo.C_MAJOR_A_MINOR="C_MAJOR_A_MINOR",Uo.D_FLAT_MAJOR_B_FLAT_MINOR="D_FLAT_MAJOR_B_FLAT_MINOR",Uo.D_MAJOR_B_MINOR="D_MAJOR_B_MINOR",Uo.E_FLAT_MAJOR_C_MINOR="E_FLAT_MAJOR_C_MINOR",Uo.E_MAJOR_D_FLAT_MINOR="E_MAJOR_D_FLAT_MINOR",Uo.F_MAJOR_D_MINOR="F_MAJOR_D_MINOR",Uo.G_FLAT_MAJOR_E_FLAT_MINOR="G_FLAT_MAJOR_E_FLAT_MINOR",Uo.G_MAJOR_E_MINOR="G_MAJOR_E_MINOR",Uo.A_FLAT_MAJOR_F_MINOR="A_FLAT_MAJOR_F_MINOR",Uo.A_MAJOR_G_FLAT_MINOR="A_MAJOR_G_FLAT_MINOR",Uo.B_FLAT_MAJOR_G_MINOR="B_FLAT_MAJOR_G_MINOR",Uo.B_MAJOR_A_FLAT_MINOR="B_MAJOR_A_FLAT_MINOR",(jo=Lo||(Lo={})).PLAYBACK_CONTROL_UNSPECIFIED="PLAYBACK_CONTROL_UNSPECIFIED",jo.PLAY="PLAY",jo.PAUSE="PAUSE",jo.STOP="STOP",jo.RESET_CONTEXT="RESET_CONTEXT";class qo{constructor(e){const t={};for(const n of e.headers.entries())t[n[0]]=n[1];this.headers=t,this.responseInternal=e}json(){return this.responseInternal.json()}}class Fo{get text(){var e,n,o,i,s,r,a,l;if(0===(null===(i=null===(o=null===(n=null===(e=this.candidates)||void 0===e?void 0:e[0])||void 0===n?void 0:n.content)||void 0===o?void 0:o.parts)||void 0===i?void 0:i.length))return;this.candidates&&this.candidates.length>1&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1250","there are multiple candidates in the response, returning text from the first one.");let c="",u=!1;const d=[];for(const t of null!==(l=null===(a=null===(r=null===(s=this.candidates)||void 0===s?void 0:s[0])||void 0===r?void 0:r.content)||void 0===a?void 0:a.parts)&&void 0!==l?l:[]){for(const[e,n]of Object.entries(t))"text"===e||"thought"===e||null===n&&void 0===n||d.push(e);if("string"==typeof t.text){if("boolean"==typeof t.thought&&t.thought)continue;u=!0,c+=t.text}}return d.length>0&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1272",`there are non-text parts ${d} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),u?c:void 0}get data(){var e,n,o,i,s,r,a,l;if(0===(null===(i=null===(o=null===(n=null===(e=this.candidates)||void 0===e?void 0:e[0])||void 0===n?void 0:n.content)||void 0===o?void 0:o.parts)||void 0===i?void 0:i.length))return;this.candidates&&this.candidates.length>1&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1293","there are multiple candidates in the response, returning data from the first one.");let c="";const u=[];for(const t of null!==(l=null===(a=null===(r=null===(s=this.candidates)||void 0===s?void 0:s[0])||void 0===r?void 0:r.content)||void 0===a?void 0:a.parts)&&void 0!==l?l:[]){for(const[e,n]of Object.entries(t))"inlineData"===e||null===n&&void 0===n||u.push(e);t.inlineData&&"string"==typeof t.inlineData.data&&(c+=atob(t.inlineData.data))}return u.length>0&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1309",`there are non-data parts ${u} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),c.length>0?btoa(c):void 0}get functionCalls(){var e,n,o,i,s,r,a,l;if(0===(null===(i=null===(o=null===(n=null===(e=this.candidates)||void 0===e?void 0:e[0])||void 0===n?void 0:n.content)||void 0===o?void 0:o.parts)||void 0===i?void 0:i.length))return;this.candidates&&this.candidates.length>1&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1364","there are multiple candidates in the response, returning function calls from the first one.");const c=null===(l=null===(a=null===(r=null===(s=this.candidates)||void 0===s?void 0:s[0])||void 0===r?void 0:r.content)||void 0===a?void 0:a.parts)||void 0===l?void 0:l.filter((e=>e.functionCall)).map((e=>e.functionCall)).filter((e=>void 0!==e));return 0!==(null==c?void 0:c.length)?c:void 0}get executableCode(){var e,n,o,i,s,r,a,l,c;if(0===(null===(i=null===(o=null===(n=null===(e=this.candidates)||void 0===e?void 0:e[0])||void 0===n?void 0:n.content)||void 0===o?void 0:o.parts)||void 0===i?void 0:i.length))return;this.candidates&&this.candidates.length>1&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1401","there are multiple candidates in the response, returning executable code from the first one.");const u=null===(l=null===(a=null===(r=null===(s=this.candidates)||void 0===s?void 0:s[0])||void 0===r?void 0:r.content)||void 0===a?void 0:a.parts)||void 0===l?void 0:l.filter((e=>e.executableCode)).map((e=>e.executableCode)).filter((e=>void 0!==e));return 0!==(null==u?void 0:u.length)?null===(c=null==u?void 0:u[0])||void 0===c?void 0:c.code:void 0}get codeExecutionResult(){var e,n,o,i,s,r,a,l,c;if(0===(null===(i=null===(o=null===(n=null===(e=this.candidates)||void 0===e?void 0:e[0])||void 0===n?void 0:n.content)||void 0===o?void 0:o.parts)||void 0===i?void 0:i.length))return;this.candidates&&this.candidates.length>1&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1437","there are multiple candidates in the response, returning code execution result from the first one.");const u=null===(l=null===(a=null===(r=null===(s=this.candidates)||void 0===s?void 0:s[0])||void 0===r?void 0:r.content)||void 0===a?void 0:a.parts)||void 0===l?void 0:l.filter((e=>e.codeExecutionResult)).map((e=>e.codeExecutionResult)).filter((e=>void 0!==e));return 0!==(null==u?void 0:u.length)?null===(c=null==u?void 0:u[0])||void 0===c?void 0:c.output:void 0}}class Bo{}class Go{}class $o{}class Jo{}class Ho{}class Zo{}class Yo{}class Ko{}class Wo{}class zo{}class Xo{}class Qo{}class ei{}class ti{}class ni{}class oi{get text(){var e,n,o;let i="",s=!1;const r=[];for(const t of null!==(o=null===(n=null===(e=this.serverContent)||void 0===e?void 0:e.modelTurn)||void 0===n?void 0:n.parts)&&void 0!==o?o:[]){for(const[e,n]of Object.entries(t))"text"!==e&&"thought"!==e&&null!==n&&r.push(e);if("string"==typeof t.text){if("boolean"==typeof t.thought&&t.thought)continue;s=!0,i+=t.text}}return r.length>0&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1628",`there are non-text parts ${r} in the response, returning concatenation of all text parts. Please refer to the non text parts for a full response from model.`),s?i:void 0}get data(){var e,n,o;let i="";const s=[];for(const t of null!==(o=null===(n=null===(e=this.serverContent)||void 0===e?void 0:e.modelTurn)||void 0===n?void 0:n.parts)&&void 0!==o?o:[]){for(const[e,n]of Object.entries(t))"inlineData"!==e&&null!==n&&s.push(e);t.inlineData&&"string"==typeof t.inlineData.data&&(i+=atob(t.inlineData.data))}return s.length>0&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:1656",`there are non-data parts ${s} in the response, returning concatenation of all data parts. Please refer to the non data parts for a full response from model.`),i.length>0?btoa(i):void 0}}class ii{get audioChunk(){if(this.serverContent&&this.serverContent.audioChunks&&this.serverContent.audioChunks.length>0)return this.serverContent.audioChunks[0]}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function si(e,t){if(!t||"string"!=typeof t)throw new Error("model is required and must be a string");if(e.isVertexAI()){if(t.startsWith("publishers/")||t.startsWith("projects/")||t.startsWith("models/"))return t;if(t.indexOf("/")>=0){const e=t.split("/",2);return`publishers/${e[0]}/models/${e[1]}`}return`publishers/google/models/${t}`}return t.startsWith("models/")||t.startsWith("tunedModels/")?t:`models/${t}`}function ri(e,t){const n=si(e,t);return n?n.startsWith("publishers/")&&e.isVertexAI()?`projects/${e.getProject()}/locations/${e.getLocation()}/${n}`:n.startsWith("models/")&&e.isVertexAI()?`projects/${e.getProject()}/locations/${e.getLocation()}/publishers/google/${n}`:n:""}function ai(e){return Array.isArray(e)?e.map((e=>li(e))):[li(e)]}function li(e){if("object"==typeof e&&null!==e)return e;throw new Error("Could not parse input as Blob. Unsupported blob type: "+typeof e)}function ci(e){const t=li(e);if(t.mimeType&&t.mimeType.startsWith("image/"))return t;throw new Error(`Unsupported mime type: ${t.mimeType}`)}function ui(e){const t=li(e);if(t.mimeType&&t.mimeType.startsWith("audio/"))return t;throw new Error(`Unsupported mime type: ${t.mimeType}`)}function di(e){if(null==e)throw new Error("PartUnion is required");if("object"==typeof e)return e;if("string"==typeof e)return{text:e};throw new Error("Unsupported part type: "+typeof e)}function pi(e){if(null==e||Array.isArray(e)&&0===e.length)throw new Error("PartListUnion is required");return Array.isArray(e)?e.map((e=>di(e))):[di(e)]}function mi(e){return null!=e&&"object"==typeof e&&"parts"in e&&Array.isArray(e.parts)}function fi(e){return null!=e&&"object"==typeof e&&"functionCall"in e}function hi(e){return null!=e&&"object"==typeof e&&"functionResponse"in e}function gi(e){if(null==e)throw new Error("ContentUnion is required");return mi(e)?e:{role:"user",parts:pi(e)}}function vi(e,t){if(!t)return[];if(e.isVertexAI()&&Array.isArray(t))return t.flatMap((e=>{const t=gi(e);return t.parts&&t.parts.length>0&&void 0!==t.parts[0].text?[t.parts[0].text]:[]}));if(e.isVertexAI()){const e=gi(t);return e.parts&&e.parts.length>0&&void 0!==e.parts[0].text?[e.parts[0].text]:[]}return Array.isArray(t)?t.map((e=>gi(e))):[gi(t)]}function yi(e){if(null==e||Array.isArray(e)&&0===e.length)throw new Error("contents are required");if(!Array.isArray(e)){if(fi(e)||hi(e))throw new Error("To specify functionCall or functionResponse parts, please wrap them in a Content object, specifying the role for them");return[gi(e)]}const t=[],n=[],o=mi(e[0]);for(const i of e){const e=mi(i);if(e!=o)throw new Error("Mixing Content and Parts is not supported, please group the parts into a the appropriate Content objects and specify the roles for them");if(e)t.push(i);else{if(fi(i)||hi(i))throw new Error("To specify functionCall or functionResponse parts, please wrap them, and any other parts, in Content objects as appropriate, specifying the role for them");n.push(i)}}return o||t.push({role:"user",parts:pi(n)}),t}const _i=new Set(["type","format","title","description","default","items","minItems","maxItems","enum","properties","required","minProperties","maxProperties","minimum","maximum","minLength","maxLength","pattern","anyOf","propertyOrdering"]),Ei=Qt(["string","number","integer","object","array","boolean","null"]),Ci=Wt([Ei,Yt(Ei)]);function bi(e){const t={},n=["items"],o=["anyOf"],i=["properties"];if(e.type&&e.anyOf)throw new Error("type and anyOf cannot be both populated.");const s=e.anyOf;null!=s&&2==s.length&&("null"===s[0].type?(t.nullable=!0,e=s[1]):"null"===s[1].type&&(t.nullable=!0,e=s[0])),e.type instanceof Array&&function(e,t){e.includes("null")&&(t.nullable=!0);const n=e.filter((e=>"null"!==e));if(1===n.length)t.type=Object.values(mn).includes(n[0].toUpperCase())?n[0].toUpperCase():mn.TYPE_UNSPECIFIED;else{t.anyOf=[];for(const e of n)t.anyOf.push({type:Object.values(mn).includes(e.toUpperCase())?e.toUpperCase():mn.TYPE_UNSPECIFIED})}}(e.type,t);for(const[r,a]of Object.entries(e))if(null!=a)if("type"==r){if("null"===a)throw new Error("type: null can not be the only possible type for the field.");if(a instanceof Array)continue;t.type=Object.values(mn).includes(a.toUpperCase())?a.toUpperCase():mn.TYPE_UNSPECIFIED}else if(n.includes(r))t[r]=bi(a);else if(o.includes(r)){const e=[];for(const n of a)"null"!=n.type?e.push(bi(n)):t.nullable=!0;t[r]=e}else if(i.includes(r)){const e={};for(const[t,n]of Object.entries(a))e[t]=bi(n);t[r]=e}else{if("additionalProperties"===r)continue;t[r]=a}return t}function Ti(e){if(Object.keys(e).includes("$schema")){delete e.$schema;return bi(function(e=!0){const t=Xt((()=>{const n=Kt({type:Ci.optional(),format:$t().optional(),title:$t().optional(),description:$t().optional(),default:Zt().optional(),items:t.optional(),minItems:en().optional(),maxItems:en().optional(),enum:Yt(Zt()).optional(),properties:zt($t(),t).optional(),required:Yt($t()).optional(),minProperties:en().optional(),maxProperties:en().optional(),propertyOrdering:Yt($t()).optional(),minimum:Jt().optional(),maximum:Jt().optional(),minLength:en().optional(),maxLength:en().optional(),pattern:$t().optional(),anyOf:Yt(t).optional(),additionalProperties:Ht().optional()});return e?n.strict():n}));return t}().parse(e))}return bi(e)}function Si(e){if("object"==typeof e)return e;if("string"==typeof e)return{voiceConfig:{prebuiltVoiceConfig:{voiceName:e}}};throw new Error("Unsupported speechConfig type: "+typeof e)}function wi(e){if("multiSpeakerVoiceConfig"in e)throw new Error("multiSpeakerVoiceConfig is not supported in the live API.");return e}function Ai(e){if(e.functionDeclarations)for(const t of e.functionDeclarations)t.parameters&&(t.parameters=Ti(t.parameters)),t.response&&(t.response=Ti(t.response));return e}function Ii(e){if(null==e)throw new Error("tools is required");if(!Array.isArray(e))throw new Error("tools is required and must be an array of Tools");const t=[];for(const n of e)t.push(n);return t}function Oi(e,t){if("string"!=typeof t)throw new Error("name must be a string");return function(e,t,n,o=1){const i=!t.startsWith(`${n}/`)&&t.split("/").length===o;return e.isVertexAI()?t.startsWith("projects/")?t:t.startsWith("locations/")?`projects/${e.getProject()}/${t}`:t.startsWith(`${n}/`)?`projects/${e.getProject()}/locations/${e.getLocation()}/${t}`:i?`projects/${e.getProject()}/locations/${e.getLocation()}/${n}/${t}`:t:i?`${n}/${t}`:t}(e,t,"cachedContents")}function Ni(e){switch(e){case"STATE_UNSPECIFIED":return"JOB_STATE_UNSPECIFIED";case"CREATING":return"JOB_STATE_RUNNING";case"ACTIVE":return"JOB_STATE_SUCCEEDED";case"FAILED":return"JOB_STATE_FAILED";default:return e}}function xi(e){if("string"!=typeof e)throw new Error("fromImageBytes must be a string");return e}function ki(e){var t;let n;var o;if(null!=(o=e)&&"object"==typeof o&&"name"in o&&(n=e.name),!(function(e){return null!=e&&"object"==typeof e&&"uri"in e}(e)&&(n=e.uri,void 0===n)||function(e){return null!=e&&"object"==typeof e&&"video"in e}(e)&&(n=null===(t=e.video)||void 0===t?void 0:t.uri,void 0===n))){if("string"==typeof e&&(n=e),void 0===n)throw new Error("Could not extract file name from the provided input.");if(n.startsWith("https://")){const e=n.split("files/")[1].match(/[a-z0-9]+/);if(null===e)throw new Error(`Could not extract file name from URI ${n}`);n=e[0]}else n.startsWith("files/")&&(n=n.split("files/")[1]);return n}}function Pi(e,t){let n;return n=e.isVertexAI()?t?"publishers/google/models":"models":t?"models":"tunedModels",n}function Ri(e){for(const o of["models","tunedModels","publisherModels"])if(n=o,null!==(t=e)&&"object"==typeof t&&n in t)return e[o];var t,n;return[]}function Mi(e,t={}){const n=e,o={name:n.name,description:n.description,parameters:bi(Ui(n.inputSchema))};t.behavior&&(o.behavior=t.behavior);return{functionDeclarations:[o]}}function Di(e){const t=[];for(const n of e)t.push(Ui(n));return t}function Vi(e){const t={};for(const[n,o]of Object.entries(e)){const e=o;t[n]=Ui(e)}return t}function Ui(e){const t=new Set(["items"]),n=new Set(["anyOf"]),o=new Set(["properties"]),i={};for(const[s,r]of Object.entries(e))if(t.has(s))i[s]=Ui(r);else if(n.has(s))i[s]=Di(r);else if(o.has(s))i[s]=Vi(r);else if("type"===s){const e=r.toUpperCase();i[s]=Object.values(mn).includes(e)?e:mn.TYPE_UNSPECIFIED}else _i.has(s)&&(i[s]=r);return i}function Li(e,t){if("string"!=typeof t&&!Array.isArray(t)){if(e&&e.isVertexAI()){if(t.gcsUri&&t.bigqueryUri)throw new Error("Only one of `gcsUri` or `bigqueryUri` can be set.");if(!t.gcsUri&&!t.bigqueryUri)throw new Error("One of `gcsUri` or `bigqueryUri` must be set.")}else{if(t.inlinedRequests&&t.fileName)throw new Error("Only one of `inlinedRequests` or `fileName` can be set.");if(!t.inlinedRequests&&!t.fileName)throw new Error("One of `inlinedRequests` or `fileName` must be set.")}return t}if(Array.isArray(t))return{inlinedRequests:t};if("string"==typeof t){if(t.startsWith("gs://"))return{format:"jsonl",gcsUri:[t]};if(t.startsWith("bq://"))return{format:"bigquery",bigqueryUri:t};if(t.startsWith("files/"))return{fileName:t}}throw new Error(`Unsupported source: ${t}`)}function ji(e,t){const n=t;if(!e.isVertexAI()){if(/batches\/[^/]+$/.test(n))return n.split("/").pop();throw new Error(`Invalid batch job name: ${n}.`)}if(/^projects\/[^/]+\/locations\/[^/]+\/batchPredictionJobs\/[^/]+$/.test(n))return n.split("/").pop();if(/^\d+$/.test(n))return n;throw new Error(`Invalid batch job name: ${n}.`)}function qi(e){return"BATCH_STATE_UNSPECIFIED"===e?"JOB_STATE_UNSPECIFIED":"BATCH_STATE_PENDING"===e?"JOB_STATE_PENDING":"BATCH_STATE_SUCCEEDED"===e?"JOB_STATE_SUCCEEDED":"BATCH_STATE_FAILED"===e?"JOB_STATE_FAILED":"BATCH_STATE_CANCELLED"===e?"JOB_STATE_CANCELLED":e}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function Fi(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["fileUri"]);null!=n&&an(t,["fileUri"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function Bi(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Fi(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}function Gi(e){const t={},n=ln(e,["timeRangeFilter"]);return null!=n&&an(t,["timeRangeFilter"],function(e){const t={},n=ln(e,["startTime"]);null!=n&&an(t,["startTime"],n);const o=ln(e,["endTime"]);return null!=o&&an(t,["endTime"],o),t}(n)),t}function $i(e){const t={},n=ln(e,["dynamicRetrievalConfig"]);return null!=n&&an(t,["dynamicRetrievalConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["dynamicThreshold"]);return null!=o&&an(t,["dynamicThreshold"],o),t}(n)),t}function Ji(e){const t={},n=ln(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["behavior"]);null!=n&&an(t,["behavior"],n);const o=ln(e,["description"]);null!=o&&an(t,["description"],o);const i=ln(e,["name"]);null!=i&&an(t,["name"],i);const s=ln(e,["parameters"]);null!=s&&an(t,["parameters"],s);const r=ln(e,["parametersJsonSchema"]);null!=r&&an(t,["parametersJsonSchema"],r);const a=ln(e,["response"]);null!=a&&an(t,["response"],a);const l=ln(e,["responseJsonSchema"]);return null!=l&&an(t,["responseJsonSchema"],l),t}(e)))),an(t,["functionDeclarations"],e)}if(void 0!==ln(e,["retrieval"]))throw new Error("retrieval parameter is not supported in Gemini API.");const o=ln(e,["googleSearch"]);null!=o&&an(t,["googleSearch"],Gi(o));const i=ln(e,["googleSearchRetrieval"]);if(null!=i&&an(t,["googleSearchRetrieval"],$i(i)),void 0!==ln(e,["enterpriseWebSearch"]))throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==ln(e,["googleMaps"]))throw new Error("googleMaps parameter is not supported in Gemini API.");null!=ln(e,["urlContext"])&&an(t,["urlContext"],{});const s=ln(e,["codeExecution"]);null!=s&&an(t,["codeExecution"],s);const r=ln(e,["computerUse"]);return null!=r&&an(t,["computerUse"],r),t}function Hi(e){const t={},n=ln(e,["latLng"]);null!=n&&an(t,["latLng"],function(e){const t={},n=ln(e,["latitude"]);null!=n&&an(t,["latitude"],n);const o=ln(e,["longitude"]);return null!=o&&an(t,["longitude"],o),t}(n));const o=ln(e,["languageCode"]);return null!=o&&an(t,["languageCode"],o),t}function Zi(e){const t={},n=ln(e,["functionCallingConfig"]);null!=n&&an(t,["functionCallingConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["allowedFunctionNames"]);return null!=o&&an(t,["allowedFunctionNames"],o),t}(n));const o=ln(e,["retrievalConfig"]);return null!=o&&an(t,["retrievalConfig"],Hi(o)),t}function Yi(e){const t={},n=ln(e,["prebuiltVoiceConfig"]);return null!=n&&an(t,["prebuiltVoiceConfig"],function(e){const t={},n=ln(e,["voiceName"]);return null!=n&&an(t,["voiceName"],n),t}(n)),t}function Ki(e){const t={},n=ln(e,["speakerVoiceConfigs"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["speaker"]);null!=n&&an(t,["speaker"],n);const o=ln(e,["voiceConfig"]);return null!=o&&an(t,["voiceConfig"],Yi(o)),t}(e)))),an(t,["speakerVoiceConfigs"],e)}return t}function Wi(e,t,n){const o={},i=ln(t,["systemInstruction"]);void 0!==n&&null!=i&&an(n,["systemInstruction"],Bi(gi(i)));const s=ln(t,["temperature"]);null!=s&&an(o,["temperature"],s);const r=ln(t,["topP"]);null!=r&&an(o,["topP"],r);const a=ln(t,["topK"]);null!=a&&an(o,["topK"],a);const l=ln(t,["candidateCount"]);null!=l&&an(o,["candidateCount"],l);const c=ln(t,["maxOutputTokens"]);null!=c&&an(o,["maxOutputTokens"],c);const u=ln(t,["stopSequences"]);null!=u&&an(o,["stopSequences"],u);const d=ln(t,["responseLogprobs"]);null!=d&&an(o,["responseLogprobs"],d);const p=ln(t,["logprobs"]);null!=p&&an(o,["logprobs"],p);const m=ln(t,["presencePenalty"]);null!=m&&an(o,["presencePenalty"],m);const f=ln(t,["frequencyPenalty"]);null!=f&&an(o,["frequencyPenalty"],f);const h=ln(t,["seed"]);null!=h&&an(o,["seed"],h);const g=ln(t,["responseMimeType"]);null!=g&&an(o,["responseMimeType"],g);const v=ln(t,["responseSchema"]);null!=v&&an(o,["responseSchema"],function(e){const t={},n=ln(e,["anyOf"]);null!=n&&an(t,["anyOf"],n);const o=ln(e,["default"]);null!=o&&an(t,["default"],o);const i=ln(e,["description"]);null!=i&&an(t,["description"],i);const s=ln(e,["enum"]);null!=s&&an(t,["enum"],s);const r=ln(e,["example"]);null!=r&&an(t,["example"],r);const a=ln(e,["format"]);null!=a&&an(t,["format"],a);const l=ln(e,["items"]);null!=l&&an(t,["items"],l);const c=ln(e,["maxItems"]);null!=c&&an(t,["maxItems"],c);const u=ln(e,["maxLength"]);null!=u&&an(t,["maxLength"],u);const d=ln(e,["maxProperties"]);null!=d&&an(t,["maxProperties"],d);const p=ln(e,["maximum"]);null!=p&&an(t,["maximum"],p);const m=ln(e,["minItems"]);null!=m&&an(t,["minItems"],m);const f=ln(e,["minLength"]);null!=f&&an(t,["minLength"],f);const h=ln(e,["minProperties"]);null!=h&&an(t,["minProperties"],h);const g=ln(e,["minimum"]);null!=g&&an(t,["minimum"],g);const v=ln(e,["nullable"]);null!=v&&an(t,["nullable"],v);const y=ln(e,["pattern"]);null!=y&&an(t,["pattern"],y);const _=ln(e,["properties"]);null!=_&&an(t,["properties"],_);const E=ln(e,["propertyOrdering"]);null!=E&&an(t,["propertyOrdering"],E);const C=ln(e,["required"]);null!=C&&an(t,["required"],C);const b=ln(e,["title"]);null!=b&&an(t,["title"],b);const T=ln(e,["type"]);return null!=T&&an(t,["type"],T),t}(Ti(v)));const y=ln(t,["responseJsonSchema"]);if(null!=y&&an(o,["responseJsonSchema"],y),void 0!==ln(t,["routingConfig"]))throw new Error("routingConfig parameter is not supported in Gemini API.");if(void 0!==ln(t,["modelSelectionConfig"]))throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const _=ln(t,["safetySettings"]);if(void 0!==n&&null!=_){let e=_;Array.isArray(e)&&(e=e.map((e=>function(e){const t={};if(void 0!==ln(e,["method"]))throw new Error("method parameter is not supported in Gemini API.");const n=ln(e,["category"]);null!=n&&an(t,["category"],n);const o=ln(e,["threshold"]);return null!=o&&an(t,["threshold"],o),t}(e)))),an(n,["safetySettings"],e)}const E=ln(t,["tools"]);if(void 0!==n&&null!=E){let e=Ii(E);Array.isArray(e)&&(e=e.map((e=>Ji(Ai(e))))),an(n,["tools"],e)}const C=ln(t,["toolConfig"]);if(void 0!==n&&null!=C&&an(n,["toolConfig"],Zi(C)),void 0!==ln(t,["labels"]))throw new Error("labels parameter is not supported in Gemini API.");const b=ln(t,["cachedContent"]);void 0!==n&&null!=b&&an(n,["cachedContent"],Oi(e,b));const T=ln(t,["responseModalities"]);null!=T&&an(o,["responseModalities"],T);const S=ln(t,["mediaResolution"]);null!=S&&an(o,["mediaResolution"],S);const w=ln(t,["speechConfig"]);if(null!=w&&an(o,["speechConfig"],function(e){const t={},n=ln(e,["voiceConfig"]);null!=n&&an(t,["voiceConfig"],Yi(n));const o=ln(e,["multiSpeakerVoiceConfig"]);null!=o&&an(t,["multiSpeakerVoiceConfig"],Ki(o));const i=ln(e,["languageCode"]);return null!=i&&an(t,["languageCode"],i),t}(Si(w))),void 0!==ln(t,["audioTimestamp"]))throw new Error("audioTimestamp parameter is not supported in Gemini API.");const A=ln(t,["thinkingConfig"]);return null!=A&&an(o,["thinkingConfig"],function(e){const t={},n=ln(e,["includeThoughts"]);null!=n&&an(t,["includeThoughts"],n);const o=ln(e,["thinkingBudget"]);return null!=o&&an(t,["thinkingBudget"],o),t}(A)),o}function zi(e,t){const n={};if(void 0!==ln(t,["format"]))throw new Error("format parameter is not supported in Gemini API.");if(void 0!==ln(t,["gcsUri"]))throw new Error("gcsUri parameter is not supported in Gemini API.");if(void 0!==ln(t,["bigqueryUri"]))throw new Error("bigqueryUri parameter is not supported in Gemini API.");const o=ln(t,["fileName"]);null!=o&&an(n,["fileName"],o);const i=ln(t,["inlinedRequests"]);if(null!=i){let t=i;Array.isArray(t)&&(t=t.map((t=>function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["request","model"],si(e,o));const i=ln(t,["contents"]);if(null!=i){let e=yi(i);Array.isArray(e)&&(e=e.map((e=>Bi(e)))),an(n,["request","contents"],e)}const s=ln(t,["config"]);return null!=s&&an(n,["request","generationConfig"],Wi(e,s,n)),n}(e,t)))),an(n,["requests","requests"],t)}return n}function Xi(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["src"]);null!=i&&an(n,["batch","inputConfig"],zi(e,Li(e,i)));const s=ln(t,["config"]);return null!=s&&an(n,["config"],function(e,t){const n=ln(e,["displayName"]);if(void 0!==t&&null!=n&&an(t,["batch","displayName"],n),void 0!==ln(e,["dest"]))throw new Error("dest parameter is not supported in Gemini API.");return{}}(s,n)),n}function Qi(e){const t={},n=ln(e,["config"]);return null!=n&&an(t,["config"],function(e,t){const n=ln(e,["pageSize"]);void 0!==t&&null!=n&&an(t,["_query","pageSize"],n);const o=ln(e,["pageToken"]);if(void 0!==t&&null!=o&&an(t,["_query","pageToken"],o),void 0!==ln(e,["filter"]))throw new Error("filter parameter is not supported in Gemini API.");return{}}(n,t)),t}function es(e,t){const n=ln(e,["displayName"]);void 0!==t&&null!=n&&an(t,["displayName"],n);const o=ln(e,["dest"]);return void 0!==t&&null!=o&&an(t,["outputConfig"],function(e){const t={},n=ln(e,["format"]);null!=n&&an(t,["predictionsFormat"],n);const o=ln(e,["gcsUri"]);null!=o&&an(t,["gcsDestination","outputUriPrefix"],o);const i=ln(e,["bigqueryUri"]);if(null!=i&&an(t,["bigqueryDestination","outputUri"],i),void 0!==ln(e,["fileName"]))throw new Error("fileName parameter is not supported in Vertex AI.");if(void 0!==ln(e,["inlinedResponses"]))throw new Error("inlinedResponses parameter is not supported in Vertex AI.");return t}(function(e){const t=e;if(t.startsWith("gs://"))return{format:"jsonl",gcsUri:t};if(t.startsWith("bq://"))return{format:"bigquery",bigqueryUri:t};throw new Error(`Unsupported destination: ${t}`)}(o))),{}}function ts(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["model"],si(e,o));const i=ln(t,["src"]);null!=i&&an(n,["inputConfig"],function(e){const t={},n=ln(e,["format"]);null!=n&&an(t,["instancesFormat"],n);const o=ln(e,["gcsUri"]);null!=o&&an(t,["gcsSource","uris"],o);const i=ln(e,["bigqueryUri"]);if(null!=i&&an(t,["bigquerySource","inputUri"],i),void 0!==ln(e,["fileName"]))throw new Error("fileName parameter is not supported in Vertex AI.");if(void 0!==ln(e,["inlinedRequests"]))throw new Error("inlinedRequests parameter is not supported in Vertex AI.");return t}(Li(e,i)));const s=ln(t,["config"]);return null!=s&&an(n,["config"],es(s,n)),n}function ns(e){const t={},n=ln(e,["config"]);return null!=n&&an(t,["config"],function(e,t){const n=ln(e,["pageSize"]);void 0!==t&&null!=n&&an(t,["_query","pageSize"],n);const o=ln(e,["pageToken"]);void 0!==t&&null!=o&&an(t,["_query","pageToken"],o);const i=ln(e,["filter"]);return void 0!==t&&null!=i&&an(t,["_query","filter"],i),{}}(n,t)),t}function os(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={},n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={},n=ln(e,["fileUri"]);null!=n&&an(t,["fileUri"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function is(e){const t={},n=ln(e,["urlMetadata"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["retrievedUrl"]);null!=n&&an(t,["retrievedUrl"],n);const o=ln(e,["urlRetrievalStatus"]);return null!=o&&an(t,["urlRetrievalStatus"],o),t}(e)))),an(t,["urlMetadata"],e)}return t}function ss(e){const t={},n=ln(e,["content"]);null!=n&&an(t,["content"],function(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>os(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}(n));const o=ln(e,["citationMetadata"]);null!=o&&an(t,["citationMetadata"],function(e){const t={},n=ln(e,["citationSources"]);return null!=n&&an(t,["citations"],n),t}(o));const i=ln(e,["tokenCount"]);null!=i&&an(t,["tokenCount"],i);const s=ln(e,["finishReason"]);null!=s&&an(t,["finishReason"],s);const r=ln(e,["urlContextMetadata"]);null!=r&&an(t,["urlContextMetadata"],is(r));const a=ln(e,["avgLogprobs"]);null!=a&&an(t,["avgLogprobs"],a);const l=ln(e,["groundingMetadata"]);null!=l&&an(t,["groundingMetadata"],l);const c=ln(e,["index"]);null!=c&&an(t,["index"],c);const u=ln(e,["logprobsResult"]);null!=u&&an(t,["logprobsResult"],u);const d=ln(e,["safetyRatings"]);return null!=d&&an(t,["safetyRatings"],d),t}function rs(e){const t={},n=ln(e,["response"]);null!=n&&an(t,["response"],function(e){const t={},n=ln(e,["candidates"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>ss(e)))),an(t,["candidates"],e)}const o=ln(e,["modelVersion"]);null!=o&&an(t,["modelVersion"],o);const i=ln(e,["promptFeedback"]);null!=i&&an(t,["promptFeedback"],i);const s=ln(e,["usageMetadata"]);return null!=s&&an(t,["usageMetadata"],s),t}(n));return null!=ln(e,["error"])&&an(t,["error"],{}),t}function as(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["metadata","displayName"]);null!=o&&an(t,["displayName"],o);const i=ln(e,["metadata","state"]);null!=i&&an(t,["state"],qi(i));const s=ln(e,["metadata","createTime"]);null!=s&&an(t,["createTime"],s);const r=ln(e,["metadata","endTime"]);null!=r&&an(t,["endTime"],r);const a=ln(e,["metadata","updateTime"]);null!=a&&an(t,["updateTime"],a);const l=ln(e,["metadata","model"]);null!=l&&an(t,["model"],l);const c=ln(e,["metadata","output"]);return null!=c&&an(t,["dest"],function(e){const t={},n=ln(e,["responsesFile"]);null!=n&&an(t,["fileName"],n);const o=ln(e,["inlinedResponses","inlinedResponses"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>rs(e)))),an(t,["inlinedResponses"],e)}return t}(c)),t}function ls(e){const t={},n=ln(e,["details"]);null!=n&&an(t,["details"],n);const o=ln(e,["code"]);null!=o&&an(t,["code"],o);const i=ln(e,["message"]);return null!=i&&an(t,["message"],i),t}function cs(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["displayName"]);null!=o&&an(t,["displayName"],o);const i=ln(e,["state"]);null!=i&&an(t,["state"],qi(i));const s=ln(e,["error"]);null!=s&&an(t,["error"],ls(s));const r=ln(e,["createTime"]);null!=r&&an(t,["createTime"],r);const a=ln(e,["startTime"]);null!=a&&an(t,["startTime"],a);const l=ln(e,["endTime"]);null!=l&&an(t,["endTime"],l);const c=ln(e,["updateTime"]);null!=c&&an(t,["updateTime"],c);const u=ln(e,["model"]);null!=u&&an(t,["model"],u);const d=ln(e,["inputConfig"]);null!=d&&an(t,["src"],function(e){const t={},n=ln(e,["instancesFormat"]);null!=n&&an(t,["format"],n);const o=ln(e,["gcsSource","uris"]);null!=o&&an(t,["gcsUri"],o);const i=ln(e,["bigquerySource","inputUri"]);return null!=i&&an(t,["bigqueryUri"],i),t}(d));const p=ln(e,["outputConfig"]);return null!=p&&an(t,["dest"],function(e){const t={},n=ln(e,["predictionsFormat"]);null!=n&&an(t,["format"],n);const o=ln(e,["gcsDestination","outputUriPrefix"]);null!=o&&an(t,["gcsUri"],o);const i=ln(e,["bigqueryDestination","outputUri"]);return null!=i&&an(t,["bigqueryUri"],i),t}(p)),t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */
var us,ds;(ds=us||(us={})).PAGED_ITEM_BATCH_JOBS="batchJobs",ds.PAGED_ITEM_MODELS="models",ds.PAGED_ITEM_TUNING_JOBS="tuningJobs",ds.PAGED_ITEM_FILES="files",ds.PAGED_ITEM_CACHED_CONTENTS="cachedContents";class ps{constructor(e,t,n,o){this.pageInternal=[],this.paramsInternal={},this.requestInternal=t,this.init(e,n,o)}init(e,t,n){var o,i;this.nameInternal=e,this.pageInternal=t[this.nameInternal]||[],this.idxInternal=0;let s={config:{}};s=n?"object"==typeof n?Object.assign({},n):n:{config:{}},s.config&&(s.config.pageToken=t.nextPageToken),this.paramsInternal=s,this.pageInternalSize=null!==(i=null===(o=s.config)||void 0===o?void 0:o.pageSize)&&void 0!==i?i:this.pageInternal.length}initNextPage(e){this.init(this.nameInternal,e,this.paramsInternal)}get page(){return this.pageInternal}get name(){return this.nameInternal}get pageSize(){return this.pageInternalSize}get params(){return this.paramsInternal}get pageLength(){return this.pageInternal.length}getItem(e){return this.pageInternal[e]}[Symbol.asyncIterator](){return{next:async()=>{if(this.idxInternal>=this.pageLength){if(!this.hasNextPage())return{value:void 0,done:!0};await this.nextPage()}const e=this.getItem(this.idxInternal);return this.idxInternal+=1,{value:e,done:!1}},return:async()=>({value:void 0,done:!0})}}async nextPage(){if(!this.hasNextPage())throw new Error("No more pages to fetch.");const e=await this.requestInternal(this.params);return this.initNextPage(e),this.page}hasNextPage(){var e;return void 0!==(null===(e=this.params.config)||void 0===e?void 0:e.pageToken)}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class ms extends sn{constructor(e){super(),this.apiClient=e,this.create=async e=>{if(this.apiClient.isVertexAI()){const t=Date.now().toString();if(Array.isArray(e.src))throw new Error("InlinedRequest[] is not supported in Vertex AI. Please use Google Cloud Storage URI or BigQuery URI instead.");if(e.config=e.config||{},void 0===e.config.displayName&&(e.config.displayName="genaiBatchJob_${timestampStr}"),void 0===e.config.dest&&"string"==typeof e.src)if(e.src.startsWith("gs://")&&e.src.endsWith(".jsonl"))e.config.dest=`${e.src.slice(0,-6)}/dest`;else{if(!e.src.startsWith("bq://"))throw new Error("Unsupported source:"+e.src);e.config.dest=`${e.src}_dest_${t}`}}return await this.createInternal(e)},this.list=async(e={})=>new ps(us.PAGED_ITEM_BATCH_JOBS,(e=>this.listInternal(e)),await this.listInternal(e),e)}async createInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=ts(this.apiClient,e);return r=rn("batchPredictionJobs",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>cs(e)))}{const t=Xi(this.apiClient,e);return r=rn("{model}:batchGenerateContent",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>as(e)))}}async get(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],ji(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("batchPredictionJobs/{name}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>cs(e)))}{const t=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],ji(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("batches/{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>as(e)))}}async cancel(e){var t,n,o,i;let s="",r={};if(this.apiClient.isVertexAI()){const o=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],ji(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);s=rn("batchPredictionJobs/{name}:cancel",o._url),r=o._query,delete o.config,delete o._url,delete o._query,await this.apiClient.request({path:s,queryParams:r,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal})}else{const t=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],ji(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);s=rn("batches/{name}:cancel",t._url),r=t._query,delete t.config,delete t._url,delete t._query,await this.apiClient.request({path:s,queryParams:r,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal})}}async listInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=ns(e);return r=rn("batchPredictionJobs",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["batchPredictionJobs"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>cs(e)))),an(t,["batchJobs"],e)}return t}(e),n=new ni;return Object.assign(n,t),n}))}{const t=Qi(e);return r=rn("batches",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["operations"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>as(e)))),an(t,["batchJobs"],e)}return t}(e),n=new ni;return Object.assign(n,t),n}))}}async delete(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],ji(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("batchPredictionJobs/{name}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"DELETE",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>function(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["done"]);null!=o&&an(t,["done"],o);const i=ln(e,["error"]);return null!=i&&an(t,["error"],ls(i)),t}(e)))}{const t=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],ji(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("batches/{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"DELETE",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>function(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["done"]);return null!=o&&an(t,["done"],o),null!=ln(e,["error"])&&an(t,["error"],{}),t}(e)))}}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function fs(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["fileUri"]);null!=n&&an(t,["fileUri"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function hs(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>fs(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}function gs(e){const t={},n=ln(e,["timeRangeFilter"]);return null!=n&&an(t,["timeRangeFilter"],function(e){const t={},n=ln(e,["startTime"]);null!=n&&an(t,["startTime"],n);const o=ln(e,["endTime"]);return null!=o&&an(t,["endTime"],o),t}(n)),t}function vs(e){const t={},n=ln(e,["dynamicRetrievalConfig"]);return null!=n&&an(t,["dynamicRetrievalConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["dynamicThreshold"]);return null!=o&&an(t,["dynamicThreshold"],o),t}(n)),t}function ys(e){const t={},n=ln(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["behavior"]);null!=n&&an(t,["behavior"],n);const o=ln(e,["description"]);null!=o&&an(t,["description"],o);const i=ln(e,["name"]);null!=i&&an(t,["name"],i);const s=ln(e,["parameters"]);null!=s&&an(t,["parameters"],s);const r=ln(e,["parametersJsonSchema"]);null!=r&&an(t,["parametersJsonSchema"],r);const a=ln(e,["response"]);null!=a&&an(t,["response"],a);const l=ln(e,["responseJsonSchema"]);return null!=l&&an(t,["responseJsonSchema"],l),t}(e)))),an(t,["functionDeclarations"],e)}if(void 0!==ln(e,["retrieval"]))throw new Error("retrieval parameter is not supported in Gemini API.");const o=ln(e,["googleSearch"]);null!=o&&an(t,["googleSearch"],gs(o));const i=ln(e,["googleSearchRetrieval"]);if(null!=i&&an(t,["googleSearchRetrieval"],vs(i)),void 0!==ln(e,["enterpriseWebSearch"]))throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==ln(e,["googleMaps"]))throw new Error("googleMaps parameter is not supported in Gemini API.");null!=ln(e,["urlContext"])&&an(t,["urlContext"],{});const s=ln(e,["codeExecution"]);null!=s&&an(t,["codeExecution"],s);const r=ln(e,["computerUse"]);return null!=r&&an(t,["computerUse"],r),t}function _s(e){const t={},n=ln(e,["latLng"]);null!=n&&an(t,["latLng"],function(e){const t={},n=ln(e,["latitude"]);null!=n&&an(t,["latitude"],n);const o=ln(e,["longitude"]);return null!=o&&an(t,["longitude"],o),t}(n));const o=ln(e,["languageCode"]);return null!=o&&an(t,["languageCode"],o),t}function Es(e){const t={},n=ln(e,["functionCallingConfig"]);null!=n&&an(t,["functionCallingConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["allowedFunctionNames"]);return null!=o&&an(t,["allowedFunctionNames"],o),t}(n));const o=ln(e,["retrievalConfig"]);return null!=o&&an(t,["retrievalConfig"],_s(o)),t}function Cs(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["model"],ri(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],function(e,t){const n=ln(e,["ttl"]);void 0!==t&&null!=n&&an(t,["ttl"],n);const o=ln(e,["expireTime"]);void 0!==t&&null!=o&&an(t,["expireTime"],o);const i=ln(e,["displayName"]);void 0!==t&&null!=i&&an(t,["displayName"],i);const s=ln(e,["contents"]);if(void 0!==t&&null!=s){let e=yi(s);Array.isArray(e)&&(e=e.map((e=>hs(e)))),an(t,["contents"],e)}const r=ln(e,["systemInstruction"]);void 0!==t&&null!=r&&an(t,["systemInstruction"],hs(gi(r)));const a=ln(e,["tools"]);if(void 0!==t&&null!=a){let e=a;Array.isArray(e)&&(e=e.map((e=>ys(e)))),an(t,["tools"],e)}const l=ln(e,["toolConfig"]);if(void 0!==t&&null!=l&&an(t,["toolConfig"],Es(l)),void 0!==ln(e,["kmsKeyName"]))throw new Error("kmsKeyName parameter is not supported in Gemini API.");return{}}(i,n)),n}function bs(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],Oi(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],function(e,t){const n=ln(e,["ttl"]);void 0!==t&&null!=n&&an(t,["ttl"],n);const o=ln(e,["expireTime"]);return void 0!==t&&null!=o&&an(t,["expireTime"],o),{}}(i,n)),n}function Ts(e){const t={},n=ln(e,["config"]);return null!=n&&an(t,["config"],function(e,t){const n=ln(e,["pageSize"]);void 0!==t&&null!=n&&an(t,["_query","pageSize"],n);const o=ln(e,["pageToken"]);return void 0!==t&&null!=o&&an(t,["_query","pageToken"],o),{}}(n,t)),t}function Ss(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["data"]);null!=o&&an(t,["data"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["fileUri"]);null!=o&&an(t,["fileUri"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function ws(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Ss(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}function As(e){const t={},n=ln(e,["timeRangeFilter"]);return null!=n&&an(t,["timeRangeFilter"],function(e){const t={},n=ln(e,["startTime"]);null!=n&&an(t,["startTime"],n);const o=ln(e,["endTime"]);return null!=o&&an(t,["endTime"],o),t}(n)),t}function Is(e){const t={},n=ln(e,["dynamicRetrievalConfig"]);return null!=n&&an(t,["dynamicRetrievalConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["dynamicThreshold"]);return null!=o&&an(t,["dynamicThreshold"],o),t}(n)),t}function Os(e){const t={},n=ln(e,["apiKeyConfig"]);null!=n&&an(t,["apiKeyConfig"],function(e){const t={},n=ln(e,["apiKeyString"]);return null!=n&&an(t,["apiKeyString"],n),t}(n));const o=ln(e,["authType"]);null!=o&&an(t,["authType"],o);const i=ln(e,["googleServiceAccountConfig"]);null!=i&&an(t,["googleServiceAccountConfig"],i);const s=ln(e,["httpBasicAuthConfig"]);null!=s&&an(t,["httpBasicAuthConfig"],s);const r=ln(e,["oauthConfig"]);null!=r&&an(t,["oauthConfig"],r);const a=ln(e,["oidcConfig"]);return null!=a&&an(t,["oidcConfig"],a),t}function Ns(e){const t={},n=ln(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={};if(void 0!==ln(e,["behavior"]))throw new Error("behavior parameter is not supported in Vertex AI.");const n=ln(e,["description"]);null!=n&&an(t,["description"],n);const o=ln(e,["name"]);null!=o&&an(t,["name"],o);const i=ln(e,["parameters"]);null!=i&&an(t,["parameters"],i);const s=ln(e,["parametersJsonSchema"]);null!=s&&an(t,["parametersJsonSchema"],s);const r=ln(e,["response"]);null!=r&&an(t,["response"],r);const a=ln(e,["responseJsonSchema"]);return null!=a&&an(t,["responseJsonSchema"],a),t}(e)))),an(t,["functionDeclarations"],e)}const o=ln(e,["retrieval"]);null!=o&&an(t,["retrieval"],o);const i=ln(e,["googleSearch"]);null!=i&&an(t,["googleSearch"],As(i));const s=ln(e,["googleSearchRetrieval"]);null!=s&&an(t,["googleSearchRetrieval"],Is(s));null!=ln(e,["enterpriseWebSearch"])&&an(t,["enterpriseWebSearch"],{});const r=ln(e,["googleMaps"]);null!=r&&an(t,["googleMaps"],function(e){const t={},n=ln(e,["authConfig"]);return null!=n&&an(t,["authConfig"],Os(n)),t}(r));null!=ln(e,["urlContext"])&&an(t,["urlContext"],{});const a=ln(e,["codeExecution"]);null!=a&&an(t,["codeExecution"],a);const l=ln(e,["computerUse"]);return null!=l&&an(t,["computerUse"],l),t}function xs(e){const t={},n=ln(e,["latLng"]);null!=n&&an(t,["latLng"],function(e){const t={},n=ln(e,["latitude"]);null!=n&&an(t,["latitude"],n);const o=ln(e,["longitude"]);return null!=o&&an(t,["longitude"],o),t}(n));const o=ln(e,["languageCode"]);return null!=o&&an(t,["languageCode"],o),t}function ks(e){const t={},n=ln(e,["functionCallingConfig"]);null!=n&&an(t,["functionCallingConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["allowedFunctionNames"]);return null!=o&&an(t,["allowedFunctionNames"],o),t}(n));const o=ln(e,["retrievalConfig"]);return null!=o&&an(t,["retrievalConfig"],xs(o)),t}function Ps(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["model"],ri(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],function(e,t){const n=ln(e,["ttl"]);void 0!==t&&null!=n&&an(t,["ttl"],n);const o=ln(e,["expireTime"]);void 0!==t&&null!=o&&an(t,["expireTime"],o);const i=ln(e,["displayName"]);void 0!==t&&null!=i&&an(t,["displayName"],i);const s=ln(e,["contents"]);if(void 0!==t&&null!=s){let e=yi(s);Array.isArray(e)&&(e=e.map((e=>ws(e)))),an(t,["contents"],e)}const r=ln(e,["systemInstruction"]);void 0!==t&&null!=r&&an(t,["systemInstruction"],ws(gi(r)));const a=ln(e,["tools"]);if(void 0!==t&&null!=a){let e=a;Array.isArray(e)&&(e=e.map((e=>Ns(e)))),an(t,["tools"],e)}const l=ln(e,["toolConfig"]);void 0!==t&&null!=l&&an(t,["toolConfig"],ks(l));const c=ln(e,["kmsKeyName"]);return void 0!==t&&null!=c&&an(t,["encryption_spec","kmsKeyName"],c),{}}(i,n)),n}function Rs(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],Oi(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],function(e,t){const n=ln(e,["ttl"]);void 0!==t&&null!=n&&an(t,["ttl"],n);const o=ln(e,["expireTime"]);return void 0!==t&&null!=o&&an(t,["expireTime"],o),{}}(i,n)),n}function Ms(e){const t={},n=ln(e,["config"]);return null!=n&&an(t,["config"],function(e,t){const n=ln(e,["pageSize"]);void 0!==t&&null!=n&&an(t,["_query","pageSize"],n);const o=ln(e,["pageToken"]);return void 0!==t&&null!=o&&an(t,["_query","pageToken"],o),{}}(n,t)),t}function Ds(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["displayName"]);null!=o&&an(t,["displayName"],o);const i=ln(e,["model"]);null!=i&&an(t,["model"],i);const s=ln(e,["createTime"]);null!=s&&an(t,["createTime"],s);const r=ln(e,["updateTime"]);null!=r&&an(t,["updateTime"],r);const a=ln(e,["expireTime"]);null!=a&&an(t,["expireTime"],a);const l=ln(e,["usageMetadata"]);return null!=l&&an(t,["usageMetadata"],l),t}function Vs(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["displayName"]);null!=o&&an(t,["displayName"],o);const i=ln(e,["model"]);null!=i&&an(t,["model"],i);const s=ln(e,["createTime"]);null!=s&&an(t,["createTime"],s);const r=ln(e,["updateTime"]);null!=r&&an(t,["updateTime"],r);const a=ln(e,["expireTime"]);null!=a&&an(t,["expireTime"],a);const l=ln(e,["usageMetadata"]);return null!=l&&an(t,["usageMetadata"],l),t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */
class Us extends sn{constructor(e){super(),this.apiClient=e,this.list=async(e={})=>new ps(us.PAGED_ITEM_CACHED_CONTENTS,(e=>this.listInternal(e)),await this.listInternal(e),e)}async create(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=Ps(this.apiClient,e);return r=rn("cachedContents",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>Vs(e)))}{const t=Cs(this.apiClient,e);return r=rn("cachedContents",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>Ds(e)))}}async get(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],Oi(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("{name}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>Vs(e)))}{const t=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],Oi(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>Ds(e)))}}async delete(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],Oi(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("{name}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"DELETE",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((()=>{const e={},t=new zo;return Object.assign(t,e),t}))}{const t=function(e,t){const n={},o=ln(t,["name"]);null!=o&&an(n,["_url","name"],Oi(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"DELETE",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((()=>{const e={},t=new zo;return Object.assign(t,e),t}))}}async update(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=Rs(this.apiClient,e);return r=rn("{name}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"PATCH",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>Vs(e)))}{const t=bs(this.apiClient,e);return r=rn("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"PATCH",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>Ds(e)))}}async listInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=Ms(e);return r=rn("cachedContents",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["cachedContents"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>Vs(e)))),an(t,["cachedContents"],e)}return t}(e),n=new Xo;return Object.assign(n,t),n}))}{const t=Ts(e);return r=rn("cachedContents",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["cachedContents"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>Ds(e)))),an(t,["cachedContents"],e)}return t}(e),n=new Xo;return Object.assign(n,t),n}))}}}function Ls(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],o=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&o>=e.length&&(e=void 0),{value:e&&e[o++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function js(e){return this instanceof js?(this.v=e,this):new js(e)}function qs(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,i=n.apply(e,t||[]),s=[];return o=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),r("next"),r("throw"),r("return",(function(e){return function(t){return Promise.resolve(t).then(e,c)}})),o[Symbol.asyncIterator]=function(){return this},o;function r(e,t){i[e]&&(o[e]=function(t){return new Promise((function(n,o){s.push([e,t,n,o])>1||a(e,t)}))},t&&(o[e]=t(o[e])))}function a(e,t){try{(n=i[e](t)).value instanceof js?Promise.resolve(n.value.v).then(l,c):u(s[0][2],n)}catch(o){u(s[0][3],o)}var n}function l(e){a("next",e)}function c(e){a("throw",e)}function u(e,t){e(t),s.shift(),s.length&&a(s[0][0],s[0][1])}}function Fs(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t,n=e[Symbol.asyncIterator];return n?n.call(e):(e=Ls(e),t={},o("next"),o("throw"),o("return"),t[Symbol.asyncIterator]=function(){return this},t);function o(n){t[n]=e[n]&&function(t){return new Promise((function(o,i){(function(e,t,n,o){Promise.resolve(o).then((function(t){e({value:t,done:n})}),t)})(o,i,(t=e[n](t)).done,t.value)}))}}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */
function Bs(e){var t;if(null==e.candidates||0===e.candidates.length)return!1;const n=null===(t=e.candidates[0])||void 0===t?void 0:t.content;return void 0!==n&&Gs(n)}function Gs(e){if(void 0===e.parts||0===e.parts.length)return!1;for(const t of e.parts){if(void 0===t||0===Object.keys(t).length)return!1;if(!t.thought&&void 0!==t.text&&""===t.text)return!1}return!0}function $s(e){if(void 0===e||0===e.length)return[];const t=[],n=e.length;let o=0;for(;o<n;)if("user"===e[o].role)t.push(e[o]),o++;else{const i=[];let s=!0;for(;o<n&&"model"===e[o].role;)i.push(e[o]),s&&!Gs(e[o])&&(s=!1),o++;s?t.push(...i):t.pop()}return t}"function"==typeof SuppressedError&&SuppressedError;class Js{constructor(e,t){this.modelsModule=e,this.apiClient=t}create(e){return new Hs(this.apiClient,this.modelsModule,e.model,e.config,structuredClone(e.history))}}class Hs{constructor(e,t,n,o={},i=[]){this.apiClient=e,this.modelsModule=t,this.model=n,this.config=o,this.history=i,this.sendPromise=Promise.resolve(),function(e){if(0!==e.length)for(const t of e)if("user"!==t.role&&"model"!==t.role)throw new Error(`Role must be user or model, but got ${t.role}.`)}(i)}async sendMessage(e){var t;await this.sendPromise;const n=gi(e.message),o=this.modelsModule.generateContent({model:this.model,contents:this.getHistory(!0).concat(n),config:null!==(t=e.config)&&void 0!==t?t:this.config});return this.sendPromise=(async()=>{var e,t,i;const s=await o,r=null===(t=null===(e=s.candidates)||void 0===e?void 0:e[0])||void 0===t?void 0:t.content,a=s.automaticFunctionCallingHistory,l=this.getHistory(!0).length;let c=[];null!=a&&(c=null!==(i=a.slice(l))&&void 0!==i?i:[]);const u=r?[r]:[];this.recordHistory(n,u,c)})(),await this.sendPromise.catch((()=>{this.sendPromise=Promise.resolve()})),o}async sendMessageStream(e){var t;await this.sendPromise;const n=gi(e.message),o=this.modelsModule.generateContentStream({model:this.model,contents:this.getHistory(!0).concat(n),config:null!==(t=e.config)&&void 0!==t?t:this.config});this.sendPromise=o.then((()=>{})).catch((()=>{}));const i=await o;return this.processStreamResponse(i,n)}getHistory(e=!1){const t=e?$s(this.history):this.history;return structuredClone(t)}processStreamResponse(e,t){var n,o;return qs(this,arguments,(function*(){var i,s,r,a;const l=[];try{for(var c,u=!0,d=Fs(e);!(i=(c=yield js(d.next())).done);u=!0){a=c.value,u=!1;const e=a;if(Bs(e)){const t=null===(o=null===(n=e.candidates)||void 0===n?void 0:n[0])||void 0===o?void 0:o.content;void 0!==t&&l.push(t)}yield yield js(e)}}catch(p){s={error:p}}finally{try{u||i||!(r=d.return)||(yield js(r.call(d)))}finally{if(s)throw s.error}}this.recordHistory(t,l)}))}recordHistory(e,t,n){let o=[];t.length>0&&t.every((e=>void 0!==e.role))?o=t:o.push({role:"model",parts:[]}),n&&n.length>0?this.history.push(...$s(n)):this.history.push(e),this.history.push(...o)}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class Zs extends Error{constructor(e){super(e.message),this.name="ApiError",this.status=e.status,Object.setPrototypeOf(this,Zs.prototype)}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function Ys(e){const t={},n=ln(e,["config"]);return null!=n&&an(t,["config"],function(e,t){const n=ln(e,["pageSize"]);void 0!==t&&null!=n&&an(t,["_query","pageSize"],n);const o=ln(e,["pageToken"]);return void 0!==t&&null!=o&&an(t,["_query","pageToken"],o),{}}(n,t)),t}function Ks(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["displayName"]);null!=o&&an(t,["displayName"],o);const i=ln(e,["mimeType"]);null!=i&&an(t,["mimeType"],i);const s=ln(e,["sizeBytes"]);null!=s&&an(t,["sizeBytes"],s);const r=ln(e,["createTime"]);null!=r&&an(t,["createTime"],r);const a=ln(e,["expirationTime"]);null!=a&&an(t,["expirationTime"],a);const l=ln(e,["updateTime"]);null!=l&&an(t,["updateTime"],l);const c=ln(e,["sha256Hash"]);null!=c&&an(t,["sha256Hash"],c);const u=ln(e,["uri"]);null!=u&&an(t,["uri"],u);const d=ln(e,["downloadUri"]);null!=d&&an(t,["downloadUri"],d);const p=ln(e,["state"]);null!=p&&an(t,["state"],p);const m=ln(e,["source"]);null!=m&&an(t,["source"],m);const f=ln(e,["videoMetadata"]);null!=f&&an(t,["videoMetadata"],f);const h=ln(e,["error"]);return null!=h&&an(t,["error"],function(e){const t={},n=ln(e,["details"]);null!=n&&an(t,["details"],n);const o=ln(e,["message"]);null!=o&&an(t,["message"],o);const i=ln(e,["code"]);return null!=i&&an(t,["code"],i),t}(h)),t}function Ws(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["displayName"]);null!=o&&an(t,["displayName"],o);const i=ln(e,["mimeType"]);null!=i&&an(t,["mimeType"],i);const s=ln(e,["sizeBytes"]);null!=s&&an(t,["sizeBytes"],s);const r=ln(e,["createTime"]);null!=r&&an(t,["createTime"],r);const a=ln(e,["expirationTime"]);null!=a&&an(t,["expirationTime"],a);const l=ln(e,["updateTime"]);null!=l&&an(t,["updateTime"],l);const c=ln(e,["sha256Hash"]);null!=c&&an(t,["sha256Hash"],c);const u=ln(e,["uri"]);null!=u&&an(t,["uri"],u);const d=ln(e,["downloadUri"]);null!=d&&an(t,["downloadUri"],d);const p=ln(e,["state"]);null!=p&&an(t,["state"],p);const m=ln(e,["source"]);null!=m&&an(t,["source"],m);const f=ln(e,["videoMetadata"]);null!=f&&an(t,["videoMetadata"],f);const h=ln(e,["error"]);return null!=h&&an(t,["error"],function(e){const t={},n=ln(e,["details"]);null!=n&&an(t,["details"],n);const o=ln(e,["message"]);null!=o&&an(t,["message"],o);const i=ln(e,["code"]);return null!=i&&an(t,["code"],i),t}(h)),t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */
class zs extends sn{constructor(e){super(),this.apiClient=e,this.list=async(e={})=>new ps(us.PAGED_ITEM_FILES,(e=>this.listInternal(e)),await this.listInternal(e),e)}async upload(e){if(this.apiClient.isVertexAI())throw new Error("Vertex AI does not support uploading files. You can share files through a GCS bucket.");return this.apiClient.uploadFile(e.file,e.config).then((e=>Ws(e)))}async download(e){await this.apiClient.downloadFile(e)}async listInternal(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const r=Ys(e);return i=rn("files",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["files"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>Ws(e)))),an(t,["files"],e)}return t}(e),n=new Qo;return Object.assign(n,t),n}))}}async createInternal(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const r=function(e){const t={},n=ln(e,["file"]);null!=n&&an(t,["file"],Ks(n));const o=ln(e,["config"]);return null!=o&&an(t,["config"],o),t}(e);return i=rn("upload/v1beta/files",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((()=>{const e={},t=new ei;return Object.assign(t,e),t}))}}async get(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const r=function(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["_url","file"],ki(n));const o=ln(e,["config"]);return null!=o&&an(t,["config"],o),t}(e);return i=rn("files/{file}",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>Ws(e)))}}async delete(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const r=function(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["_url","file"],ki(n));const o=ln(e,["config"]);return null!=o&&an(t,["config"],o),t}(e);return i=rn("files/{file}",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"DELETE",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((()=>{const e={},t=new ti;return Object.assign(t,e),t}))}}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function Xs(e){const t={},n=ln(e,["prebuiltVoiceConfig"]);return null!=n&&an(t,["prebuiltVoiceConfig"],function(e){const t={},n=ln(e,["voiceName"]);return null!=n&&an(t,["voiceName"],n),t}(n)),t}function Qs(e){const t={},n=ln(e,["prebuiltVoiceConfig"]);return null!=n&&an(t,["prebuiltVoiceConfig"],function(e){const t={},n=ln(e,["voiceName"]);return null!=n&&an(t,["voiceName"],n),t}(n)),t}function er(e){const t={},n=ln(e,["speakerVoiceConfigs"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["speaker"]);null!=n&&an(t,["speaker"],n);const o=ln(e,["voiceConfig"]);return null!=o&&an(t,["voiceConfig"],Xs(o)),t}(e)))),an(t,["speakerVoiceConfigs"],e)}return t}function tr(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["fileUri"]);null!=n&&an(t,["fileUri"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function nr(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["data"]);null!=o&&an(t,["data"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["fileUri"]);null!=o&&an(t,["fileUri"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function or(e){const t={},n=ln(e,["timeRangeFilter"]);return null!=n&&an(t,["timeRangeFilter"],function(e){const t={},n=ln(e,["startTime"]);null!=n&&an(t,["startTime"],n);const o=ln(e,["endTime"]);return null!=o&&an(t,["endTime"],o),t}(n)),t}function ir(e){const t={},n=ln(e,["timeRangeFilter"]);return null!=n&&an(t,["timeRangeFilter"],function(e){const t={},n=ln(e,["startTime"]);null!=n&&an(t,["startTime"],n);const o=ln(e,["endTime"]);return null!=o&&an(t,["endTime"],o),t}(n)),t}function sr(e){const t={},n=ln(e,["dynamicRetrievalConfig"]);return null!=n&&an(t,["dynamicRetrievalConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["dynamicThreshold"]);return null!=o&&an(t,["dynamicThreshold"],o),t}(n)),t}function rr(e){const t={},n=ln(e,["dynamicRetrievalConfig"]);return null!=n&&an(t,["dynamicRetrievalConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["dynamicThreshold"]);return null!=o&&an(t,["dynamicThreshold"],o),t}(n)),t}function ar(e){const t={},n=ln(e,["apiKeyConfig"]);null!=n&&an(t,["apiKeyConfig"],function(e){const t={},n=ln(e,["apiKeyString"]);return null!=n&&an(t,["apiKeyString"],n),t}(n));const o=ln(e,["authType"]);null!=o&&an(t,["authType"],o);const i=ln(e,["googleServiceAccountConfig"]);null!=i&&an(t,["googleServiceAccountConfig"],i);const s=ln(e,["httpBasicAuthConfig"]);null!=s&&an(t,["httpBasicAuthConfig"],s);const r=ln(e,["oauthConfig"]);null!=r&&an(t,["oauthConfig"],r);const a=ln(e,["oidcConfig"]);return null!=a&&an(t,["oidcConfig"],a),t}function lr(e){const t={},n=ln(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["behavior"]);null!=n&&an(t,["behavior"],n);const o=ln(e,["description"]);null!=o&&an(t,["description"],o);const i=ln(e,["name"]);null!=i&&an(t,["name"],i);const s=ln(e,["parameters"]);null!=s&&an(t,["parameters"],s);const r=ln(e,["parametersJsonSchema"]);null!=r&&an(t,["parametersJsonSchema"],r);const a=ln(e,["response"]);null!=a&&an(t,["response"],a);const l=ln(e,["responseJsonSchema"]);return null!=l&&an(t,["responseJsonSchema"],l),t}(e)))),an(t,["functionDeclarations"],e)}if(void 0!==ln(e,["retrieval"]))throw new Error("retrieval parameter is not supported in Gemini API.");const o=ln(e,["googleSearch"]);null!=o&&an(t,["googleSearch"],or(o));const i=ln(e,["googleSearchRetrieval"]);if(null!=i&&an(t,["googleSearchRetrieval"],sr(i)),void 0!==ln(e,["enterpriseWebSearch"]))throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==ln(e,["googleMaps"]))throw new Error("googleMaps parameter is not supported in Gemini API.");null!=ln(e,["urlContext"])&&an(t,["urlContext"],{});const s=ln(e,["codeExecution"]);null!=s&&an(t,["codeExecution"],s);const r=ln(e,["computerUse"]);return null!=r&&an(t,["computerUse"],r),t}function cr(e){const t={},n=ln(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={};if(void 0!==ln(e,["behavior"]))throw new Error("behavior parameter is not supported in Vertex AI.");const n=ln(e,["description"]);null!=n&&an(t,["description"],n);const o=ln(e,["name"]);null!=o&&an(t,["name"],o);const i=ln(e,["parameters"]);null!=i&&an(t,["parameters"],i);const s=ln(e,["parametersJsonSchema"]);null!=s&&an(t,["parametersJsonSchema"],s);const r=ln(e,["response"]);null!=r&&an(t,["response"],r);const a=ln(e,["responseJsonSchema"]);return null!=a&&an(t,["responseJsonSchema"],a),t}(e)))),an(t,["functionDeclarations"],e)}const o=ln(e,["retrieval"]);null!=o&&an(t,["retrieval"],o);const i=ln(e,["googleSearch"]);null!=i&&an(t,["googleSearch"],ir(i));const s=ln(e,["googleSearchRetrieval"]);null!=s&&an(t,["googleSearchRetrieval"],rr(s));null!=ln(e,["enterpriseWebSearch"])&&an(t,["enterpriseWebSearch"],{});const r=ln(e,["googleMaps"]);null!=r&&an(t,["googleMaps"],function(e){const t={},n=ln(e,["authConfig"]);return null!=n&&an(t,["authConfig"],ar(n)),t}(r));null!=ln(e,["urlContext"])&&an(t,["urlContext"],{});const a=ln(e,["codeExecution"]);null!=a&&an(t,["codeExecution"],a);const l=ln(e,["computerUse"]);return null!=l&&an(t,["computerUse"],l),t}function ur(e){const t={},n=ln(e,["automaticActivityDetection"]);null!=n&&an(t,["automaticActivityDetection"],function(e){const t={},n=ln(e,["disabled"]);null!=n&&an(t,["disabled"],n);const o=ln(e,["startOfSpeechSensitivity"]);null!=o&&an(t,["startOfSpeechSensitivity"],o);const i=ln(e,["endOfSpeechSensitivity"]);null!=i&&an(t,["endOfSpeechSensitivity"],i);const s=ln(e,["prefixPaddingMs"]);null!=s&&an(t,["prefixPaddingMs"],s);const r=ln(e,["silenceDurationMs"]);return null!=r&&an(t,["silenceDurationMs"],r),t}(n));const o=ln(e,["activityHandling"]);null!=o&&an(t,["activityHandling"],o);const i=ln(e,["turnCoverage"]);return null!=i&&an(t,["turnCoverage"],i),t}function dr(e){const t={},n=ln(e,["automaticActivityDetection"]);null!=n&&an(t,["automaticActivityDetection"],function(e){const t={},n=ln(e,["disabled"]);null!=n&&an(t,["disabled"],n);const o=ln(e,["startOfSpeechSensitivity"]);null!=o&&an(t,["startOfSpeechSensitivity"],o);const i=ln(e,["endOfSpeechSensitivity"]);null!=i&&an(t,["endOfSpeechSensitivity"],i);const s=ln(e,["prefixPaddingMs"]);null!=s&&an(t,["prefixPaddingMs"],s);const r=ln(e,["silenceDurationMs"]);return null!=r&&an(t,["silenceDurationMs"],r),t}(n));const o=ln(e,["activityHandling"]);null!=o&&an(t,["activityHandling"],o);const i=ln(e,["turnCoverage"]);return null!=i&&an(t,["turnCoverage"],i),t}function pr(e){const t={},n=ln(e,["triggerTokens"]);null!=n&&an(t,["triggerTokens"],n);const o=ln(e,["slidingWindow"]);return null!=o&&an(t,["slidingWindow"],function(e){const t={},n=ln(e,["targetTokens"]);return null!=n&&an(t,["targetTokens"],n),t}(o)),t}function mr(e){const t={},n=ln(e,["triggerTokens"]);null!=n&&an(t,["triggerTokens"],n);const o=ln(e,["slidingWindow"]);return null!=o&&an(t,["slidingWindow"],function(e){const t={},n=ln(e,["targetTokens"]);return null!=n&&an(t,["targetTokens"],n),t}(o)),t}function fr(e,t){const n=ln(e,["generationConfig"]);void 0!==t&&null!=n&&an(t,["setup","generationConfig"],n);const o=ln(e,["responseModalities"]);void 0!==t&&null!=o&&an(t,["setup","generationConfig","responseModalities"],o);const i=ln(e,["temperature"]);void 0!==t&&null!=i&&an(t,["setup","generationConfig","temperature"],i);const s=ln(e,["topP"]);void 0!==t&&null!=s&&an(t,["setup","generationConfig","topP"],s);const r=ln(e,["topK"]);void 0!==t&&null!=r&&an(t,["setup","generationConfig","topK"],r);const a=ln(e,["maxOutputTokens"]);void 0!==t&&null!=a&&an(t,["setup","generationConfig","maxOutputTokens"],a);const l=ln(e,["mediaResolution"]);void 0!==t&&null!=l&&an(t,["setup","generationConfig","mediaResolution"],l);const c=ln(e,["seed"]);void 0!==t&&null!=c&&an(t,["setup","generationConfig","seed"],c);const u=ln(e,["speechConfig"]);void 0!==t&&null!=u&&an(t,["setup","generationConfig","speechConfig"],function(e){const t={},n=ln(e,["voiceConfig"]);null!=n&&an(t,["voiceConfig"],Xs(n));const o=ln(e,["multiSpeakerVoiceConfig"]);null!=o&&an(t,["multiSpeakerVoiceConfig"],er(o));const i=ln(e,["languageCode"]);return null!=i&&an(t,["languageCode"],i),t}(wi(u)));const d=ln(e,["enableAffectiveDialog"]);void 0!==t&&null!=d&&an(t,["setup","generationConfig","enableAffectiveDialog"],d);const p=ln(e,["systemInstruction"]);void 0!==t&&null!=p&&an(t,["setup","systemInstruction"],function(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>tr(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}(gi(p)));const m=ln(e,["tools"]);if(void 0!==t&&null!=m){let e=Ii(m);Array.isArray(e)&&(e=e.map((e=>lr(Ai(e))))),an(t,["setup","tools"],e)}const f=ln(e,["sessionResumption"]);void 0!==t&&null!=f&&an(t,["setup","sessionResumption"],function(e){const t={},n=ln(e,["handle"]);if(null!=n&&an(t,["handle"],n),void 0!==ln(e,["transparent"]))throw new Error("transparent parameter is not supported in Gemini API.");return t}(f));const h=ln(e,["inputAudioTranscription"]);void 0!==t&&null!=h&&an(t,["setup","inputAudioTranscription"],{});const g=ln(e,["outputAudioTranscription"]);void 0!==t&&null!=g&&an(t,["setup","outputAudioTranscription"],{});const v=ln(e,["realtimeInputConfig"]);void 0!==t&&null!=v&&an(t,["setup","realtimeInputConfig"],ur(v));const y=ln(e,["contextWindowCompression"]);void 0!==t&&null!=y&&an(t,["setup","contextWindowCompression"],pr(y));const _=ln(e,["proactivity"]);return void 0!==t&&null!=_&&an(t,["setup","proactivity"],function(e){const t={},n=ln(e,["proactiveAudio"]);return null!=n&&an(t,["proactiveAudio"],n),t}(_)),{}}function hr(e,t){const n=ln(e,["generationConfig"]);void 0!==t&&null!=n&&an(t,["setup","generationConfig"],n);const o=ln(e,["responseModalities"]);void 0!==t&&null!=o&&an(t,["setup","generationConfig","responseModalities"],o);const i=ln(e,["temperature"]);void 0!==t&&null!=i&&an(t,["setup","generationConfig","temperature"],i);const s=ln(e,["topP"]);void 0!==t&&null!=s&&an(t,["setup","generationConfig","topP"],s);const r=ln(e,["topK"]);void 0!==t&&null!=r&&an(t,["setup","generationConfig","topK"],r);const a=ln(e,["maxOutputTokens"]);void 0!==t&&null!=a&&an(t,["setup","generationConfig","maxOutputTokens"],a);const l=ln(e,["mediaResolution"]);void 0!==t&&null!=l&&an(t,["setup","generationConfig","mediaResolution"],l);const c=ln(e,["seed"]);void 0!==t&&null!=c&&an(t,["setup","generationConfig","seed"],c);const u=ln(e,["speechConfig"]);void 0!==t&&null!=u&&an(t,["setup","generationConfig","speechConfig"],function(e){const t={},n=ln(e,["voiceConfig"]);if(null!=n&&an(t,["voiceConfig"],Qs(n)),void 0!==ln(e,["multiSpeakerVoiceConfig"]))throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const o=ln(e,["languageCode"]);return null!=o&&an(t,["languageCode"],o),t}(wi(u)));const d=ln(e,["enableAffectiveDialog"]);void 0!==t&&null!=d&&an(t,["setup","generationConfig","enableAffectiveDialog"],d);const p=ln(e,["systemInstruction"]);void 0!==t&&null!=p&&an(t,["setup","systemInstruction"],function(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>nr(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}(gi(p)));const m=ln(e,["tools"]);if(void 0!==t&&null!=m){let e=Ii(m);Array.isArray(e)&&(e=e.map((e=>cr(Ai(e))))),an(t,["setup","tools"],e)}const f=ln(e,["sessionResumption"]);void 0!==t&&null!=f&&an(t,["setup","sessionResumption"],function(e){const t={},n=ln(e,["handle"]);null!=n&&an(t,["handle"],n);const o=ln(e,["transparent"]);return null!=o&&an(t,["transparent"],o),t}(f));const h=ln(e,["inputAudioTranscription"]);void 0!==t&&null!=h&&an(t,["setup","inputAudioTranscription"],{});const g=ln(e,["outputAudioTranscription"]);void 0!==t&&null!=g&&an(t,["setup","outputAudioTranscription"],{});const v=ln(e,["realtimeInputConfig"]);void 0!==t&&null!=v&&an(t,["setup","realtimeInputConfig"],dr(v));const y=ln(e,["contextWindowCompression"]);void 0!==t&&null!=y&&an(t,["setup","contextWindowCompression"],mr(y));const _=ln(e,["proactivity"]);return void 0!==t&&null!=_&&an(t,["setup","proactivity"],function(e){const t={},n=ln(e,["proactiveAudio"]);return null!=n&&an(t,["proactiveAudio"],n),t}(_)),{}}function gr(e){const t={},n=ln(e,["media"]);null!=n&&an(t,["mediaChunks"],ai(n));const o=ln(e,["audio"]);null!=o&&an(t,["audio"],ui(o));const i=ln(e,["audioStreamEnd"]);null!=i&&an(t,["audioStreamEnd"],i);const s=ln(e,["video"]);null!=s&&an(t,["video"],ci(s));const r=ln(e,["text"]);null!=r&&an(t,["text"],r);null!=ln(e,["activityStart"])&&an(t,["activityStart"],{});return null!=ln(e,["activityEnd"])&&an(t,["activityEnd"],{}),t}function vr(e){const t={},n=ln(e,["media"]);null!=n&&an(t,["mediaChunks"],ai(n));const o=ln(e,["audio"]);null!=o&&an(t,["audio"],ui(o));const i=ln(e,["audioStreamEnd"]);null!=i&&an(t,["audioStreamEnd"],i);const s=ln(e,["video"]);null!=s&&an(t,["video"],ci(s));const r=ln(e,["text"]);null!=r&&an(t,["text"],r);null!=ln(e,["activityStart"])&&an(t,["activityStart"],{});return null!=ln(e,["activityEnd"])&&an(t,["activityEnd"],{}),t}function yr(e){const t={},n=ln(e,["text"]);null!=n&&an(t,["text"],n);const o=ln(e,["weight"]);return null!=o&&an(t,["weight"],o),t}function _r(e){const t={},n=ln(e,["temperature"]);null!=n&&an(t,["temperature"],n);const o=ln(e,["topK"]);null!=o&&an(t,["topK"],o);const i=ln(e,["seed"]);null!=i&&an(t,["seed"],i);const s=ln(e,["guidance"]);null!=s&&an(t,["guidance"],s);const r=ln(e,["bpm"]);null!=r&&an(t,["bpm"],r);const a=ln(e,["density"]);null!=a&&an(t,["density"],a);const l=ln(e,["brightness"]);null!=l&&an(t,["brightness"],l);const c=ln(e,["scale"]);null!=c&&an(t,["scale"],c);const u=ln(e,["muteBass"]);null!=u&&an(t,["muteBass"],u);const d=ln(e,["muteDrums"]);null!=d&&an(t,["muteDrums"],d);const p=ln(e,["onlyBassAndDrums"]);return null!=p&&an(t,["onlyBassAndDrums"],p),t}function Er(e){const t={},n=ln(e,["model"]);return null!=n&&an(t,["model"],n),t}function Cr(e){const t={},n=ln(e,["weightedPrompts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>yr(e)))),an(t,["weightedPrompts"],e)}return t}function br(e){const t={},n=ln(e,["setup"]);null!=n&&an(t,["setup"],Er(n));const o=ln(e,["clientContent"]);null!=o&&an(t,["clientContent"],Cr(o));const i=ln(e,["musicGenerationConfig"]);null!=i&&an(t,["musicGenerationConfig"],_r(i));const s=ln(e,["playbackControl"]);return null!=s&&an(t,["playbackControl"],s),t}function Tr(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={},n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={},n=ln(e,["fileUri"]);null!=n&&an(t,["fileUri"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function Sr(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["data"]);null!=o&&an(t,["data"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["fileUri"]);null!=o&&an(t,["fileUri"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function wr(e){const t={},n=ln(e,["text"]);null!=n&&an(t,["text"],n);const o=ln(e,["finished"]);return null!=o&&an(t,["finished"],o),t}function Ar(e){const t={},n=ln(e,["text"]);null!=n&&an(t,["text"],n);const o=ln(e,["finished"]);return null!=o&&an(t,["finished"],o),t}function Ir(e){const t={},n=ln(e,["urlMetadata"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["retrievedUrl"]);null!=n&&an(t,["retrievedUrl"],n);const o=ln(e,["urlRetrievalStatus"]);return null!=o&&an(t,["urlRetrievalStatus"],o),t}(e)))),an(t,["urlMetadata"],e)}return t}function Or(e){const t={},n=ln(e,["modelTurn"]);null!=n&&an(t,["modelTurn"],function(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Tr(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}(n));const o=ln(e,["turnComplete"]);null!=o&&an(t,["turnComplete"],o);const i=ln(e,["interrupted"]);null!=i&&an(t,["interrupted"],i);const s=ln(e,["groundingMetadata"]);null!=s&&an(t,["groundingMetadata"],s);const r=ln(e,["generationComplete"]);null!=r&&an(t,["generationComplete"],r);const a=ln(e,["inputTranscription"]);null!=a&&an(t,["inputTranscription"],wr(a));const l=ln(e,["outputTranscription"]);null!=l&&an(t,["outputTranscription"],wr(l));const c=ln(e,["urlContextMetadata"]);return null!=c&&an(t,["urlContextMetadata"],Ir(c)),t}function Nr(e){const t={},n=ln(e,["modelTurn"]);null!=n&&an(t,["modelTurn"],function(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Sr(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}(n));const o=ln(e,["turnComplete"]);null!=o&&an(t,["turnComplete"],o);const i=ln(e,["interrupted"]);null!=i&&an(t,["interrupted"],i);const s=ln(e,["groundingMetadata"]);null!=s&&an(t,["groundingMetadata"],s);const r=ln(e,["generationComplete"]);null!=r&&an(t,["generationComplete"],r);const a=ln(e,["inputTranscription"]);null!=a&&an(t,["inputTranscription"],Ar(a));const l=ln(e,["outputTranscription"]);return null!=l&&an(t,["outputTranscription"],Ar(l)),t}function xr(e){const t={},n=ln(e,["functionCalls"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["id"]);null!=n&&an(t,["id"],n);const o=ln(e,["args"]);null!=o&&an(t,["args"],o);const i=ln(e,["name"]);return null!=i&&an(t,["name"],i),t}(e)))),an(t,["functionCalls"],e)}return t}function kr(e){const t={},n=ln(e,["functionCalls"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["args"]);null!=n&&an(t,["args"],n);const o=ln(e,["name"]);return null!=o&&an(t,["name"],o),t}(e)))),an(t,["functionCalls"],e)}return t}function Pr(e){const t={},n=ln(e,["modality"]);null!=n&&an(t,["modality"],n);const o=ln(e,["tokenCount"]);return null!=o&&an(t,["tokenCount"],o),t}function Rr(e){const t={},n=ln(e,["modality"]);null!=n&&an(t,["modality"],n);const o=ln(e,["tokenCount"]);return null!=o&&an(t,["tokenCount"],o),t}function Mr(e){const t={};null!=ln(e,["setupComplete"])&&an(t,["setupComplete"],{});const n=ln(e,["serverContent"]);null!=n&&an(t,["serverContent"],Or(n));const o=ln(e,["toolCall"]);null!=o&&an(t,["toolCall"],xr(o));const i=ln(e,["toolCallCancellation"]);null!=i&&an(t,["toolCallCancellation"],function(e){const t={},n=ln(e,["ids"]);return null!=n&&an(t,["ids"],n),t}(i));const s=ln(e,["usageMetadata"]);null!=s&&an(t,["usageMetadata"],function(e){const t={},n=ln(e,["promptTokenCount"]);null!=n&&an(t,["promptTokenCount"],n);const o=ln(e,["cachedContentTokenCount"]);null!=o&&an(t,["cachedContentTokenCount"],o);const i=ln(e,["responseTokenCount"]);null!=i&&an(t,["responseTokenCount"],i);const s=ln(e,["toolUsePromptTokenCount"]);null!=s&&an(t,["toolUsePromptTokenCount"],s);const r=ln(e,["thoughtsTokenCount"]);null!=r&&an(t,["thoughtsTokenCount"],r);const a=ln(e,["totalTokenCount"]);null!=a&&an(t,["totalTokenCount"],a);const l=ln(e,["promptTokensDetails"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map((e=>Pr(e)))),an(t,["promptTokensDetails"],e)}const c=ln(e,["cacheTokensDetails"]);if(null!=c){let e=c;Array.isArray(e)&&(e=e.map((e=>Pr(e)))),an(t,["cacheTokensDetails"],e)}const u=ln(e,["responseTokensDetails"]);if(null!=u){let e=u;Array.isArray(e)&&(e=e.map((e=>Pr(e)))),an(t,["responseTokensDetails"],e)}const d=ln(e,["toolUsePromptTokensDetails"]);if(null!=d){let e=d;Array.isArray(e)&&(e=e.map((e=>Pr(e)))),an(t,["toolUsePromptTokensDetails"],e)}return t}(s));const r=ln(e,["goAway"]);null!=r&&an(t,["goAway"],function(e){const t={},n=ln(e,["timeLeft"]);return null!=n&&an(t,["timeLeft"],n),t}(r));const a=ln(e,["sessionResumptionUpdate"]);return null!=a&&an(t,["sessionResumptionUpdate"],function(e){const t={},n=ln(e,["newHandle"]);null!=n&&an(t,["newHandle"],n);const o=ln(e,["resumable"]);null!=o&&an(t,["resumable"],o);const i=ln(e,["lastConsumedClientMessageIndex"]);return null!=i&&an(t,["lastConsumedClientMessageIndex"],i),t}(a)),t}function Dr(e){const t={},n=ln(e,["setupComplete"]);null!=n&&an(t,["setupComplete"],function(e){const t={},n=ln(e,["sessionId"]);return null!=n&&an(t,["sessionId"],n),t}(n));const o=ln(e,["serverContent"]);null!=o&&an(t,["serverContent"],Nr(o));const i=ln(e,["toolCall"]);null!=i&&an(t,["toolCall"],kr(i));const s=ln(e,["toolCallCancellation"]);null!=s&&an(t,["toolCallCancellation"],function(e){const t={},n=ln(e,["ids"]);return null!=n&&an(t,["ids"],n),t}(s));const r=ln(e,["usageMetadata"]);null!=r&&an(t,["usageMetadata"],function(e){const t={},n=ln(e,["promptTokenCount"]);null!=n&&an(t,["promptTokenCount"],n);const o=ln(e,["cachedContentTokenCount"]);null!=o&&an(t,["cachedContentTokenCount"],o);const i=ln(e,["candidatesTokenCount"]);null!=i&&an(t,["responseTokenCount"],i);const s=ln(e,["toolUsePromptTokenCount"]);null!=s&&an(t,["toolUsePromptTokenCount"],s);const r=ln(e,["thoughtsTokenCount"]);null!=r&&an(t,["thoughtsTokenCount"],r);const a=ln(e,["totalTokenCount"]);null!=a&&an(t,["totalTokenCount"],a);const l=ln(e,["promptTokensDetails"]);if(null!=l){let e=l;Array.isArray(e)&&(e=e.map((e=>Rr(e)))),an(t,["promptTokensDetails"],e)}const c=ln(e,["cacheTokensDetails"]);if(null!=c){let e=c;Array.isArray(e)&&(e=e.map((e=>Rr(e)))),an(t,["cacheTokensDetails"],e)}const u=ln(e,["candidatesTokensDetails"]);if(null!=u){let e=u;Array.isArray(e)&&(e=e.map((e=>Rr(e)))),an(t,["responseTokensDetails"],e)}const d=ln(e,["toolUsePromptTokensDetails"]);if(null!=d){let e=d;Array.isArray(e)&&(e=e.map((e=>Rr(e)))),an(t,["toolUsePromptTokensDetails"],e)}const p=ln(e,["trafficType"]);return null!=p&&an(t,["trafficType"],p),t}(r));const a=ln(e,["goAway"]);null!=a&&an(t,["goAway"],function(e){const t={},n=ln(e,["timeLeft"]);return null!=n&&an(t,["timeLeft"],n),t}(a));const l=ln(e,["sessionResumptionUpdate"]);return null!=l&&an(t,["sessionResumptionUpdate"],function(e){const t={},n=ln(e,["newHandle"]);null!=n&&an(t,["newHandle"],n);const o=ln(e,["resumable"]);null!=o&&an(t,["resumable"],o);const i=ln(e,["lastConsumedClientMessageIndex"]);return null!=i&&an(t,["lastConsumedClientMessageIndex"],i),t}(l)),t}function Vr(e){const t={},n=ln(e,["weightedPrompts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["text"]);null!=n&&an(t,["text"],n);const o=ln(e,["weight"]);return null!=o&&an(t,["weight"],o),t}(e)))),an(t,["weightedPrompts"],e)}return t}function Ur(e){const t={},n=ln(e,["clientContent"]);null!=n&&an(t,["clientContent"],Vr(n));const o=ln(e,["musicGenerationConfig"]);return null!=o&&an(t,["musicGenerationConfig"],function(e){const t={},n=ln(e,["temperature"]);null!=n&&an(t,["temperature"],n);const o=ln(e,["topK"]);null!=o&&an(t,["topK"],o);const i=ln(e,["seed"]);null!=i&&an(t,["seed"],i);const s=ln(e,["guidance"]);null!=s&&an(t,["guidance"],s);const r=ln(e,["bpm"]);null!=r&&an(t,["bpm"],r);const a=ln(e,["density"]);null!=a&&an(t,["density"],a);const l=ln(e,["brightness"]);null!=l&&an(t,["brightness"],l);const c=ln(e,["scale"]);null!=c&&an(t,["scale"],c);const u=ln(e,["muteBass"]);null!=u&&an(t,["muteBass"],u);const d=ln(e,["muteDrums"]);null!=d&&an(t,["muteDrums"],d);const p=ln(e,["onlyBassAndDrums"]);return null!=p&&an(t,["onlyBassAndDrums"],p),t}(o)),t}function Lr(e){const t={},n=ln(e,["audioChunks"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);null!=o&&an(t,["mimeType"],o);const i=ln(e,["sourceMetadata"]);return null!=i&&an(t,["sourceMetadata"],Ur(i)),t}(e)))),an(t,["audioChunks"],e)}return t}function jr(e){const t={};null!=ln(e,["setupComplete"])&&an(t,["setupComplete"],{});const n=ln(e,["serverContent"]);null!=n&&an(t,["serverContent"],Lr(n));const o=ln(e,["filteredPrompt"]);return null!=o&&an(t,["filteredPrompt"],function(e){const t={},n=ln(e,["text"]);null!=n&&an(t,["text"],n);const o=ln(e,["filteredReason"]);return null!=o&&an(t,["filteredReason"],o),t}(o)),t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function qr(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["fileUri"]);null!=n&&an(t,["fileUri"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function Fr(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>qr(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}function Br(e){const t={},n=ln(e,["timeRangeFilter"]);return null!=n&&an(t,["timeRangeFilter"],function(e){const t={},n=ln(e,["startTime"]);null!=n&&an(t,["startTime"],n);const o=ln(e,["endTime"]);return null!=o&&an(t,["endTime"],o),t}(n)),t}function Gr(e){const t={},n=ln(e,["dynamicRetrievalConfig"]);return null!=n&&an(t,["dynamicRetrievalConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["dynamicThreshold"]);return null!=o&&an(t,["dynamicThreshold"],o),t}(n)),t}function $r(e){const t={},n=ln(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["behavior"]);null!=n&&an(t,["behavior"],n);const o=ln(e,["description"]);null!=o&&an(t,["description"],o);const i=ln(e,["name"]);null!=i&&an(t,["name"],i);const s=ln(e,["parameters"]);null!=s&&an(t,["parameters"],s);const r=ln(e,["parametersJsonSchema"]);null!=r&&an(t,["parametersJsonSchema"],r);const a=ln(e,["response"]);null!=a&&an(t,["response"],a);const l=ln(e,["responseJsonSchema"]);return null!=l&&an(t,["responseJsonSchema"],l),t}(e)))),an(t,["functionDeclarations"],e)}if(void 0!==ln(e,["retrieval"]))throw new Error("retrieval parameter is not supported in Gemini API.");const o=ln(e,["googleSearch"]);null!=o&&an(t,["googleSearch"],Br(o));const i=ln(e,["googleSearchRetrieval"]);if(null!=i&&an(t,["googleSearchRetrieval"],Gr(i)),void 0!==ln(e,["enterpriseWebSearch"]))throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==ln(e,["googleMaps"]))throw new Error("googleMaps parameter is not supported in Gemini API.");null!=ln(e,["urlContext"])&&an(t,["urlContext"],{});const s=ln(e,["codeExecution"]);null!=s&&an(t,["codeExecution"],s);const r=ln(e,["computerUse"]);return null!=r&&an(t,["computerUse"],r),t}function Jr(e){const t={},n=ln(e,["latLng"]);null!=n&&an(t,["latLng"],function(e){const t={},n=ln(e,["latitude"]);null!=n&&an(t,["latitude"],n);const o=ln(e,["longitude"]);return null!=o&&an(t,["longitude"],o),t}(n));const o=ln(e,["languageCode"]);return null!=o&&an(t,["languageCode"],o),t}function Hr(e){const t={},n=ln(e,["functionCallingConfig"]);null!=n&&an(t,["functionCallingConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["allowedFunctionNames"]);return null!=o&&an(t,["allowedFunctionNames"],o),t}(n));const o=ln(e,["retrievalConfig"]);return null!=o&&an(t,["retrievalConfig"],Jr(o)),t}function Zr(e){const t={},n=ln(e,["prebuiltVoiceConfig"]);return null!=n&&an(t,["prebuiltVoiceConfig"],function(e){const t={},n=ln(e,["voiceName"]);return null!=n&&an(t,["voiceName"],n),t}(n)),t}function Yr(e){const t={},n=ln(e,["speakerVoiceConfigs"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["speaker"]);null!=n&&an(t,["speaker"],n);const o=ln(e,["voiceConfig"]);return null!=o&&an(t,["voiceConfig"],Zr(o)),t}(e)))),an(t,["speakerVoiceConfigs"],e)}return t}function Kr(e,t,n){const o={},i=ln(t,["systemInstruction"]);void 0!==n&&null!=i&&an(n,["systemInstruction"],Fr(gi(i)));const s=ln(t,["temperature"]);null!=s&&an(o,["temperature"],s);const r=ln(t,["topP"]);null!=r&&an(o,["topP"],r);const a=ln(t,["topK"]);null!=a&&an(o,["topK"],a);const l=ln(t,["candidateCount"]);null!=l&&an(o,["candidateCount"],l);const c=ln(t,["maxOutputTokens"]);null!=c&&an(o,["maxOutputTokens"],c);const u=ln(t,["stopSequences"]);null!=u&&an(o,["stopSequences"],u);const d=ln(t,["responseLogprobs"]);null!=d&&an(o,["responseLogprobs"],d);const p=ln(t,["logprobs"]);null!=p&&an(o,["logprobs"],p);const m=ln(t,["presencePenalty"]);null!=m&&an(o,["presencePenalty"],m);const f=ln(t,["frequencyPenalty"]);null!=f&&an(o,["frequencyPenalty"],f);const h=ln(t,["seed"]);null!=h&&an(o,["seed"],h);const g=ln(t,["responseMimeType"]);null!=g&&an(o,["responseMimeType"],g);const v=ln(t,["responseSchema"]);null!=v&&an(o,["responseSchema"],function(e){const t={},n=ln(e,["anyOf"]);null!=n&&an(t,["anyOf"],n);const o=ln(e,["default"]);null!=o&&an(t,["default"],o);const i=ln(e,["description"]);null!=i&&an(t,["description"],i);const s=ln(e,["enum"]);null!=s&&an(t,["enum"],s);const r=ln(e,["example"]);null!=r&&an(t,["example"],r);const a=ln(e,["format"]);null!=a&&an(t,["format"],a);const l=ln(e,["items"]);null!=l&&an(t,["items"],l);const c=ln(e,["maxItems"]);null!=c&&an(t,["maxItems"],c);const u=ln(e,["maxLength"]);null!=u&&an(t,["maxLength"],u);const d=ln(e,["maxProperties"]);null!=d&&an(t,["maxProperties"],d);const p=ln(e,["maximum"]);null!=p&&an(t,["maximum"],p);const m=ln(e,["minItems"]);null!=m&&an(t,["minItems"],m);const f=ln(e,["minLength"]);null!=f&&an(t,["minLength"],f);const h=ln(e,["minProperties"]);null!=h&&an(t,["minProperties"],h);const g=ln(e,["minimum"]);null!=g&&an(t,["minimum"],g);const v=ln(e,["nullable"]);null!=v&&an(t,["nullable"],v);const y=ln(e,["pattern"]);null!=y&&an(t,["pattern"],y);const _=ln(e,["properties"]);null!=_&&an(t,["properties"],_);const E=ln(e,["propertyOrdering"]);null!=E&&an(t,["propertyOrdering"],E);const C=ln(e,["required"]);null!=C&&an(t,["required"],C);const b=ln(e,["title"]);null!=b&&an(t,["title"],b);const T=ln(e,["type"]);return null!=T&&an(t,["type"],T),t}(Ti(v)));const y=ln(t,["responseJsonSchema"]);if(null!=y&&an(o,["responseJsonSchema"],y),void 0!==ln(t,["routingConfig"]))throw new Error("routingConfig parameter is not supported in Gemini API.");if(void 0!==ln(t,["modelSelectionConfig"]))throw new Error("modelSelectionConfig parameter is not supported in Gemini API.");const _=ln(t,["safetySettings"]);if(void 0!==n&&null!=_){let e=_;Array.isArray(e)&&(e=e.map((e=>function(e){const t={};if(void 0!==ln(e,["method"]))throw new Error("method parameter is not supported in Gemini API.");const n=ln(e,["category"]);null!=n&&an(t,["category"],n);const o=ln(e,["threshold"]);return null!=o&&an(t,["threshold"],o),t}(e)))),an(n,["safetySettings"],e)}const E=ln(t,["tools"]);if(void 0!==n&&null!=E){let e=Ii(E);Array.isArray(e)&&(e=e.map((e=>$r(Ai(e))))),an(n,["tools"],e)}const C=ln(t,["toolConfig"]);if(void 0!==n&&null!=C&&an(n,["toolConfig"],Hr(C)),void 0!==ln(t,["labels"]))throw new Error("labels parameter is not supported in Gemini API.");const b=ln(t,["cachedContent"]);void 0!==n&&null!=b&&an(n,["cachedContent"],Oi(e,b));const T=ln(t,["responseModalities"]);null!=T&&an(o,["responseModalities"],T);const S=ln(t,["mediaResolution"]);null!=S&&an(o,["mediaResolution"],S);const w=ln(t,["speechConfig"]);if(null!=w&&an(o,["speechConfig"],function(e){const t={},n=ln(e,["voiceConfig"]);null!=n&&an(t,["voiceConfig"],Zr(n));const o=ln(e,["multiSpeakerVoiceConfig"]);null!=o&&an(t,["multiSpeakerVoiceConfig"],Yr(o));const i=ln(e,["languageCode"]);return null!=i&&an(t,["languageCode"],i),t}(Si(w))),void 0!==ln(t,["audioTimestamp"]))throw new Error("audioTimestamp parameter is not supported in Gemini API.");const A=ln(t,["thinkingConfig"]);return null!=A&&an(o,["thinkingConfig"],function(e){const t={},n=ln(e,["includeThoughts"]);null!=n&&an(t,["includeThoughts"],n);const o=ln(e,["thinkingBudget"]);return null!=o&&an(t,["thinkingBudget"],o),t}(A)),o}function Wr(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["contents"]);if(null!=i){let e=yi(i);Array.isArray(e)&&(e=e.map((e=>Fr(e)))),an(n,["contents"],e)}const s=ln(t,["config"]);return null!=s&&an(n,["generationConfig"],Kr(e,s,n)),n}function zr(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["contents"]);null!=i&&an(n,["requests[]","content"],vi(e,i));const s=ln(t,["config"]);null!=s&&an(n,["config"],function(e,t){const n=ln(e,["taskType"]);void 0!==t&&null!=n&&an(t,["requests[]","taskType"],n);const o=ln(e,["title"]);void 0!==t&&null!=o&&an(t,["requests[]","title"],o);const i=ln(e,["outputDimensionality"]);if(void 0!==t&&null!=i&&an(t,["requests[]","outputDimensionality"],i),void 0!==ln(e,["mimeType"]))throw new Error("mimeType parameter is not supported in Gemini API.");if(void 0!==ln(e,["autoTruncate"]))throw new Error("autoTruncate parameter is not supported in Gemini API.");return{}}(s,n));const r=ln(t,["model"]);return void 0!==r&&an(n,["requests[]","model"],si(e,r)),n}function Xr(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["prompt"]);null!=i&&an(n,["instances[0]","prompt"],i);const s=ln(t,["config"]);return null!=s&&an(n,["config"],function(e,t){if(void 0!==ln(e,["outputGcsUri"]))throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(void 0!==ln(e,["negativePrompt"]))throw new Error("negativePrompt parameter is not supported in Gemini API.");const n=ln(e,["numberOfImages"]);void 0!==t&&null!=n&&an(t,["parameters","sampleCount"],n);const o=ln(e,["aspectRatio"]);void 0!==t&&null!=o&&an(t,["parameters","aspectRatio"],o);const i=ln(e,["guidanceScale"]);if(void 0!==t&&null!=i&&an(t,["parameters","guidanceScale"],i),void 0!==ln(e,["seed"]))throw new Error("seed parameter is not supported in Gemini API.");const s=ln(e,["safetyFilterLevel"]);void 0!==t&&null!=s&&an(t,["parameters","safetySetting"],s);const r=ln(e,["personGeneration"]);void 0!==t&&null!=r&&an(t,["parameters","personGeneration"],r);const a=ln(e,["includeSafetyAttributes"]);void 0!==t&&null!=a&&an(t,["parameters","includeSafetyAttributes"],a);const l=ln(e,["includeRaiReason"]);void 0!==t&&null!=l&&an(t,["parameters","includeRaiReason"],l);const c=ln(e,["language"]);void 0!==t&&null!=c&&an(t,["parameters","language"],c);const u=ln(e,["outputMimeType"]);void 0!==t&&null!=u&&an(t,["parameters","outputOptions","mimeType"],u);const d=ln(e,["outputCompressionQuality"]);if(void 0!==t&&null!=d&&an(t,["parameters","outputOptions","compressionQuality"],d),void 0!==ln(e,["addWatermark"]))throw new Error("addWatermark parameter is not supported in Gemini API.");if(void 0!==ln(e,["enhancePrompt"]))throw new Error("enhancePrompt parameter is not supported in Gemini API.");return{}}(s,n)),n}function Qr(e,t){const n={},o=ln(t,["config"]);return null!=o&&an(n,["config"],function(e,t,n){const o=ln(t,["pageSize"]);void 0!==n&&null!=o&&an(n,["_query","pageSize"],o);const i=ln(t,["pageToken"]);void 0!==n&&null!=i&&an(n,["_query","pageToken"],i);const s=ln(t,["filter"]);void 0!==n&&null!=s&&an(n,["_query","filter"],s);const r=ln(t,["queryBase"]);return void 0!==n&&null!=r&&an(n,["_url","models_url"],Pi(e,r)),{}}(e,o,n)),n}function ea(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","name"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],function(e,t){const n=ln(e,["displayName"]);void 0!==t&&null!=n&&an(t,["displayName"],n);const o=ln(e,["description"]);void 0!==t&&null!=o&&an(t,["description"],o);const i=ln(e,["defaultCheckpointId"]);return void 0!==t&&null!=i&&an(t,["defaultCheckpointId"],i),{}}(i,n)),n}function ta(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["contents"]);if(null!=i){let e=yi(i);Array.isArray(e)&&(e=e.map((e=>Fr(e)))),an(n,["contents"],e)}const s=ln(t,["config"]);return null!=s&&an(n,["config"],function(e){if(void 0!==ln(e,["systemInstruction"]))throw new Error("systemInstruction parameter is not supported in Gemini API.");if(void 0!==ln(e,["tools"]))throw new Error("tools parameter is not supported in Gemini API.");if(void 0!==ln(e,["generationConfig"]))throw new Error("generationConfig parameter is not supported in Gemini API.");return{}}(s)),n}function na(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["prompt"]);null!=i&&an(n,["instances[0]","prompt"],i);const s=ln(t,["image"]);if(null!=s&&an(n,["instances[0]","image"],function(e){const t={};if(void 0!==ln(e,["gcsUri"]))throw new Error("gcsUri parameter is not supported in Gemini API.");const n=ln(e,["imageBytes"]);null!=n&&an(t,["bytesBase64Encoded"],xi(n));const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s)),void 0!==ln(t,["video"]))throw new Error("video parameter is not supported in Gemini API.");const r=ln(t,["config"]);return null!=r&&an(n,["config"],function(e,t){const n=ln(e,["numberOfVideos"]);if(void 0!==t&&null!=n&&an(t,["parameters","sampleCount"],n),void 0!==ln(e,["outputGcsUri"]))throw new Error("outputGcsUri parameter is not supported in Gemini API.");if(void 0!==ln(e,["fps"]))throw new Error("fps parameter is not supported in Gemini API.");const o=ln(e,["durationSeconds"]);if(void 0!==t&&null!=o&&an(t,["parameters","durationSeconds"],o),void 0!==ln(e,["seed"]))throw new Error("seed parameter is not supported in Gemini API.");const i=ln(e,["aspectRatio"]);if(void 0!==t&&null!=i&&an(t,["parameters","aspectRatio"],i),void 0!==ln(e,["resolution"]))throw new Error("resolution parameter is not supported in Gemini API.");const s=ln(e,["personGeneration"]);if(void 0!==t&&null!=s&&an(t,["parameters","personGeneration"],s),void 0!==ln(e,["pubsubTopic"]))throw new Error("pubsubTopic parameter is not supported in Gemini API.");const r=ln(e,["negativePrompt"]);void 0!==t&&null!=r&&an(t,["parameters","negativePrompt"],r);const a=ln(e,["enhancePrompt"]);if(void 0!==t&&null!=a&&an(t,["parameters","enhancePrompt"],a),void 0!==ln(e,["generateAudio"]))throw new Error("generateAudio parameter is not supported in Gemini API.");if(void 0!==ln(e,["lastFrame"]))throw new Error("lastFrame parameter is not supported in Gemini API.");if(void 0!==ln(e,["compressionQuality"]))throw new Error("compressionQuality parameter is not supported in Gemini API.");return{}}(r,n)),n}function oa(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["data"]);null!=o&&an(t,["data"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["fileUri"]);null!=o&&an(t,["fileUri"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function ia(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>oa(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}function sa(e){const t={},n=ln(e,["timeRangeFilter"]);return null!=n&&an(t,["timeRangeFilter"],function(e){const t={},n=ln(e,["startTime"]);null!=n&&an(t,["startTime"],n);const o=ln(e,["endTime"]);return null!=o&&an(t,["endTime"],o),t}(n)),t}function ra(e){const t={},n=ln(e,["dynamicRetrievalConfig"]);return null!=n&&an(t,["dynamicRetrievalConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["dynamicThreshold"]);return null!=o&&an(t,["dynamicThreshold"],o),t}(n)),t}function aa(e){const t={},n=ln(e,["apiKeyConfig"]);null!=n&&an(t,["apiKeyConfig"],function(e){const t={},n=ln(e,["apiKeyString"]);return null!=n&&an(t,["apiKeyString"],n),t}(n));const o=ln(e,["authType"]);null!=o&&an(t,["authType"],o);const i=ln(e,["googleServiceAccountConfig"]);null!=i&&an(t,["googleServiceAccountConfig"],i);const s=ln(e,["httpBasicAuthConfig"]);null!=s&&an(t,["httpBasicAuthConfig"],s);const r=ln(e,["oauthConfig"]);null!=r&&an(t,["oauthConfig"],r);const a=ln(e,["oidcConfig"]);return null!=a&&an(t,["oidcConfig"],a),t}function la(e){const t={},n=ln(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={};if(void 0!==ln(e,["behavior"]))throw new Error("behavior parameter is not supported in Vertex AI.");const n=ln(e,["description"]);null!=n&&an(t,["description"],n);const o=ln(e,["name"]);null!=o&&an(t,["name"],o);const i=ln(e,["parameters"]);null!=i&&an(t,["parameters"],i);const s=ln(e,["parametersJsonSchema"]);null!=s&&an(t,["parametersJsonSchema"],s);const r=ln(e,["response"]);null!=r&&an(t,["response"],r);const a=ln(e,["responseJsonSchema"]);return null!=a&&an(t,["responseJsonSchema"],a),t}(e)))),an(t,["functionDeclarations"],e)}const o=ln(e,["retrieval"]);null!=o&&an(t,["retrieval"],o);const i=ln(e,["googleSearch"]);null!=i&&an(t,["googleSearch"],sa(i));const s=ln(e,["googleSearchRetrieval"]);null!=s&&an(t,["googleSearchRetrieval"],ra(s));null!=ln(e,["enterpriseWebSearch"])&&an(t,["enterpriseWebSearch"],{});const r=ln(e,["googleMaps"]);null!=r&&an(t,["googleMaps"],function(e){const t={},n=ln(e,["authConfig"]);return null!=n&&an(t,["authConfig"],aa(n)),t}(r));null!=ln(e,["urlContext"])&&an(t,["urlContext"],{});const a=ln(e,["codeExecution"]);null!=a&&an(t,["codeExecution"],a);const l=ln(e,["computerUse"]);return null!=l&&an(t,["computerUse"],l),t}function ca(e){const t={},n=ln(e,["latLng"]);null!=n&&an(t,["latLng"],function(e){const t={},n=ln(e,["latitude"]);null!=n&&an(t,["latitude"],n);const o=ln(e,["longitude"]);return null!=o&&an(t,["longitude"],o),t}(n));const o=ln(e,["languageCode"]);return null!=o&&an(t,["languageCode"],o),t}function ua(e){const t={},n=ln(e,["functionCallingConfig"]);null!=n&&an(t,["functionCallingConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["allowedFunctionNames"]);return null!=o&&an(t,["allowedFunctionNames"],o),t}(n));const o=ln(e,["retrievalConfig"]);return null!=o&&an(t,["retrievalConfig"],ca(o)),t}function da(e){const t={},n=ln(e,["prebuiltVoiceConfig"]);return null!=n&&an(t,["prebuiltVoiceConfig"],function(e){const t={},n=ln(e,["voiceName"]);return null!=n&&an(t,["voiceName"],n),t}(n)),t}function pa(e,t,n){const o={},i=ln(t,["systemInstruction"]);void 0!==n&&null!=i&&an(n,["systemInstruction"],ia(gi(i)));const s=ln(t,["temperature"]);null!=s&&an(o,["temperature"],s);const r=ln(t,["topP"]);null!=r&&an(o,["topP"],r);const a=ln(t,["topK"]);null!=a&&an(o,["topK"],a);const l=ln(t,["candidateCount"]);null!=l&&an(o,["candidateCount"],l);const c=ln(t,["maxOutputTokens"]);null!=c&&an(o,["maxOutputTokens"],c);const u=ln(t,["stopSequences"]);null!=u&&an(o,["stopSequences"],u);const d=ln(t,["responseLogprobs"]);null!=d&&an(o,["responseLogprobs"],d);const p=ln(t,["logprobs"]);null!=p&&an(o,["logprobs"],p);const m=ln(t,["presencePenalty"]);null!=m&&an(o,["presencePenalty"],m);const f=ln(t,["frequencyPenalty"]);null!=f&&an(o,["frequencyPenalty"],f);const h=ln(t,["seed"]);null!=h&&an(o,["seed"],h);const g=ln(t,["responseMimeType"]);null!=g&&an(o,["responseMimeType"],g);const v=ln(t,["responseSchema"]);null!=v&&an(o,["responseSchema"],function(e){const t={},n=ln(e,["anyOf"]);null!=n&&an(t,["anyOf"],n);const o=ln(e,["default"]);null!=o&&an(t,["default"],o);const i=ln(e,["description"]);null!=i&&an(t,["description"],i);const s=ln(e,["enum"]);null!=s&&an(t,["enum"],s);const r=ln(e,["example"]);null!=r&&an(t,["example"],r);const a=ln(e,["format"]);null!=a&&an(t,["format"],a);const l=ln(e,["items"]);null!=l&&an(t,["items"],l);const c=ln(e,["maxItems"]);null!=c&&an(t,["maxItems"],c);const u=ln(e,["maxLength"]);null!=u&&an(t,["maxLength"],u);const d=ln(e,["maxProperties"]);null!=d&&an(t,["maxProperties"],d);const p=ln(e,["maximum"]);null!=p&&an(t,["maximum"],p);const m=ln(e,["minItems"]);null!=m&&an(t,["minItems"],m);const f=ln(e,["minLength"]);null!=f&&an(t,["minLength"],f);const h=ln(e,["minProperties"]);null!=h&&an(t,["minProperties"],h);const g=ln(e,["minimum"]);null!=g&&an(t,["minimum"],g);const v=ln(e,["nullable"]);null!=v&&an(t,["nullable"],v);const y=ln(e,["pattern"]);null!=y&&an(t,["pattern"],y);const _=ln(e,["properties"]);null!=_&&an(t,["properties"],_);const E=ln(e,["propertyOrdering"]);null!=E&&an(t,["propertyOrdering"],E);const C=ln(e,["required"]);null!=C&&an(t,["required"],C);const b=ln(e,["title"]);null!=b&&an(t,["title"],b);const T=ln(e,["type"]);return null!=T&&an(t,["type"],T),t}(Ti(v)));const y=ln(t,["responseJsonSchema"]);null!=y&&an(o,["responseJsonSchema"],y);const _=ln(t,["routingConfig"]);null!=_&&an(o,["routingConfig"],_);const E=ln(t,["modelSelectionConfig"]);null!=E&&an(o,["modelConfig"],function(e){const t={},n=ln(e,["featureSelectionPreference"]);return null!=n&&an(t,["featureSelectionPreference"],n),t}(E));const C=ln(t,["safetySettings"]);if(void 0!==n&&null!=C){let e=C;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["method"]);null!=n&&an(t,["method"],n);const o=ln(e,["category"]);null!=o&&an(t,["category"],o);const i=ln(e,["threshold"]);return null!=i&&an(t,["threshold"],i),t}(e)))),an(n,["safetySettings"],e)}const b=ln(t,["tools"]);if(void 0!==n&&null!=b){let e=Ii(b);Array.isArray(e)&&(e=e.map((e=>la(Ai(e))))),an(n,["tools"],e)}const T=ln(t,["toolConfig"]);void 0!==n&&null!=T&&an(n,["toolConfig"],ua(T));const S=ln(t,["labels"]);void 0!==n&&null!=S&&an(n,["labels"],S);const w=ln(t,["cachedContent"]);void 0!==n&&null!=w&&an(n,["cachedContent"],Oi(e,w));const A=ln(t,["responseModalities"]);null!=A&&an(o,["responseModalities"],A);const I=ln(t,["mediaResolution"]);null!=I&&an(o,["mediaResolution"],I);const O=ln(t,["speechConfig"]);null!=O&&an(o,["speechConfig"],function(e){const t={},n=ln(e,["voiceConfig"]);if(null!=n&&an(t,["voiceConfig"],da(n)),void 0!==ln(e,["multiSpeakerVoiceConfig"]))throw new Error("multiSpeakerVoiceConfig parameter is not supported in Vertex AI.");const o=ln(e,["languageCode"]);return null!=o&&an(t,["languageCode"],o),t}(Si(O)));const N=ln(t,["audioTimestamp"]);null!=N&&an(o,["audioTimestamp"],N);const x=ln(t,["thinkingConfig"]);return null!=x&&an(o,["thinkingConfig"],function(e){const t={},n=ln(e,["includeThoughts"]);null!=n&&an(t,["includeThoughts"],n);const o=ln(e,["thinkingBudget"]);return null!=o&&an(t,["thinkingBudget"],o),t}(x)),o}function ma(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["contents"]);if(null!=i){let e=yi(i);Array.isArray(e)&&(e=e.map((e=>ia(e)))),an(n,["contents"],e)}const s=ln(t,["config"]);return null!=s&&an(n,["generationConfig"],pa(e,s,n)),n}function fa(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["contents"]);null!=i&&an(n,["instances[]","content"],vi(e,i));const s=ln(t,["config"]);return null!=s&&an(n,["config"],function(e,t){const n=ln(e,["taskType"]);void 0!==t&&null!=n&&an(t,["instances[]","task_type"],n);const o=ln(e,["title"]);void 0!==t&&null!=o&&an(t,["instances[]","title"],o);const i=ln(e,["outputDimensionality"]);void 0!==t&&null!=i&&an(t,["parameters","outputDimensionality"],i);const s=ln(e,["mimeType"]);void 0!==t&&null!=s&&an(t,["instances[]","mimeType"],s);const r=ln(e,["autoTruncate"]);return void 0!==t&&null!=r&&an(t,["parameters","autoTruncate"],r),{}}(s,n)),n}function ha(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["prompt"]);null!=i&&an(n,["instances[0]","prompt"],i);const s=ln(t,["config"]);return null!=s&&an(n,["config"],function(e,t){const n=ln(e,["outputGcsUri"]);void 0!==t&&null!=n&&an(t,["parameters","storageUri"],n);const o=ln(e,["negativePrompt"]);void 0!==t&&null!=o&&an(t,["parameters","negativePrompt"],o);const i=ln(e,["numberOfImages"]);void 0!==t&&null!=i&&an(t,["parameters","sampleCount"],i);const s=ln(e,["aspectRatio"]);void 0!==t&&null!=s&&an(t,["parameters","aspectRatio"],s);const r=ln(e,["guidanceScale"]);void 0!==t&&null!=r&&an(t,["parameters","guidanceScale"],r);const a=ln(e,["seed"]);void 0!==t&&null!=a&&an(t,["parameters","seed"],a);const l=ln(e,["safetyFilterLevel"]);void 0!==t&&null!=l&&an(t,["parameters","safetySetting"],l);const c=ln(e,["personGeneration"]);void 0!==t&&null!=c&&an(t,["parameters","personGeneration"],c);const u=ln(e,["includeSafetyAttributes"]);void 0!==t&&null!=u&&an(t,["parameters","includeSafetyAttributes"],u);const d=ln(e,["includeRaiReason"]);void 0!==t&&null!=d&&an(t,["parameters","includeRaiReason"],d);const p=ln(e,["language"]);void 0!==t&&null!=p&&an(t,["parameters","language"],p);const m=ln(e,["outputMimeType"]);void 0!==t&&null!=m&&an(t,["parameters","outputOptions","mimeType"],m);const f=ln(e,["outputCompressionQuality"]);void 0!==t&&null!=f&&an(t,["parameters","outputOptions","compressionQuality"],f);const h=ln(e,["addWatermark"]);void 0!==t&&null!=h&&an(t,["parameters","addWatermark"],h);const g=ln(e,["enhancePrompt"]);return void 0!==t&&null!=g&&an(t,["parameters","enhancePrompt"],g),{}}(s,n)),n}function ga(e){const t={},n=ln(e,["gcsUri"]);null!=n&&an(t,["gcsUri"],n);const o=ln(e,["imageBytes"]);null!=o&&an(t,["bytesBase64Encoded"],xi(o));const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}function va(e){const t={},n=ln(e,["referenceImage"]);null!=n&&an(t,["referenceImage"],ga(n));const o=ln(e,["referenceId"]);null!=o&&an(t,["referenceId"],o);const i=ln(e,["referenceType"]);null!=i&&an(t,["referenceType"],i);const s=ln(e,["maskImageConfig"]);null!=s&&an(t,["maskImageConfig"],function(e){const t={},n=ln(e,["maskMode"]);null!=n&&an(t,["maskMode"],n);const o=ln(e,["segmentationClasses"]);null!=o&&an(t,["maskClasses"],o);const i=ln(e,["maskDilation"]);return null!=i&&an(t,["dilation"],i),t}(s));const r=ln(e,["controlImageConfig"]);null!=r&&an(t,["controlImageConfig"],function(e){const t={},n=ln(e,["controlType"]);null!=n&&an(t,["controlType"],n);const o=ln(e,["enableControlImageComputation"]);return null!=o&&an(t,["computeControl"],o),t}(r));const a=ln(e,["styleImageConfig"]);null!=a&&an(t,["styleImageConfig"],function(e){const t={},n=ln(e,["styleDescription"]);return null!=n&&an(t,["styleDescription"],n),t}(a));const l=ln(e,["subjectImageConfig"]);return null!=l&&an(t,["subjectImageConfig"],function(e){const t={},n=ln(e,["subjectType"]);null!=n&&an(t,["subjectType"],n);const o=ln(e,["subjectDescription"]);return null!=o&&an(t,["subjectDescription"],o),t}(l)),t}function ya(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["prompt"]);null!=i&&an(n,["instances[0]","prompt"],i);const s=ln(t,["referenceImages"]);if(null!=s){let e=s;Array.isArray(e)&&(e=e.map((e=>va(e)))),an(n,["instances[0]","referenceImages"],e)}const r=ln(t,["config"]);return null!=r&&an(n,["config"],function(e,t){const n=ln(e,["outputGcsUri"]);void 0!==t&&null!=n&&an(t,["parameters","storageUri"],n);const o=ln(e,["negativePrompt"]);void 0!==t&&null!=o&&an(t,["parameters","negativePrompt"],o);const i=ln(e,["numberOfImages"]);void 0!==t&&null!=i&&an(t,["parameters","sampleCount"],i);const s=ln(e,["aspectRatio"]);void 0!==t&&null!=s&&an(t,["parameters","aspectRatio"],s);const r=ln(e,["guidanceScale"]);void 0!==t&&null!=r&&an(t,["parameters","guidanceScale"],r);const a=ln(e,["seed"]);void 0!==t&&null!=a&&an(t,["parameters","seed"],a);const l=ln(e,["safetyFilterLevel"]);void 0!==t&&null!=l&&an(t,["parameters","safetySetting"],l);const c=ln(e,["personGeneration"]);void 0!==t&&null!=c&&an(t,["parameters","personGeneration"],c);const u=ln(e,["includeSafetyAttributes"]);void 0!==t&&null!=u&&an(t,["parameters","includeSafetyAttributes"],u);const d=ln(e,["includeRaiReason"]);void 0!==t&&null!=d&&an(t,["parameters","includeRaiReason"],d);const p=ln(e,["language"]);void 0!==t&&null!=p&&an(t,["parameters","language"],p);const m=ln(e,["outputMimeType"]);void 0!==t&&null!=m&&an(t,["parameters","outputOptions","mimeType"],m);const f=ln(e,["outputCompressionQuality"]);void 0!==t&&null!=f&&an(t,["parameters","outputOptions","compressionQuality"],f);const h=ln(e,["editMode"]);void 0!==t&&null!=h&&an(t,["parameters","editMode"],h);const g=ln(e,["baseSteps"]);return void 0!==t&&null!=g&&an(t,["parameters","editConfig","baseSteps"],g),{}}(r,n)),n}function _a(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["image"]);null!=i&&an(n,["instances[0]","image"],ga(i));const s=ln(t,["upscaleFactor"]);null!=s&&an(n,["parameters","upscaleConfig","upscaleFactor"],s);const r=ln(t,["config"]);return null!=r&&an(n,["config"],function(e,t){const n=ln(e,["includeRaiReason"]);void 0!==t&&null!=n&&an(t,["parameters","includeRaiReason"],n);const o=ln(e,["outputMimeType"]);void 0!==t&&null!=o&&an(t,["parameters","outputOptions","mimeType"],o);const i=ln(e,["outputCompressionQuality"]);void 0!==t&&null!=i&&an(t,["parameters","outputOptions","compressionQuality"],i);const s=ln(e,["enhanceInputImage"]);void 0!==t&&null!=s&&an(t,["parameters","upscaleConfig","enhanceInputImage"],s);const r=ln(e,["imagePreservationFactor"]);void 0!==t&&null!=r&&an(t,["parameters","upscaleConfig","imagePreservationFactor"],r);const a=ln(e,["numberOfImages"]);void 0!==t&&null!=a&&an(t,["parameters","sampleCount"],a);const l=ln(e,["mode"]);return void 0!==t&&null!=l&&an(t,["parameters","mode"],l),{}}(r,n)),n}function Ea(e,t){const n={},o=ln(t,["config"]);return null!=o&&an(n,["config"],function(e,t,n){const o=ln(t,["pageSize"]);void 0!==n&&null!=o&&an(n,["_query","pageSize"],o);const i=ln(t,["pageToken"]);void 0!==n&&null!=i&&an(n,["_query","pageToken"],i);const s=ln(t,["filter"]);void 0!==n&&null!=s&&an(n,["_query","filter"],s);const r=ln(t,["queryBase"]);return void 0!==n&&null!=r&&an(n,["_url","models_url"],Pi(e,r)),{}}(e,o,n)),n}function Ca(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],function(e,t){const n=ln(e,["displayName"]);void 0!==t&&null!=n&&an(t,["displayName"],n);const o=ln(e,["description"]);void 0!==t&&null!=o&&an(t,["description"],o);const i=ln(e,["defaultCheckpointId"]);return void 0!==t&&null!=i&&an(t,["defaultCheckpointId"],i),{}}(i,n)),n}function ba(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["contents"]);if(null!=i){let e=yi(i);Array.isArray(e)&&(e=e.map((e=>ia(e)))),an(n,["contents"],e)}const s=ln(t,["config"]);return null!=s&&an(n,["config"],function(e,t){const n=ln(e,["systemInstruction"]);void 0!==t&&null!=n&&an(t,["systemInstruction"],ia(gi(n)));const o=ln(e,["tools"]);if(void 0!==t&&null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>la(e)))),an(t,["tools"],e)}const i=ln(e,["generationConfig"]);return void 0!==t&&null!=i&&an(t,["generationConfig"],i),{}}(s,n)),n}function Ta(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["prompt"]);null!=i&&an(n,["instances[0]","prompt"],i);const s=ln(t,["image"]);null!=s&&an(n,["instances[0]","image"],ga(s));const r=ln(t,["video"]);null!=r&&an(n,["instances[0]","video"],function(e){const t={},n=ln(e,["uri"]);null!=n&&an(t,["gcsUri"],n);const o=ln(e,["videoBytes"]);null!=o&&an(t,["bytesBase64Encoded"],xi(o));const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(r));const a=ln(t,["config"]);return null!=a&&an(n,["config"],function(e,t){const n=ln(e,["numberOfVideos"]);void 0!==t&&null!=n&&an(t,["parameters","sampleCount"],n);const o=ln(e,["outputGcsUri"]);void 0!==t&&null!=o&&an(t,["parameters","storageUri"],o);const i=ln(e,["fps"]);void 0!==t&&null!=i&&an(t,["parameters","fps"],i);const s=ln(e,["durationSeconds"]);void 0!==t&&null!=s&&an(t,["parameters","durationSeconds"],s);const r=ln(e,["seed"]);void 0!==t&&null!=r&&an(t,["parameters","seed"],r);const a=ln(e,["aspectRatio"]);void 0!==t&&null!=a&&an(t,["parameters","aspectRatio"],a);const l=ln(e,["resolution"]);void 0!==t&&null!=l&&an(t,["parameters","resolution"],l);const c=ln(e,["personGeneration"]);void 0!==t&&null!=c&&an(t,["parameters","personGeneration"],c);const u=ln(e,["pubsubTopic"]);void 0!==t&&null!=u&&an(t,["parameters","pubsubTopic"],u);const d=ln(e,["negativePrompt"]);void 0!==t&&null!=d&&an(t,["parameters","negativePrompt"],d);const p=ln(e,["enhancePrompt"]);void 0!==t&&null!=p&&an(t,["parameters","enhancePrompt"],p);const m=ln(e,["generateAudio"]);void 0!==t&&null!=m&&an(t,["parameters","generateAudio"],m);const f=ln(e,["lastFrame"]);void 0!==t&&null!=f&&an(t,["instances[0]","lastFrame"],ga(f));const h=ln(e,["compressionQuality"]);return void 0!==t&&null!=h&&an(t,["parameters","compressionQuality"],h),{}}(a,n)),n}function Sa(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={},n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={},n=ln(e,["fileUri"]);null!=n&&an(t,["fileUri"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function wa(e){const t={},n=ln(e,["urlMetadata"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["retrievedUrl"]);null!=n&&an(t,["retrievedUrl"],n);const o=ln(e,["urlRetrievalStatus"]);return null!=o&&an(t,["urlRetrievalStatus"],o),t}(e)))),an(t,["urlMetadata"],e)}return t}function Aa(e){const t={},n=ln(e,["content"]);null!=n&&an(t,["content"],function(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Sa(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}(n));const o=ln(e,["citationMetadata"]);null!=o&&an(t,["citationMetadata"],function(e){const t={},n=ln(e,["citationSources"]);return null!=n&&an(t,["citations"],n),t}(o));const i=ln(e,["tokenCount"]);null!=i&&an(t,["tokenCount"],i);const s=ln(e,["finishReason"]);null!=s&&an(t,["finishReason"],s);const r=ln(e,["urlContextMetadata"]);null!=r&&an(t,["urlContextMetadata"],wa(r));const a=ln(e,["avgLogprobs"]);null!=a&&an(t,["avgLogprobs"],a);const l=ln(e,["groundingMetadata"]);null!=l&&an(t,["groundingMetadata"],l);const c=ln(e,["index"]);null!=c&&an(t,["index"],c);const u=ln(e,["logprobsResult"]);null!=u&&an(t,["logprobsResult"],u);const d=ln(e,["safetyRatings"]);return null!=d&&an(t,["safetyRatings"],d),t}function Ia(e){const t={},n=ln(e,["candidates"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Aa(e)))),an(t,["candidates"],e)}const o=ln(e,["modelVersion"]);null!=o&&an(t,["modelVersion"],o);const i=ln(e,["promptFeedback"]);null!=i&&an(t,["promptFeedback"],i);const s=ln(e,["usageMetadata"]);return null!=s&&an(t,["usageMetadata"],s),t}function Oa(e){const t={},n=ln(e,["embeddings"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["values"]);return null!=n&&an(t,["values"],n),t}(e)))),an(t,["embeddings"],e)}return null!=ln(e,["metadata"])&&an(t,["metadata"],{}),t}function Na(e){const t={},n=ln(e,["safetyAttributes","categories"]);null!=n&&an(t,["categories"],n);const o=ln(e,["safetyAttributes","scores"]);null!=o&&an(t,["scores"],o);const i=ln(e,["contentType"]);return null!=i&&an(t,["contentType"],i),t}function xa(e){const t={},n=ln(e,["_self"]);null!=n&&an(t,["image"],function(e){const t={},n=ln(e,["bytesBase64Encoded"]);null!=n&&an(t,["imageBytes"],xi(n));const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(n));const o=ln(e,["raiFilteredReason"]);null!=o&&an(t,["raiFilteredReason"],o);const i=ln(e,["_self"]);return null!=i&&an(t,["safetyAttributes"],Na(i)),t}function ka(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["displayName"]);null!=o&&an(t,["displayName"],o);const i=ln(e,["description"]);null!=i&&an(t,["description"],i);const s=ln(e,["version"]);null!=s&&an(t,["version"],s);const r=ln(e,["_self"]);null!=r&&an(t,["tunedModelInfo"],function(e){const t={},n=ln(e,["baseModel"]);null!=n&&an(t,["baseModel"],n);const o=ln(e,["createTime"]);null!=o&&an(t,["createTime"],o);const i=ln(e,["updateTime"]);return null!=i&&an(t,["updateTime"],i),t}(r));const a=ln(e,["inputTokenLimit"]);null!=a&&an(t,["inputTokenLimit"],a);const l=ln(e,["outputTokenLimit"]);null!=l&&an(t,["outputTokenLimit"],l);const c=ln(e,["supportedGenerationMethods"]);return null!=c&&an(t,["supportedActions"],c),t}function Pa(e){const t={},n=ln(e,["_self"]);return null!=n&&an(t,["video"],function(e){const t={},n=ln(e,["video","uri"]);null!=n&&an(t,["uri"],n);const o=ln(e,["video","encodedVideo"]);null!=o&&an(t,["videoBytes"],xi(o));const i=ln(e,["encoding"]);return null!=i&&an(t,["mimeType"],i),t}(n)),t}function Ra(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["metadata"]);null!=o&&an(t,["metadata"],o);const i=ln(e,["done"]);null!=i&&an(t,["done"],i);const s=ln(e,["error"]);null!=s&&an(t,["error"],s);const r=ln(e,["response","generateVideoResponse"]);return null!=r&&an(t,["response"],function(e){const t={},n=ln(e,["generatedSamples"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Pa(e)))),an(t,["generatedVideos"],e)}const o=ln(e,["raiMediaFilteredCount"]);null!=o&&an(t,["raiMediaFilteredCount"],o);const i=ln(e,["raiMediaFilteredReasons"]);return null!=i&&an(t,["raiMediaFilteredReasons"],i),t}(r)),t}function Ma(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["data"]);null!=o&&an(t,["data"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={},n=ln(e,["displayName"]);null!=n&&an(t,["displayName"],n);const o=ln(e,["fileUri"]);null!=o&&an(t,["fileUri"],o);const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function Da(e){const t={},n=ln(e,["urlMetadata"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["retrievedUrl"]);null!=n&&an(t,["retrievedUrl"],n);const o=ln(e,["urlRetrievalStatus"]);return null!=o&&an(t,["urlRetrievalStatus"],o),t}(e)))),an(t,["urlMetadata"],e)}return t}function Va(e){const t={},n=ln(e,["content"]);null!=n&&an(t,["content"],function(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Ma(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}(n));const o=ln(e,["citationMetadata"]);null!=o&&an(t,["citationMetadata"],function(e){const t={},n=ln(e,["citations"]);return null!=n&&an(t,["citations"],n),t}(o));const i=ln(e,["finishMessage"]);null!=i&&an(t,["finishMessage"],i);const s=ln(e,["finishReason"]);null!=s&&an(t,["finishReason"],s);const r=ln(e,["urlContextMetadata"]);null!=r&&an(t,["urlContextMetadata"],Da(r));const a=ln(e,["avgLogprobs"]);null!=a&&an(t,["avgLogprobs"],a);const l=ln(e,["groundingMetadata"]);null!=l&&an(t,["groundingMetadata"],l);const c=ln(e,["index"]);null!=c&&an(t,["index"],c);const u=ln(e,["logprobsResult"]);null!=u&&an(t,["logprobsResult"],u);const d=ln(e,["safetyRatings"]);return null!=d&&an(t,["safetyRatings"],d),t}function Ua(e){const t={},n=ln(e,["candidates"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Va(e)))),an(t,["candidates"],e)}const o=ln(e,["createTime"]);null!=o&&an(t,["createTime"],o);const i=ln(e,["responseId"]);null!=i&&an(t,["responseId"],i);const s=ln(e,["modelVersion"]);null!=s&&an(t,["modelVersion"],s);const r=ln(e,["promptFeedback"]);null!=r&&an(t,["promptFeedback"],r);const a=ln(e,["usageMetadata"]);return null!=a&&an(t,["usageMetadata"],a),t}function La(e){const t={},n=ln(e,["values"]);null!=n&&an(t,["values"],n);const o=ln(e,["statistics"]);return null!=o&&an(t,["statistics"],function(e){const t={},n=ln(e,["truncated"]);null!=n&&an(t,["truncated"],n);const o=ln(e,["token_count"]);return null!=o&&an(t,["tokenCount"],o),t}(o)),t}function ja(e){const t={},n=ln(e,["predictions[]","embeddings"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>La(e)))),an(t,["embeddings"],e)}const o=ln(e,["metadata"]);return null!=o&&an(t,["metadata"],function(e){const t={},n=ln(e,["billableCharacterCount"]);return null!=n&&an(t,["billableCharacterCount"],n),t}(o)),t}function qa(e){const t={},n=ln(e,["safetyAttributes","categories"]);null!=n&&an(t,["categories"],n);const o=ln(e,["safetyAttributes","scores"]);null!=o&&an(t,["scores"],o);const i=ln(e,["contentType"]);return null!=i&&an(t,["contentType"],i),t}function Fa(e){const t={},n=ln(e,["_self"]);null!=n&&an(t,["image"],function(e){const t={},n=ln(e,["gcsUri"]);null!=n&&an(t,["gcsUri"],n);const o=ln(e,["bytesBase64Encoded"]);null!=o&&an(t,["imageBytes"],xi(o));const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(n));const o=ln(e,["raiFilteredReason"]);null!=o&&an(t,["raiFilteredReason"],o);const i=ln(e,["_self"]);null!=i&&an(t,["safetyAttributes"],qa(i));const s=ln(e,["prompt"]);return null!=s&&an(t,["enhancedPrompt"],s),t}function Ba(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["displayName"]);null!=o&&an(t,["displayName"],o);const i=ln(e,["description"]);null!=i&&an(t,["description"],i);const s=ln(e,["versionId"]);null!=s&&an(t,["version"],s);const r=ln(e,["deployedModels"]);if(null!=r){let e=r;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["endpoint"]);null!=n&&an(t,["name"],n);const o=ln(e,["deployedModelId"]);return null!=o&&an(t,["deployedModelId"],o),t}(e)))),an(t,["endpoints"],e)}const a=ln(e,["labels"]);null!=a&&an(t,["labels"],a);const l=ln(e,["_self"]);null!=l&&an(t,["tunedModelInfo"],function(e){const t={},n=ln(e,["labels","google-vertex-llm-tuning-base-model-id"]);null!=n&&an(t,["baseModel"],n);const o=ln(e,["createTime"]);null!=o&&an(t,["createTime"],o);const i=ln(e,["updateTime"]);return null!=i&&an(t,["updateTime"],i),t}(l));const c=ln(e,["defaultCheckpointId"]);null!=c&&an(t,["defaultCheckpointId"],c);const u=ln(e,["checkpoints"]);if(null!=u){let e=u;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["checkpointId"]);null!=n&&an(t,["checkpointId"],n);const o=ln(e,["epoch"]);null!=o&&an(t,["epoch"],o);const i=ln(e,["step"]);return null!=i&&an(t,["step"],i),t}(e)))),an(t,["checkpoints"],e)}return t}function Ga(e){const t={},n=ln(e,["_self"]);return null!=n&&an(t,["video"],function(e){const t={},n=ln(e,["gcsUri"]);null!=n&&an(t,["uri"],n);const o=ln(e,["bytesBase64Encoded"]);null!=o&&an(t,["videoBytes"],xi(o));const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(n)),t}function $a(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["metadata"]);null!=o&&an(t,["metadata"],o);const i=ln(e,["done"]);null!=i&&an(t,["done"],i);const s=ln(e,["error"]);null!=s&&an(t,["error"],s);const r=ln(e,["response"]);return null!=r&&an(t,["response"],function(e){const t={},n=ln(e,["videos"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Ga(e)))),an(t,["generatedVideos"],e)}const o=ln(e,["raiMediaFilteredCount"]);null!=o&&an(t,["raiMediaFilteredCount"],o);const i=ln(e,["raiMediaFilteredReasons"]);return null!=i&&an(t,["raiMediaFilteredReasons"],i),t}(r)),t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */const Ja=/^data: (.*)(?:\n\n|\r\r|\r\n\r\n)/;class Ha{constructor(e){var t,n;this.clientOptions=Object.assign(Object.assign({},e),{project:e.project,location:e.location,apiKey:e.apiKey,vertexai:e.vertexai});const o={};this.clientOptions.vertexai?(o.apiVersion=null!==(t=this.clientOptions.apiVersion)&&void 0!==t?t:"v1beta1",o.baseUrl=this.baseUrlFromProjectLocation(),this.normalizeAuthParameters()):(o.apiVersion=null!==(n=this.clientOptions.apiVersion)&&void 0!==n?n:"v1beta",o.baseUrl="https://generativelanguage.googleapis.com/"),o.headers=this.getDefaultHeaders(),this.clientOptions.httpOptions=o,e.httpOptions&&(this.clientOptions.httpOptions=this.patchHttpOptions(o,e.httpOptions))}baseUrlFromProjectLocation(){return this.clientOptions.project&&this.clientOptions.location&&"global"!==this.clientOptions.location?`https://${this.clientOptions.location}-aiplatform.googleapis.com/`:"https://aiplatform.googleapis.com/"}normalizeAuthParameters(){this.clientOptions.project&&this.clientOptions.location?this.clientOptions.apiKey=void 0:(this.clientOptions.project=void 0,this.clientOptions.location=void 0)}isVertexAI(){var e;return null!==(e=this.clientOptions.vertexai)&&void 0!==e&&e}getProject(){return this.clientOptions.project}getLocation(){return this.clientOptions.location}getApiVersion(){if(this.clientOptions.httpOptions&&void 0!==this.clientOptions.httpOptions.apiVersion)return this.clientOptions.httpOptions.apiVersion;throw new Error("API version is not set.")}getBaseUrl(){if(this.clientOptions.httpOptions&&void 0!==this.clientOptions.httpOptions.baseUrl)return this.clientOptions.httpOptions.baseUrl;throw new Error("Base URL is not set.")}getRequestUrl(){return this.getRequestUrlInternal(this.clientOptions.httpOptions)}getHeaders(){if(this.clientOptions.httpOptions&&void 0!==this.clientOptions.httpOptions.headers)return this.clientOptions.httpOptions.headers;throw new Error("Headers are not set.")}getRequestUrlInternal(e){if(!e||void 0===e.baseUrl||void 0===e.apiVersion)throw new Error("HTTP options are not correctly set.");const t=[e.baseUrl.endsWith("/")?e.baseUrl.slice(0,-1):e.baseUrl];return e.apiVersion&&""!==e.apiVersion&&t.push(e.apiVersion),t.join("/")}getBaseResourcePath(){return`projects/${this.clientOptions.project}/locations/${this.clientOptions.location}`}getApiKey(){return this.clientOptions.apiKey}getWebsocketBaseUrl(){const e=this.getBaseUrl(),t=new URL(e);return t.protocol="http:"==t.protocol?"ws":"wss",t.toString()}setBaseUrl(e){if(!this.clientOptions.httpOptions)throw new Error("HTTP options are not correctly set.");this.clientOptions.httpOptions.baseUrl=e}constructUrl(e,t,n){const o=[this.getRequestUrlInternal(t)];n&&o.push(this.getBaseResourcePath()),""!==e&&o.push(e);return new URL(`${o.join("/")}`)}shouldPrependVertexProjectPath(e){return!this.clientOptions.apiKey&&(!!this.clientOptions.vertexai&&(!e.path.startsWith("projects/")&&("GET"!==e.httpMethod||!e.path.startsWith("publishers/google/models"))))}async request(e){let t=this.clientOptions.httpOptions;e.httpOptions&&(t=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const n=this.shouldPrependVertexProjectPath(e),o=this.constructUrl(e.path,t,n);if(e.queryParams)for(const[s,r]of Object.entries(e.queryParams))o.searchParams.append(s,String(r));let i={};if("GET"===e.httpMethod){if(e.body&&"{}"!==e.body)throw new Error("Request body should be empty for GET request, but got non empty request body")}else i.body=e.body;return i=await this.includeExtraHttpOptionsToRequestInit(i,t,e.abortSignal),this.unaryApiCall(o,i,e.httpMethod)}patchHttpOptions(e,t){const n=JSON.parse(JSON.stringify(e));for(const[o,i]of Object.entries(t))"object"==typeof i?n[o]=Object.assign(Object.assign({},n[o]),i):void 0!==i&&(n[o]=i);return n}async requestStream(e){let t=this.clientOptions.httpOptions;e.httpOptions&&(t=this.patchHttpOptions(this.clientOptions.httpOptions,e.httpOptions));const n=this.shouldPrependVertexProjectPath(e),o=this.constructUrl(e.path,t,n);o.searchParams.has("alt")&&"sse"===o.searchParams.get("alt")||o.searchParams.set("alt","sse");let i={};return i.body=e.body,i=await this.includeExtraHttpOptionsToRequestInit(i,t,e.abortSignal),this.streamApiCall(o,i,e.httpMethod)}async includeExtraHttpOptionsToRequestInit(e,n,o){if(n&&n.timeout||o){const t=new AbortController,i=t.signal;n.timeout&&(null==n?void 0:n.timeout)>0&&setTimeout((()=>t.abort()),n.timeout),o&&o.addEventListener("abort",(()=>{t.abort()})),e.signal=i}return n&&null!==n.extraBody&&function(e,n){if(!n||0===Object.keys(n).length)return;if(e.body instanceof Blob)return void t("warn","at node_modules/@google/genai/dist/web/index.mjs:13333","includeExtraBodyToRequestInit: extraBody provided but current request body is a Blob. extraBody will be ignored as merging is not supported for Blob bodies.");let o={};if("string"==typeof e.body&&e.body.length>0)try{const n=JSON.parse(e.body);if("object"!=typeof n||null===n||Array.isArray(n))return void t("warn","at node_modules/@google/genai/dist/web/index.mjs:13348","includeExtraBodyToRequestInit: Original request body is valid JSON but not a non-array object. Skip applying extraBody to the request body.");o=n}catch(r){return void t("warn","at node_modules/@google/genai/dist/web/index.mjs:13354","includeExtraBodyToRequestInit: Original request body is not valid JSON. Skip applying extraBody to the request body.")}function i(e,n){const o=Object.assign({},e);for(const s in n)if(Object.prototype.hasOwnProperty.call(n,s)){const e=n[s],r=o[s];e&&"object"==typeof e&&!Array.isArray(e)&&r&&"object"==typeof r&&!Array.isArray(r)?o[s]=i(r,e):(r&&e&&typeof r!=typeof e&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:13376",`includeExtraBodyToRequestInit:deepMerge: Type mismatch for key "${s}". Original type: ${typeof r}, New type: ${typeof e}. Overwriting.`),o[s]=e)}return o}const s=i(o,n);e.body=JSON.stringify(s)}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */(e,n.extraBody),e.headers=await this.getHeadersInternal(n),e}async unaryApiCall(e,t,n){return this.apiCall(e.toString(),Object.assign(Object.assign({},t),{method:n})).then((async e=>(await Za(e),new qo(e)))).catch((e=>{throw e instanceof Error?e:new Error(JSON.stringify(e))}))}async streamApiCall(e,t,n){return this.apiCall(e.toString(),Object.assign(Object.assign({},t),{method:n})).then((async e=>(await Za(e),this.processStreamResponse(e)))).catch((e=>{throw e instanceof Error?e:new Error(JSON.stringify(e))}))}processStreamResponse(e){var t;return qs(this,arguments,(function*(){const n=null===(t=null==e?void 0:e.body)||void 0===t?void 0:t.getReader(),o=new TextDecoder("utf-8");if(!n)throw new Error("Response body is empty");try{let t="";for(;;){const{done:s,value:r}=yield js(n.read());if(s){if(t.trim().length>0)throw new Error("Incomplete JSON segment at the end");break}const a=o.decode(r);try{const e=JSON.parse(a);if("error"in e){const t=JSON.parse(JSON.stringify(e.error)),n=t.status,o=t.code,i=`got status: ${n}. ${JSON.stringify(e)}`;if(o>=400&&o<600){throw new Zs({message:i,status:o})}}}catch(i){if("ApiError"===i.name)throw i}t+=a;let l=t.match(Ja);for(;l;){const n=l[1];try{const o=new Response(n,{headers:null==e?void 0:e.headers,status:null==e?void 0:e.status,statusText:null==e?void 0:e.statusText});yield yield js(new qo(o)),t=t.slice(l[0].length),l=t.match(Ja)}catch(i){throw new Error(`exception parsing stream chunk ${n}. ${i}`)}}}}finally{n.releaseLock()}}))}async apiCall(e,t){return fetch(e,t).catch((e=>{throw new Error(`exception ${e} sending request`)}))}getDefaultHeaders(){const e={},t="google-genai-sdk/1.8.0 "+this.clientOptions.userAgentExtra;return e["User-Agent"]=t,e["x-goog-api-client"]=t,e["Content-Type"]="application/json",e}async getHeadersInternal(e){const t=new Headers;if(e&&e.headers){for(const[n,o]of Object.entries(e.headers))t.append(n,o);e.timeout&&e.timeout>0&&t.append("X-Server-Timeout",String(Math.ceil(e.timeout/1e3)))}return await this.clientOptions.auth.addAuthHeaders(t),t}async uploadFile(e,t){var n;const o={};null!=t&&(o.mimeType=t.mimeType,o.name=t.name,o.displayName=t.displayName),o.name&&!o.name.startsWith("files/")&&(o.name=`files/${o.name}`);const i=this.clientOptions.uploader,s=await i.stat(e);o.sizeBytes=String(s.size);const r=null!==(n=null==t?void 0:t.mimeType)&&void 0!==n?n:s.type;if(void 0===r||""===r)throw new Error("Can not determine mimeType. Please provide mimeType in the config.");o.mimeType=r;const a=await this.fetchUploadUrl(o,t);return i.upload(e,a,this)}async downloadFile(e){const t=this.clientOptions.downloader;await t.download(e,this)}async fetchUploadUrl(e,t){var n;let o={};o=(null==t?void 0:t.httpOptions)?t.httpOptions:{apiVersion:"",headers:{"Content-Type":"application/json","X-Goog-Upload-Protocol":"resumable","X-Goog-Upload-Command":"start","X-Goog-Upload-Header-Content-Length":`${e.sizeBytes}`,"X-Goog-Upload-Header-Content-Type":`${e.mimeType}`}};const i={file:e},s=await this.request({path:rn("upload/v1beta/files",i._url),body:JSON.stringify(i),httpMethod:"POST",httpOptions:o});if(!s||!(null==s?void 0:s.headers))throw new Error("Server did not return an HttpResponse or the returned HttpResponse did not have headers.");const r=null===(n=null==s?void 0:s.headers)||void 0===n?void 0:n["x-goog-upload-url"];if(void 0===r)throw new Error("Failed to get upload url. Server did not return the x-google-upload-url in the headers");return r}}async function Za(e){var t;if(void 0===e)throw new Error("response is undefined");if(!e.ok){const n=e.status;let o;o=(null===(t=e.headers.get("content-type"))||void 0===t?void 0:t.includes("application/json"))?await e.json():{error:{message:await e.text(),code:e.status,status:e.statusText}};const i=JSON.stringify(o);if(n>=400&&n<600){throw new Zs({message:i,status:n})}throw new Error(i)}}function Ya(e){for(const t of e){if(Wa(t))return!0;if("object"==typeof t&&"inputSchema"in t)return!0}return!1}function Ka(e){var t;const n=null!==(t=e["x-goog-api-client"])&&void 0!==t?t:"";e["x-goog-api-client"]=(n+" mcp_used/unknown").trimStart()}function Wa(e){return null!==e&&"object"==typeof e&&e instanceof Xa}function za(e,t=100){return qs(this,arguments,(function*(){let n,o=0;for(;o<t;){const t=yield js(e.listTools({cursor:n}));for(const e of t.tools)yield yield js(e),o++;if(!t.nextCursor)break;n=t.nextCursor}}))}class Xa{constructor(e=[],t){this.mcpTools=[],this.functionNameToMcpClient={},this.mcpClients=e,this.config=t}static create(e,t){return new Xa(e,t)}async initialize(){var e,t,n,o;if(this.mcpTools.length>0)return;const i={},s=[];for(const u of this.mcpClients)try{for(var r,a=!0,l=(t=void 0,Fs(za(u)));!(e=(r=await l.next()).done);a=!0){o=r.value,a=!1;const e=o;s.push(e);const t=e.name;if(i[t])throw new Error(`Duplicate function name ${t} found in MCP tools. Please ensure function names are unique.`);i[t]=u}}catch(c){t={error:c}}finally{try{a||e||!(n=l.return)||await n.call(l)}finally{if(t)throw t.error}}this.mcpTools=s,this.functionNameToMcpClient=i}async tool(){return await this.initialize(),function(e,t={}){const n=[],o=new Set;for(const i of e){const e=i.name;if(o.has(e))throw new Error(`Duplicate function name ${e} found in MCP tools. Please ensure function names are unique.`);o.add(e);const s=Mi(i,t);s.functionDeclarations&&n.push(...s.functionDeclarations)}return{functionDeclarations:n}}(this.mcpTools,this.config)}async callTool(e){await this.initialize();const t=[];for(const n of e)if(n.name in this.functionNameToMcpClient){const e=this.functionNameToMcpClient[n.name];let o;this.config.timeout&&(o={timeout:this.config.timeout});const i=await e.callTool({name:n.name,arguments:n.args},void 0,o);t.push({functionResponse:{name:n.name,response:i.isError?{error:i}:i}})}return t}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class Qa{constructor(e,t,n){this.apiClient=e,this.auth=t,this.webSocketFactory=n}async connect(e){var n,o;if(this.apiClient.isVertexAI())throw new Error("Live music is not supported for Vertex AI.");t("warn","at node_modules/@google/genai/dist/web/index.mjs:13650","Live music generation is experimental and may change in future versions.");const i=this.apiClient.getWebsocketBaseUrl(),s=this.apiClient.getApiVersion(),r=function(e){const t=new Headers;for(const[n,o]of Object.entries(e))t.append(n,o);return t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */(this.apiClient.getDefaultHeaders()),a=`${i}/ws/google.ai.generativelanguage.${s}.GenerativeService.BidiGenerateMusic?key=${this.apiClient.getApiKey()}`;let l=()=>{};const c=new Promise((e=>{l=e})),u=e.callbacks,d=(this.apiClient,{onopen:function(){l({})},onmessage:e=>{!async function(e,t,n){const o=new ii;let i;i=n.data instanceof Blob?JSON.parse(await n.data.text()):JSON.parse(n.data);const s=jr(i);Object.assign(o,s),t(o)}(0,u.onmessage,e)},onerror:null!==(n=null==u?void 0:u.onerror)&&void 0!==n?n:function(e){},onclose:null!==(o=null==u?void 0:u.onclose)&&void 0!==o?o:function(e){}}),p=this.webSocketFactory.create(a,function(e){const t={};return e.forEach(((e,n)=>{t[n]=e})),t}(r),d);p.connect(),await c;const m=br({setup:Er({model:si(this.apiClient,e.model)})});return p.send(JSON.stringify(m)),new el(p,this.apiClient)}}class el{constructor(e,t){this.conn=e,this.apiClient=t}async setWeightedPrompts(e){if(!e.weightedPrompts||0===Object.keys(e.weightedPrompts).length)throw new Error("Weighted prompts must be set and contain at least one entry.");const t=Cr(function(e){const t={},n=ln(e,["weightedPrompts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>yr(e)))),an(t,["weightedPrompts"],e)}return t}(e));this.conn.send(JSON.stringify({clientContent:t}))}async setMusicGenerationConfig(e){e.musicGenerationConfig||(e.musicGenerationConfig={});const t=br(function(e){const t={},n=ln(e,["musicGenerationConfig"]);return null!=n&&an(t,["musicGenerationConfig"],_r(n)),t}(e));this.conn.send(JSON.stringify(t))}sendPlaybackControl(e){const t=br({playbackControl:e});this.conn.send(JSON.stringify(t))}play(){this.sendPlaybackControl(Lo.PLAY)}pause(){this.sendPlaybackControl(Lo.PAUSE)}stop(){this.sendPlaybackControl(Lo.STOP)}resetContext(){this.sendPlaybackControl(Lo.RESET_CONTEXT)}close(){this.conn.close()}}class tl{constructor(e,t,n){this.apiClient=e,this.auth=t,this.webSocketFactory=n,this.music=new Qa(this.apiClient,this.auth,this.webSocketFactory)}async connect(e){var n,o,i,s,r,a;const l=this.apiClient.getWebsocketBaseUrl(),c=this.apiClient.getApiVersion();let u;const d=this.apiClient.getDefaultHeaders();e.config&&e.config.tools&&Ya(e.config.tools)&&Ka(d);const p=function(e){const t=new Headers;for(const[n,o]of Object.entries(e))t.append(n,o);return t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */(d);if(this.apiClient.isVertexAI())u=`${l}/ws/google.cloud.aiplatform.${c}.LlmBidiService/BidiGenerateContent`,await this.auth.addAuthHeaders(p);else{const e=this.apiClient.getApiKey();let n="BidiGenerateContent",o="key";(null==e?void 0:e.startsWith("auth_tokens/"))&&(t("warn","at node_modules/@google/genai/dist/web/index.mjs:13926","Warning: Ephemeral token support is experimental and may change in future versions."),n="BidiGenerateContentConstrained",o="access_token"),u=`${l}/ws/google.ai.generativelanguage.${c}.GenerativeService.${n}?${o}=${e}`}let m=()=>{};const f=new Promise((e=>{m=e})),h=e.callbacks,g=this.apiClient,v={onopen:function(){var e;null===(e=null==h?void 0:h.onopen)||void 0===e||e.call(h),m({})},onmessage:e=>{!async function(e,t,n){const o=new oi;let i;i=n.data instanceof Blob?await n.data.text():n.data instanceof ArrayBuffer?(new TextDecoder).decode(n.data):n.data;const s=JSON.parse(i);if(e.isVertexAI()){const e=Dr(s);Object.assign(o,e)}else{const e=Mr(s);Object.assign(o,e)}t(o)}(g,h.onmessage,e)},onerror:null!==(n=null==h?void 0:h.onerror)&&void 0!==n?n:function(e){},onclose:null!==(o=null==h?void 0:h.onclose)&&void 0!==o?o:function(e){}},y=this.webSocketFactory.create(u,function(e){const t={};return e.forEach(((e,n)=>{t[n]=e})),t}(p),v);y.connect(),await f;let _=si(this.apiClient,e.model);if(this.apiClient.isVertexAI()&&_.startsWith("publishers/")){_=`projects/${this.apiClient.getProject()}/locations/${this.apiClient.getLocation()}/`+_}let E={};this.apiClient.isVertexAI()&&void 0===(null===(i=e.config)||void 0===i?void 0:i.responseModalities)&&(void 0===e.config?e.config={responseModalities:[Fn.AUDIO]}:e.config.responseModalities=[Fn.AUDIO]),(null===(s=e.config)||void 0===s?void 0:s.generationConfig)&&t("warn","at node_modules/@google/genai/dist/web/index.mjs:13978","Setting `LiveConnectConfig.generation_config` is deprecated, please set the fields on `LiveConnectConfig` directly. This will become an error in a future version (not before Q3 2025).");const C=null!==(a=null===(r=e.config)||void 0===r?void 0:r.tools)&&void 0!==a?a:[],b=[];for(const t of C)if(this.isCallableTool(t)){const e=t;b.push(await e.tool())}else b.push(t);b.length>0&&(e.config.tools=b);const T={model:_,config:e.config,callbacks:e.callbacks};return E=this.apiClient.isVertexAI()?function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["setup","model"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],hr(i,n)),n}(this.apiClient,T):function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["setup","model"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],fr(i,n)),n}(this.apiClient,T),delete E.config,y.send(JSON.stringify(E)),new ol(y,this.apiClient)}isCallableTool(e){return"callTool"in e&&"function"==typeof e.callTool}}const nl={turnComplete:!0};class ol{constructor(e,t){this.conn=e,this.apiClient=t}tLiveClientContent(e,t){if(null!==t.turns&&void 0!==t.turns){let o=[];try{o=yi(t.turns),o=e.isVertexAI()?o.map((e=>ia(e))):o.map((e=>Fr(e)))}catch(n){throw new Error(`Failed to parse client content "turns", type: '${typeof t.turns}'`)}return{clientContent:{turns:o,turnComplete:t.turnComplete}}}return{clientContent:{turnComplete:t.turnComplete}}}tLiveClienttToolResponse(e,t){let n=[];if(null==t.functionResponses)throw new Error("functionResponses is required.");if(n=Array.isArray(t.functionResponses)?t.functionResponses:[t.functionResponses],0===n.length)throw new Error("functionResponses is required.");for(const o of n){if("object"!=typeof o||null===o||!("name"in o)||!("response"in o))throw new Error(`Could not parse function response, type '${typeof o}'.`);if(!e.isVertexAI()&&!("id"in o))throw new Error("FunctionResponse request must have an `id` field from the response of a ToolCall.FunctionalCalls in Google AI.")}return{toolResponse:{functionResponses:n}}}sendClientContent(e){e=Object.assign(Object.assign({},nl),e);const t=this.tLiveClientContent(this.apiClient,e);this.conn.send(JSON.stringify(t))}sendRealtimeInput(e){let t={};t=this.apiClient.isVertexAI()?{realtimeInput:vr(e)}:{realtimeInput:gr(e)},this.conn.send(JSON.stringify(t))}sendToolResponse(e){if(null==e.functionResponses)throw new Error("Tool response parameters are required.");const t=this.tLiveClienttToolResponse(this.apiClient,e);this.conn.send(JSON.stringify(t))}close(){this.conn.close()}}function il(e){var n,o,i;if(null===(n=null==e?void 0:e.automaticFunctionCalling)||void 0===n?void 0:n.disable)return!0;let s=!1;for(const t of null!==(o=null==e?void 0:e.tools)&&void 0!==o?o:[])if(sl(t)){s=!0;break}if(!s)return!0;const r=null===(i=null==e?void 0:e.automaticFunctionCalling)||void 0===i?void 0:i.maximumRemoteCalls;return!!(r&&(r<0||!Number.isInteger(r))||0==r)&&(t("warn","at node_modules/@google/genai/dist/web/index.mjs:14264","Invalid maximumRemoteCalls value provided for automatic function calling. Disabled automatic function calling. Please provide a valid integer value greater than 0. maximumRemoteCalls provided:",r),!0)}function sl(e){return"callTool"in e&&"function"==typeof e.callTool}function rl(e){var t;return!(null===(t=null==e?void 0:e.automaticFunctionCalling)||void 0===t?void 0:t.ignoreCallHistory)}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class al extends sn{constructor(e){super(),this.apiClient=e,this.generateContent=async e=>{var t,n,o,i,s;const r=await this.processParamsForMcpUsage(e);if(!function(e){var t,n,o;return null!==(o=null===(n=null===(t=e.config)||void 0===t?void 0:t.tools)||void 0===n?void 0:n.some((e=>Wa(e))))&&void 0!==o&&o}(e)||il(e.config))return await this.generateContentInternal(r);if(function(e){var t,n,o;return null!==(o=null===(n=null===(t=e.config)||void 0===t?void 0:t.tools)||void 0===n?void 0:n.some((e=>!Wa(e))))&&void 0!==o&&o}(e))throw new Error("Automatic function calling with CallableTools and Tools is not yet supported.");let a,l;const c=yi(r.contents),u=null!==(o=null===(n=null===(t=r.config)||void 0===t?void 0:t.automaticFunctionCalling)||void 0===n?void 0:n.maximumRemoteCalls)&&void 0!==o?o:10;let d=0;for(;d<u&&(a=await this.generateContentInternal(r),a.functionCalls&&0!==a.functionCalls.length);){const t=a.candidates[0].content,n=[];for(const o of null!==(s=null===(i=e.config)||void 0===i?void 0:i.tools)&&void 0!==s?s:[])if(sl(o)){const e=o,t=await e.callTool(a.functionCalls);n.push(...t)}d++,l={role:"user",parts:n},r.contents=yi(r.contents),r.contents.push(t),r.contents.push(l),rl(r.config)&&(c.push(t),c.push(l))}return rl(r.config)&&(a.automaticFunctionCallingHistory=c),a},this.generateContentStream=async e=>{if(il(e.config)){const t=await this.processParamsForMcpUsage(e);return await this.generateContentStreamInternal(t)}return await this.processAfcStream(e)},this.generateImages=async e=>await this.generateImagesInternal(e).then((e=>{var t;let n;const o=[];if(null==e?void 0:e.generatedImages)for(const s of e.generatedImages)s&&(null==s?void 0:s.safetyAttributes)&&"Positive Prompt"===(null===(t=null==s?void 0:s.safetyAttributes)||void 0===t?void 0:t.contentType)?n=null==s?void 0:s.safetyAttributes:o.push(s);let i;return i=n?{generatedImages:o,positivePromptSafetyAttributes:n}:{generatedImages:o},i})),this.list=async e=>{var t;const n={config:Object.assign(Object.assign({},{queryBase:!0}),null==e?void 0:e.config)};if(this.apiClient.isVertexAI()&&!n.config.queryBase){if(null===(t=n.config)||void 0===t?void 0:t.filter)throw new Error("Filtering tuned models list for Vertex AI is not currently supported");n.config.filter="labels.tune-type:*"}return new ps(us.PAGED_ITEM_MODELS,(e=>this.listInternal(e)),await this.listInternal(n),n)},this.editImage=async e=>{const t={model:e.model,prompt:e.prompt,referenceImages:[],config:e.config};return e.referenceImages&&e.referenceImages&&(t.referenceImages=e.referenceImages.map((e=>e.toReferenceImageAPI()))),await this.editImageInternal(t)},this.upscaleImage=async e=>{let t={numberOfImages:1,mode:"upscale"};e.config&&(t=Object.assign(Object.assign({},t),e.config));const n={model:e.model,image:e.image,upscaleFactor:e.upscaleFactor,config:t};return await this.upscaleImageInternal(n)}}async processParamsForMcpUsage(e){var t,n,o;const i=null===(t=e.config)||void 0===t?void 0:t.tools;if(!i)return e;const s=await Promise.all(i.map((async e=>{if(sl(e)){const t=e;return await t.tool()}return e}))),r={model:e.model,contents:e.contents,config:Object.assign(Object.assign({},e.config),{tools:s})};if(r.config.tools=s,e.config&&e.config.tools&&Ya(e.config.tools)){const t=null!==(o=null===(n=e.config.httpOptions)||void 0===n?void 0:n.headers)&&void 0!==o?o:{};let i=Object.assign({},t);0===Object.keys(i).length&&(i=this.apiClient.getDefaultHeaders()),Ka(i),r.config.httpOptions=Object.assign(Object.assign({},e.config.httpOptions),{headers:i})}return r}async initAfcToolsMap(e){var t,n,o;const i=new Map;for(const s of null!==(n=null===(t=e.config)||void 0===t?void 0:t.tools)&&void 0!==n?n:[])if(sl(s)){const e=s,t=await e.tool();for(const n of null!==(o=t.functionDeclarations)&&void 0!==o?o:[]){if(!n.name)throw new Error("Function declaration name is required.");if(i.has(n.name))throw new Error(`Duplicate tool declaration name: ${n.name}`);i.set(n.name,e)}}return i}async processAfcStream(e){var t,n,o;const i=null!==(o=null===(n=null===(t=e.config)||void 0===t?void 0:t.automaticFunctionCalling)||void 0===n?void 0:n.maximumRemoteCalls)&&void 0!==o?o:10;let s=!1,r=0;return function(e,t,n){var o,a;return qs(this,arguments,(function*(){for(var l,c,u,d;r<i;){s&&(r++,s=!1);const g=yield js(e.processParamsForMcpUsage(n)),v=yield js(e.generateContentStreamInternal(g)),y=[],_=[];try{for(var p,m=!0,f=(c=void 0,Fs(v));!(l=(p=yield js(f.next())).done);m=!0){d=p.value,m=!1;const e=d;if(yield yield js(e),e.candidates&&(null===(o=e.candidates[0])||void 0===o?void 0:o.content)){_.push(e.candidates[0].content);for(const n of null!==(a=e.candidates[0].content.parts)&&void 0!==a?a:[])if(r<i&&n.functionCall){if(!n.functionCall.name)throw new Error("Function call name was not returned by the model.");if(!t.has(n.functionCall.name))throw new Error(`Automatic function calling was requested, but not all the tools the model used implement the CallableTool interface. Available tools: ${t.keys()}, mising tool: ${n.functionCall.name}`);{const e=yield js(t.get(n.functionCall.name).callTool([n.functionCall]));y.push(...e)}}}}}catch(h){c={error:h}}finally{try{m||l||!(u=f.return)||(yield js(u.call(f)))}finally{if(c)throw c.error}}if(!(y.length>0))break;{s=!0;const e=new Fo;e.candidates=[{content:{role:"user",parts:y}}],yield yield js(e);const t=[];t.push(..._),t.push({role:"user",parts:y});const o=yi(n.contents).concat(t);n.contents=o}}}))}(this,await this.initAfcToolsMap(e),e)}async generateContentInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=ma(this.apiClient,e);return r=rn("{model}:generateContent",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>{const t=Ua(e),n=new Fo;return Object.assign(n,t),n}))}{const t=Wr(this.apiClient,e);return r=rn("{model}:generateContent",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>{const t=Ia(e),n=new Fo;return Object.assign(n,t),n}))}}async generateContentStreamInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=ma(this.apiClient,e);r=rn("{model}:streamGenerateContent?alt=sse",o._url),a=o._query,delete o.config,delete o._url,delete o._query;return s=this.apiClient.requestStream({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}),s.then((function(e){return qs(this,arguments,(function*(){var t,n,o,i;try{for(var s,r=!0,a=Fs(e);!(t=(s=yield js(a.next())).done);r=!0){i=s.value,r=!1;const e=i,t=Ua(yield js(e.json())),n=new Fo;Object.assign(n,t),yield yield js(n)}}catch(l){n={error:l}}finally{try{r||t||!(o=a.return)||(yield js(o.call(a)))}finally{if(n)throw n.error}}}))}))}{const t=Wr(this.apiClient,e);r=rn("{model}:streamGenerateContent?alt=sse",t._url),a=t._query,delete t.config,delete t._url,delete t._query;return s=this.apiClient.requestStream({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}),s.then((function(e){return qs(this,arguments,(function*(){var t,n,o,i;try{for(var s,r=!0,a=Fs(e);!(t=(s=yield js(a.next())).done);r=!0){i=s.value,r=!1;const e=i,t=Ia(yield js(e.json())),n=new Fo;Object.assign(n,t),yield yield js(n)}}catch(l){n={error:l}}finally{try{r||t||!(o=a.return)||(yield js(o.call(a)))}finally{if(n)throw n.error}}}))}))}}async embedContent(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=fa(this.apiClient,e);return r=rn("{model}:predict",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>{const t=ja(e),n=new Bo;return Object.assign(n,t),n}))}{const t=zr(this.apiClient,e);return r=rn("{model}:batchEmbedContents",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>{const t=Oa(e),n=new Bo;return Object.assign(n,t),n}))}}async generateImagesInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=ha(this.apiClient,e);return r=rn("{model}:predict",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["predictions"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Fa(e)))),an(t,["generatedImages"],e)}const o=ln(e,["positivePromptSafetyAttributes"]);return null!=o&&an(t,["positivePromptSafetyAttributes"],qa(o)),t}(e),n=new Go;return Object.assign(n,t),n}))}{const t=Xr(this.apiClient,e);return r=rn("{model}:predict",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["predictions"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>xa(e)))),an(t,["generatedImages"],e)}const o=ln(e,["positivePromptSafetyAttributes"]);return null!=o&&an(t,["positivePromptSafetyAttributes"],Na(o)),t}(e),n=new Go;return Object.assign(n,t),n}))}}async editImageInternal(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI()){const r=ya(this.apiClient,e);return i=rn("{model}:predict",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>{const t=function(e){const t={},n=ln(e,["predictions"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Fa(e)))),an(t,["generatedImages"],e)}return t}(e),n=new $o;return Object.assign(n,t),n}))}throw new Error("This method is only supported by the Vertex AI.")}async upscaleImageInternal(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI()){const r=_a(this.apiClient,e);return i=rn("{model}:predict",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>{const t=function(e){const t={},n=ln(e,["predictions"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>Fa(e)))),an(t,["generatedImages"],e)}return t}(e),n=new Jo;return Object.assign(n,t),n}))}throw new Error("This method is only supported by the Vertex AI.")}async get(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","name"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("{name}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>Ba(e)))}{const t=function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","name"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>ka(e)))}}async listInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=Ea(this.apiClient,e);return r=rn("{models_url}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["_self"]);if(null!=o){let e=Ri(o);Array.isArray(e)&&(e=e.map((e=>Ba(e)))),an(t,["models"],e)}return t}(e),n=new Ho;return Object.assign(n,t),n}))}{const t=Qr(this.apiClient,e);return r=rn("{models_url}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["_self"]);if(null!=o){let e=Ri(o);Array.isArray(e)&&(e=e.map((e=>ka(e)))),an(t,["models"],e)}return t}(e),n=new Ho;return Object.assign(n,t),n}))}}async update(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=Ca(this.apiClient,e);return r=rn("{model}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"PATCH",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>Ba(e)))}{const t=ea(this.apiClient,e);return r=rn("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"PATCH",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>ka(e)))}}async delete(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","name"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("{name}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"DELETE",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((()=>{const e={},t=new Zo;return Object.assign(t,e),t}))}{const t=function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","name"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],i),n}(this.apiClient,e);return r=rn("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"DELETE",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((()=>{const e={},t=new Zo;return Object.assign(t,e),t}))}}async countTokens(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=ba(this.apiClient,e);return r=rn("{model}:countTokens",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["totalTokens"]);return null!=n&&an(t,["totalTokens"],n),t}(e),n=new Yo;return Object.assign(n,t),n}))}{const t=ta(this.apiClient,e);return r=rn("{model}:countTokens",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["totalTokens"]);null!=n&&an(t,["totalTokens"],n);const o=ln(e,["cachedContentTokenCount"]);return null!=o&&an(t,["cachedContentTokenCount"],o),t}(e),n=new Yo;return Object.assign(n,t),n}))}}async computeTokens(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI()){const r=function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["_url","model"],si(e,o));const i=ln(t,["contents"]);if(null!=i){let e=yi(i);Array.isArray(e)&&(e=e.map((e=>ia(e)))),an(n,["contents"],e)}const s=ln(t,["config"]);return null!=s&&an(n,["config"],s),n}(this.apiClient,e);return i=rn("{model}:computeTokens",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>{const t=function(e){const t={},n=ln(e,["tokensInfo"]);return null!=n&&an(t,["tokensInfo"],n),t}(e),n=new Ko;return Object.assign(n,t),n}))}throw new Error("This method is only supported by the Vertex AI.")}async generateVideos(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=Ta(this.apiClient,e);return r=rn("{model}:predictLongRunning",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>$a(e)))}{const t=na(this.apiClient,e);return r=rn("{model}:predictLongRunning",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"POST",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>Ra(e)))}}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function ll(e){const t={},n=ln(e,["_self"]);return null!=n&&an(t,["video"],function(e){const t={},n=ln(e,["video","uri"]);null!=n&&an(t,["uri"],n);const o=ln(e,["video","encodedVideo"]);null!=o&&an(t,["videoBytes"],xi(o));const i=ln(e,["encoding"]);return null!=i&&an(t,["mimeType"],i),t}(n)),t}function cl(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["metadata"]);null!=o&&an(t,["metadata"],o);const i=ln(e,["done"]);null!=i&&an(t,["done"],i);const s=ln(e,["error"]);null!=s&&an(t,["error"],s);const r=ln(e,["response","generateVideoResponse"]);return null!=r&&an(t,["response"],function(e){const t={},n=ln(e,["generatedSamples"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>ll(e)))),an(t,["generatedVideos"],e)}const o=ln(e,["raiMediaFilteredCount"]);null!=o&&an(t,["raiMediaFilteredCount"],o);const i=ln(e,["raiMediaFilteredReasons"]);return null!=i&&an(t,["raiMediaFilteredReasons"],i),t}(r)),t}function ul(e){const t={},n=ln(e,["_self"]);return null!=n&&an(t,["video"],function(e){const t={},n=ln(e,["gcsUri"]);null!=n&&an(t,["uri"],n);const o=ln(e,["bytesBase64Encoded"]);null!=o&&an(t,["videoBytes"],xi(o));const i=ln(e,["mimeType"]);return null!=i&&an(t,["mimeType"],i),t}(n)),t}function dl(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["metadata"]);null!=o&&an(t,["metadata"],o);const i=ln(e,["done"]);null!=i&&an(t,["done"],i);const s=ln(e,["error"]);null!=s&&an(t,["error"],s);const r=ln(e,["response"]);return null!=r&&an(t,["response"],function(e){const t={},n=ln(e,["videos"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>ul(e)))),an(t,["generatedVideos"],e)}const o=ln(e,["raiMediaFilteredCount"]);null!=o&&an(t,["raiMediaFilteredCount"],o);const i=ln(e,["raiMediaFilteredReasons"]);return null!=i&&an(t,["raiMediaFilteredReasons"],i),t}(r)),t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class pl extends sn{constructor(e){super(),this.apiClient=e}async getVideosOperation(e){const t=e.operation,n=e.config;if(void 0===t.name||""===t.name)throw new Error("Operation name is required.");if(this.apiClient.isVertexAI()){const e=t.name.split("/operations/")[0];let o;return n&&"httpOptions"in n&&(o=n.httpOptions),this.fetchPredictVideosOperationInternal({operationName:t.name,resourceName:e,config:{httpOptions:o}})}return this.getVideosOperationInternal({operationName:t.name,config:n})}async getVideosOperationInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=function(e){const t={},n=ln(e,["operationName"]);null!=n&&an(t,["_url","operationName"],n);const o=ln(e,["config"]);return null!=o&&an(t,["config"],o),t}(e);return r=rn("{operationName}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>dl(e)))}{const t=function(e){const t={},n=ln(e,["operationName"]);null!=n&&an(t,["_url","operationName"],n);const o=ln(e,["config"]);return null!=o&&an(t,["config"],o),t}(e);return r=rn("{operationName}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>cl(e)))}}async fetchPredictVideosOperationInternal(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI()){const r=function(e){const t={},n=ln(e,["operationName"]);null!=n&&an(t,["operationName"],n);const o=ln(e,["resourceName"]);null!=o&&an(t,["_url","resourceName"],o);const i=ln(e,["config"]);return null!=i&&an(t,["config"],i),t}(e);return i=rn("{resourceName}:fetchPredictOperation",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>dl(e)))}throw new Error("This method is only supported by the Vertex AI.")}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function ml(e){const t={},n=ln(e,["prebuiltVoiceConfig"]);return null!=n&&an(t,["prebuiltVoiceConfig"],function(e){const t={},n=ln(e,["voiceName"]);return null!=n&&an(t,["voiceName"],n),t}(n)),t}function fl(e){const t={},n=ln(e,["speakerVoiceConfigs"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["speaker"]);null!=n&&an(t,["speaker"],n);const o=ln(e,["voiceConfig"]);return null!=o&&an(t,["voiceConfig"],ml(o)),t}(e)))),an(t,["speakerVoiceConfigs"],e)}return t}function hl(e){const t={},n=ln(e,["videoMetadata"]);null!=n&&an(t,["videoMetadata"],function(e){const t={},n=ln(e,["fps"]);null!=n&&an(t,["fps"],n);const o=ln(e,["endOffset"]);null!=o&&an(t,["endOffset"],o);const i=ln(e,["startOffset"]);return null!=i&&an(t,["startOffset"],i),t}(n));const o=ln(e,["thought"]);null!=o&&an(t,["thought"],o);const i=ln(e,["inlineData"]);null!=i&&an(t,["inlineData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["data"]);null!=n&&an(t,["data"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(i));const s=ln(e,["fileData"]);null!=s&&an(t,["fileData"],function(e){const t={};if(void 0!==ln(e,["displayName"]))throw new Error("displayName parameter is not supported in Gemini API.");const n=ln(e,["fileUri"]);null!=n&&an(t,["fileUri"],n);const o=ln(e,["mimeType"]);return null!=o&&an(t,["mimeType"],o),t}(s));const r=ln(e,["thoughtSignature"]);null!=r&&an(t,["thoughtSignature"],r);const a=ln(e,["codeExecutionResult"]);null!=a&&an(t,["codeExecutionResult"],a);const l=ln(e,["executableCode"]);null!=l&&an(t,["executableCode"],l);const c=ln(e,["functionCall"]);null!=c&&an(t,["functionCall"],c);const u=ln(e,["functionResponse"]);null!=u&&an(t,["functionResponse"],u);const d=ln(e,["text"]);return null!=d&&an(t,["text"],d),t}function gl(e){const t={},n=ln(e,["timeRangeFilter"]);return null!=n&&an(t,["timeRangeFilter"],function(e){const t={},n=ln(e,["startTime"]);null!=n&&an(t,["startTime"],n);const o=ln(e,["endTime"]);return null!=o&&an(t,["endTime"],o),t}(n)),t}function vl(e){const t={},n=ln(e,["dynamicRetrievalConfig"]);return null!=n&&an(t,["dynamicRetrievalConfig"],function(e){const t={},n=ln(e,["mode"]);null!=n&&an(t,["mode"],n);const o=ln(e,["dynamicThreshold"]);return null!=o&&an(t,["dynamicThreshold"],o),t}(n)),t}function yl(e){const t={},n=ln(e,["functionDeclarations"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["behavior"]);null!=n&&an(t,["behavior"],n);const o=ln(e,["description"]);null!=o&&an(t,["description"],o);const i=ln(e,["name"]);null!=i&&an(t,["name"],i);const s=ln(e,["parameters"]);null!=s&&an(t,["parameters"],s);const r=ln(e,["parametersJsonSchema"]);null!=r&&an(t,["parametersJsonSchema"],r);const a=ln(e,["response"]);null!=a&&an(t,["response"],a);const l=ln(e,["responseJsonSchema"]);return null!=l&&an(t,["responseJsonSchema"],l),t}(e)))),an(t,["functionDeclarations"],e)}if(void 0!==ln(e,["retrieval"]))throw new Error("retrieval parameter is not supported in Gemini API.");const o=ln(e,["googleSearch"]);null!=o&&an(t,["googleSearch"],gl(o));const i=ln(e,["googleSearchRetrieval"]);if(null!=i&&an(t,["googleSearchRetrieval"],vl(i)),void 0!==ln(e,["enterpriseWebSearch"]))throw new Error("enterpriseWebSearch parameter is not supported in Gemini API.");if(void 0!==ln(e,["googleMaps"]))throw new Error("googleMaps parameter is not supported in Gemini API.");null!=ln(e,["urlContext"])&&an(t,["urlContext"],{});const s=ln(e,["codeExecution"]);null!=s&&an(t,["codeExecution"],s);const r=ln(e,["computerUse"]);return null!=r&&an(t,["computerUse"],r),t}function _l(e){const t={},n=ln(e,["automaticActivityDetection"]);null!=n&&an(t,["automaticActivityDetection"],function(e){const t={},n=ln(e,["disabled"]);null!=n&&an(t,["disabled"],n);const o=ln(e,["startOfSpeechSensitivity"]);null!=o&&an(t,["startOfSpeechSensitivity"],o);const i=ln(e,["endOfSpeechSensitivity"]);null!=i&&an(t,["endOfSpeechSensitivity"],i);const s=ln(e,["prefixPaddingMs"]);null!=s&&an(t,["prefixPaddingMs"],s);const r=ln(e,["silenceDurationMs"]);return null!=r&&an(t,["silenceDurationMs"],r),t}(n));const o=ln(e,["activityHandling"]);null!=o&&an(t,["activityHandling"],o);const i=ln(e,["turnCoverage"]);return null!=i&&an(t,["turnCoverage"],i),t}function El(e){const t={},n=ln(e,["triggerTokens"]);null!=n&&an(t,["triggerTokens"],n);const o=ln(e,["slidingWindow"]);return null!=o&&an(t,["slidingWindow"],function(e){const t={},n=ln(e,["targetTokens"]);return null!=n&&an(t,["targetTokens"],n),t}(o)),t}function Cl(e,t){const n=ln(e,["generationConfig"]);void 0!==t&&null!=n&&an(t,["setup","generationConfig"],n);const o=ln(e,["responseModalities"]);void 0!==t&&null!=o&&an(t,["setup","generationConfig","responseModalities"],o);const i=ln(e,["temperature"]);void 0!==t&&null!=i&&an(t,["setup","generationConfig","temperature"],i);const s=ln(e,["topP"]);void 0!==t&&null!=s&&an(t,["setup","generationConfig","topP"],s);const r=ln(e,["topK"]);void 0!==t&&null!=r&&an(t,["setup","generationConfig","topK"],r);const a=ln(e,["maxOutputTokens"]);void 0!==t&&null!=a&&an(t,["setup","generationConfig","maxOutputTokens"],a);const l=ln(e,["mediaResolution"]);void 0!==t&&null!=l&&an(t,["setup","generationConfig","mediaResolution"],l);const c=ln(e,["seed"]);void 0!==t&&null!=c&&an(t,["setup","generationConfig","seed"],c);const u=ln(e,["speechConfig"]);void 0!==t&&null!=u&&an(t,["setup","generationConfig","speechConfig"],function(e){const t={},n=ln(e,["voiceConfig"]);null!=n&&an(t,["voiceConfig"],ml(n));const o=ln(e,["multiSpeakerVoiceConfig"]);null!=o&&an(t,["multiSpeakerVoiceConfig"],fl(o));const i=ln(e,["languageCode"]);return null!=i&&an(t,["languageCode"],i),t}(wi(u)));const d=ln(e,["enableAffectiveDialog"]);void 0!==t&&null!=d&&an(t,["setup","generationConfig","enableAffectiveDialog"],d);const p=ln(e,["systemInstruction"]);void 0!==t&&null!=p&&an(t,["setup","systemInstruction"],function(e){const t={},n=ln(e,["parts"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>hl(e)))),an(t,["parts"],e)}const o=ln(e,["role"]);return null!=o&&an(t,["role"],o),t}(gi(p)));const m=ln(e,["tools"]);if(void 0!==t&&null!=m){let e=Ii(m);Array.isArray(e)&&(e=e.map((e=>yl(Ai(e))))),an(t,["setup","tools"],e)}const f=ln(e,["sessionResumption"]);void 0!==t&&null!=f&&an(t,["setup","sessionResumption"],function(e){const t={},n=ln(e,["handle"]);if(null!=n&&an(t,["handle"],n),void 0!==ln(e,["transparent"]))throw new Error("transparent parameter is not supported in Gemini API.");return t}(f));const h=ln(e,["inputAudioTranscription"]);void 0!==t&&null!=h&&an(t,["setup","inputAudioTranscription"],{});const g=ln(e,["outputAudioTranscription"]);void 0!==t&&null!=g&&an(t,["setup","outputAudioTranscription"],{});const v=ln(e,["realtimeInputConfig"]);void 0!==t&&null!=v&&an(t,["setup","realtimeInputConfig"],_l(v));const y=ln(e,["contextWindowCompression"]);void 0!==t&&null!=y&&an(t,["setup","contextWindowCompression"],El(y));const _=ln(e,["proactivity"]);return void 0!==t&&null!=_&&an(t,["setup","proactivity"],function(e){const t={},n=ln(e,["proactiveAudio"]);return null!=n&&an(t,["proactiveAudio"],n),t}(_)),{}}function bl(e,t,n){const o=ln(t,["expireTime"]);void 0!==n&&null!=o&&an(n,["expireTime"],o);const i=ln(t,["newSessionExpireTime"]);void 0!==n&&null!=i&&an(n,["newSessionExpireTime"],i);const s=ln(t,["uses"]);void 0!==n&&null!=s&&an(n,["uses"],s);const r=ln(t,["liveConnectConstraints"]);void 0!==n&&null!=r&&an(n,["bidiGenerateContentSetup"],function(e,t){const n={},o=ln(t,["model"]);null!=o&&an(n,["setup","model"],si(e,o));const i=ln(t,["config"]);return null!=i&&an(n,["config"],Cl(i,n)),n}(e,r));const a=ln(t,["lockAdditionalFields"]);return void 0!==n&&null!=a&&an(n,["fieldMask"],a),{}}function Tl(e,t){let n=null;const o=e.bidiGenerateContentSetup;if("object"==typeof o&&null!==o&&"setup"in o){const t=o.setup;"object"==typeof t&&null!==t?(e.bidiGenerateContentSetup=t,n=t):delete e.bidiGenerateContentSetup}else void 0!==o&&delete e.bidiGenerateContentSetup;const i=e.fieldMask;if(n){const o=
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */
function(e){const t=[];for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)){const o=e[n];if("object"==typeof o&&null!=o&&Object.keys(o).length>0){const e=Object.keys(o).map((e=>`${n}.${e}`));t.push(...e)}else t.push(n)}return t.join(",")}(n);if(Array.isArray(null==t?void 0:t.lockAdditionalFields)&&0===(null==t?void 0:t.lockAdditionalFields.length))o?e.fieldMask=o:delete e.fieldMask;else if((null==t?void 0:t.lockAdditionalFields)&&t.lockAdditionalFields.length>0&&null!==i&&Array.isArray(i)&&i.length>0){const t=["temperature","topK","topP","maxOutputTokens","responseModalities","seed","speechConfig"];let n=[];i.length>0&&(n=i.map((e=>t.includes(e)?`generationConfig.${e}`:e)));const s=[];o&&s.push(o),n.length>0&&s.push(...n),s.length>0?e.fieldMask=s.join(","):delete e.fieldMask}else delete e.fieldMask}else null!==i&&Array.isArray(i)&&i.length>0?e.fieldMask=i.join(","):delete e.fieldMask;return e}class Sl extends sn{constructor(e){super(),this.apiClient=e}async create(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI())throw new Error("The client.tokens.create method is only supported by the Gemini Developer API.");{const r=function(e,t){const n={},o=ln(t,["config"]);return null!=o&&an(n,["config"],bl(e,o,n)),n}(this.apiClient,e);i=rn("auth_tokens",r._url),s=r._query,delete r.config,delete r._url,delete r._query;const a=Tl(r,e.config);return o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(a),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>function(e){const t={},n=ln(e,["name"]);return null!=n&&an(t,["name"],n),t}(e)))}}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */function wl(e){const t={},n=ln(e,["config"]);return null!=n&&an(t,["config"],function(e,t){const n=ln(e,["pageSize"]);void 0!==t&&null!=n&&an(t,["_query","pageSize"],n);const o=ln(e,["pageToken"]);void 0!==t&&null!=o&&an(t,["_query","pageToken"],o);const i=ln(e,["filter"]);return void 0!==t&&null!=i&&an(t,["_query","filter"],i),{}}(n,t)),t}function Al(e){const t={};if(void 0!==ln(e,["gcsUri"]))throw new Error("gcsUri parameter is not supported in Gemini API.");if(void 0!==ln(e,["vertexDatasetResource"]))throw new Error("vertexDatasetResource parameter is not supported in Gemini API.");const n=ln(e,["examples"]);if(null!=n){let e=n;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["textInput"]);null!=n&&an(t,["textInput"],n);const o=ln(e,["output"]);return null!=o&&an(t,["output"],o),t}(e)))),an(t,["examples","examples"],e)}return t}function Il(e){const t={},n=ln(e,["baseModel"]);null!=n&&an(t,["baseModel"],n);const o=ln(e,["trainingDataset"]);null!=o&&an(t,["tuningTask","trainingData"],Al(o));const i=ln(e,["config"]);return null!=i&&an(t,["config"],function(e,t){const n={};if(void 0!==ln(e,["validationDataset"]))throw new Error("validationDataset parameter is not supported in Gemini API.");const o=ln(e,["tunedModelDisplayName"]);if(void 0!==t&&null!=o&&an(t,["displayName"],o),void 0!==ln(e,["description"]))throw new Error("description parameter is not supported in Gemini API.");const i=ln(e,["epochCount"]);void 0!==t&&null!=i&&an(t,["tuningTask","hyperparameters","epochCount"],i);const s=ln(e,["learningRateMultiplier"]);if(null!=s&&an(n,["tuningTask","hyperparameters","learningRateMultiplier"],s),void 0!==ln(e,["exportLastCheckpointOnly"]))throw new Error("exportLastCheckpointOnly parameter is not supported in Gemini API.");if(void 0!==ln(e,["adapterSize"]))throw new Error("adapterSize parameter is not supported in Gemini API.");const r=ln(e,["batchSize"]);void 0!==t&&null!=r&&an(t,["tuningTask","hyperparameters","batchSize"],r);const a=ln(e,["learningRate"]);return void 0!==t&&null!=a&&an(t,["tuningTask","hyperparameters","learningRate"],a),n}(i,t)),t}function Ol(e){const t={},n=ln(e,["config"]);return null!=n&&an(t,["config"],function(e,t){const n=ln(e,["pageSize"]);void 0!==t&&null!=n&&an(t,["_query","pageSize"],n);const o=ln(e,["pageToken"]);void 0!==t&&null!=o&&an(t,["_query","pageToken"],o);const i=ln(e,["filter"]);return void 0!==t&&null!=i&&an(t,["_query","filter"],i),{}}(n,t)),t}function Nl(e,t){const n={},o=ln(e,["validationDataset"]);void 0!==t&&null!=o&&an(t,["supervisedTuningSpec"],function(e,t){const n={},o=ln(e,["gcsUri"]);null!=o&&an(n,["validationDatasetUri"],o);const i=ln(e,["vertexDatasetResource"]);return void 0!==t&&null!=i&&an(t,["supervisedTuningSpec","trainingDatasetUri"],i),n}(o,n));const i=ln(e,["tunedModelDisplayName"]);void 0!==t&&null!=i&&an(t,["tunedModelDisplayName"],i);const s=ln(e,["description"]);void 0!==t&&null!=s&&an(t,["description"],s);const r=ln(e,["epochCount"]);void 0!==t&&null!=r&&an(t,["supervisedTuningSpec","hyperParameters","epochCount"],r);const a=ln(e,["learningRateMultiplier"]);void 0!==t&&null!=a&&an(t,["supervisedTuningSpec","hyperParameters","learningRateMultiplier"],a);const l=ln(e,["exportLastCheckpointOnly"]);void 0!==t&&null!=l&&an(t,["supervisedTuningSpec","exportLastCheckpointOnly"],l);const c=ln(e,["adapterSize"]);if(void 0!==t&&null!=c&&an(t,["supervisedTuningSpec","hyperParameters","adapterSize"],c),void 0!==ln(e,["batchSize"]))throw new Error("batchSize parameter is not supported in Vertex AI.");if(void 0!==ln(e,["learningRate"]))throw new Error("learningRate parameter is not supported in Vertex AI.");return n}function xl(e){const t={},n=ln(e,["baseModel"]);null!=n&&an(t,["baseModel"],n);const o=ln(e,["trainingDataset"]);null!=o&&an(t,["supervisedTuningSpec","trainingDatasetUri"],function(e,t){const n=ln(e,["gcsUri"]);void 0!==t&&null!=n&&an(t,["supervisedTuningSpec","trainingDatasetUri"],n);const o=ln(e,["vertexDatasetResource"]);if(void 0!==t&&null!=o&&an(t,["supervisedTuningSpec","trainingDatasetUri"],o),void 0!==ln(e,["examples"]))throw new Error("examples parameter is not supported in Vertex AI.");return{}}(o,t));const i=ln(e,["config"]);return null!=i&&an(t,["config"],Nl(i,t)),t}function kl(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["state"]);null!=o&&an(t,["state"],Ni(o));const i=ln(e,["createTime"]);null!=i&&an(t,["createTime"],i);const s=ln(e,["tuningTask","startTime"]);null!=s&&an(t,["startTime"],s);const r=ln(e,["tuningTask","completeTime"]);null!=r&&an(t,["endTime"],r);const a=ln(e,["updateTime"]);null!=a&&an(t,["updateTime"],a);const l=ln(e,["description"]);null!=l&&an(t,["description"],l);const c=ln(e,["baseModel"]);null!=c&&an(t,["baseModel"],c);const u=ln(e,["_self"]);null!=u&&an(t,["tunedModel"],function(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["model"],n);const o=ln(e,["name"]);return null!=o&&an(t,["endpoint"],o),t}(u));const d=ln(e,["distillationSpec"]);null!=d&&an(t,["distillationSpec"],d);const p=ln(e,["experiment"]);null!=p&&an(t,["experiment"],p);const m=ln(e,["labels"]);null!=m&&an(t,["labels"],m);const f=ln(e,["pipelineJob"]);null!=f&&an(t,["pipelineJob"],f);const h=ln(e,["satisfiesPzi"]);null!=h&&an(t,["satisfiesPzi"],h);const g=ln(e,["satisfiesPzs"]);null!=g&&an(t,["satisfiesPzs"],g);const v=ln(e,["serviceAccount"]);null!=v&&an(t,["serviceAccount"],v);const y=ln(e,["tunedModelDisplayName"]);return null!=y&&an(t,["tunedModelDisplayName"],y),t}function Pl(e){const t={},n=ln(e,["model"]);null!=n&&an(t,["model"],n);const o=ln(e,["endpoint"]);null!=o&&an(t,["endpoint"],o);const i=ln(e,["checkpoints"]);if(null!=i){let e=i;Array.isArray(e)&&(e=e.map((e=>function(e){const t={},n=ln(e,["checkpointId"]);null!=n&&an(t,["checkpointId"],n);const o=ln(e,["epoch"]);null!=o&&an(t,["epoch"],o);const i=ln(e,["step"]);null!=i&&an(t,["step"],i);const s=ln(e,["endpoint"]);return null!=s&&an(t,["endpoint"],s),t}(e)))),an(t,["checkpoints"],e)}return t}function Rl(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["state"]);null!=o&&an(t,["state"],Ni(o));const i=ln(e,["createTime"]);null!=i&&an(t,["createTime"],i);const s=ln(e,["startTime"]);null!=s&&an(t,["startTime"],s);const r=ln(e,["endTime"]);null!=r&&an(t,["endTime"],r);const a=ln(e,["updateTime"]);null!=a&&an(t,["updateTime"],a);const l=ln(e,["error"]);null!=l&&an(t,["error"],l);const c=ln(e,["description"]);null!=c&&an(t,["description"],c);const u=ln(e,["baseModel"]);null!=u&&an(t,["baseModel"],u);const d=ln(e,["tunedModel"]);null!=d&&an(t,["tunedModel"],Pl(d));const p=ln(e,["supervisedTuningSpec"]);null!=p&&an(t,["supervisedTuningSpec"],p);const m=ln(e,["tuningDataStats"]);null!=m&&an(t,["tuningDataStats"],m);const f=ln(e,["encryptionSpec"]);null!=f&&an(t,["encryptionSpec"],f);const h=ln(e,["partnerModelTuningSpec"]);null!=h&&an(t,["partnerModelTuningSpec"],h);const g=ln(e,["distillationSpec"]);null!=g&&an(t,["distillationSpec"],g);const v=ln(e,["experiment"]);null!=v&&an(t,["experiment"],v);const y=ln(e,["labels"]);null!=y&&an(t,["labels"],y);const _=ln(e,["pipelineJob"]);null!=_&&an(t,["pipelineJob"],_);const E=ln(e,["satisfiesPzi"]);null!=E&&an(t,["satisfiesPzi"],E);const C=ln(e,["satisfiesPzs"]);null!=C&&an(t,["satisfiesPzs"],C);const b=ln(e,["serviceAccount"]);null!=b&&an(t,["serviceAccount"],b);const T=ln(e,["tunedModelDisplayName"]);return null!=T&&an(t,["tunedModelDisplayName"],T),t}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */
class Ml extends sn{constructor(e){super(),this.apiClient=e,this.get=async e=>await this.getInternal(e),this.list=async(e={})=>new ps(us.PAGED_ITEM_TUNING_JOBS,(e=>this.listInternal(e)),await this.listInternal(e),e),this.tune=async e=>{if(this.apiClient.isVertexAI())return await this.tuneInternal(e);{const t=await this.tuneMldevInternal(e);let n="";void 0!==t.metadata&&void 0!==t.metadata.tunedModel?n=t.metadata.tunedModel:void 0!==t.name&&t.name.includes("/operations/")&&(n=t.name.split("/operations/")[0]);return{name:n,state:Jn.JOB_STATE_QUEUED}}}}async getInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=function(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["_url","name"],n);const o=ln(e,["config"]);return null!=o&&an(t,["config"],o),t}(e);return r=rn("{name}",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>Rl(e)))}{const t=function(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["_url","name"],n);const o=ln(e,["config"]);return null!=o&&an(t,["config"],o),t}(e);return r=rn("{name}",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>kl(e)))}}async listInternal(e){var t,n,o,i;let s,r="",a={};if(this.apiClient.isVertexAI()){const o=Ol(e);return r=rn("tuningJobs",o._url),a=o._query,delete o.config,delete o._url,delete o._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(o),httpMethod:"GET",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["tuningJobs"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>Rl(e)))),an(t,["tuningJobs"],e)}return t}(e),n=new Wo;return Object.assign(n,t),n}))}{const t=wl(e);return r=rn("tunedModels",t._url),a=t._query,delete t.config,delete t._url,delete t._query,s=this.apiClient.request({path:r,queryParams:a,body:JSON.stringify(t),httpMethod:"GET",httpOptions:null===(o=e.config)||void 0===o?void 0:o.httpOptions,abortSignal:null===(i=e.config)||void 0===i?void 0:i.abortSignal}).then((e=>e.json())),s.then((e=>{const t=function(e){const t={},n=ln(e,["nextPageToken"]);null!=n&&an(t,["nextPageToken"],n);const o=ln(e,["tunedModels"]);if(null!=o){let e=o;Array.isArray(e)&&(e=e.map((e=>kl(e)))),an(t,["tuningJobs"],e)}return t}(e),n=new Wo;return Object.assign(n,t),n}))}}async tuneInternal(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI()){const r=xl(e);return i=rn("tuningJobs",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>Rl(e)))}throw new Error("This method is only supported by the Vertex AI.")}async tuneMldevInternal(e){var t,n;let o,i="",s={};if(this.apiClient.isVertexAI())throw new Error("This method is only supported by the Gemini Developer API.");{const r=Il(e);return i=rn("tunedModels",r._url),s=r._query,delete r.config,delete r._url,delete r._query,o=this.apiClient.request({path:i,queryParams:s,body:JSON.stringify(r),httpMethod:"POST",httpOptions:null===(t=e.config)||void 0===t?void 0:t.httpOptions,abortSignal:null===(n=e.config)||void 0===n?void 0:n.abortSignal}).then((e=>e.json())),o.then((e=>function(e){const t={},n=ln(e,["name"]);null!=n&&an(t,["name"],n);const o=ln(e,["metadata"]);null!=o&&an(t,["metadata"],o);const i=ln(e,["done"]);null!=i&&an(t,["done"],i);const s=ln(e,["error"]);return null!=s&&an(t,["error"],s),t}(e)))}}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class Dl{async download(e,t){throw new Error("Download to file is not supported in the browser, please use a browser compliant download like an <a> tag.")}}function Vl(e){return new Promise((t=>setTimeout(t,e)))}class Ul{async upload(e,t,n){if("string"==typeof e)throw new Error("File path is not supported in browser uploader.");return await async function(e,t,n){var o,i,s;let r=0,a=0,l=new qo(new Response),c="upload";for(r=e.size;a<r;){const s=Math.min(8388608,r-a),u=e.slice(a,a+s);a+s>=r&&(c+=", finalize");let d=0,p=1e3;for(;d<3&&(l=await n.request({path:"",body:u,httpMethod:"POST",httpOptions:{apiVersion:"",baseUrl:t,headers:{"X-Goog-Upload-Command":c,"X-Goog-Upload-Offset":String(a),"Content-Length":String(s)}}}),!(null===(o=null==l?void 0:l.headers)||void 0===o?void 0:o["x-goog-upload-status"]));)d++,await Vl(p),p*=2;if(a+=s,"active"!==(null===(i=null==l?void 0:l.headers)||void 0===i?void 0:i["x-goog-upload-status"]))break;if(r<=a)throw new Error("All content has been uploaded, but the upload status is not finalized.")}const u=await(null==l?void 0:l.json());if("final"!==(null===(s=null==l?void 0:l.headers)||void 0===s?void 0:s["x-goog-upload-status"]))throw new Error("Failed to upload file: Upload status is not finalized.");return u.file}(e,t,n)}async stat(e){if("string"==typeof e)throw new Error("File path is not supported in browser uploader.");return await async function(e){return{size:e.size,type:e.type}}(e)}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class Ll{create(e,t,n){return new jl(e,t,n)}}class jl{constructor(e,t,n){this.url=e,this.headers=t,this.callbacks=n}connect(){this.ws=new WebSocket(this.url),this.ws.onopen=this.callbacks.onopen,this.ws.onerror=this.callbacks.onerror,this.ws.onclose=this.callbacks.onclose,this.ws.onmessage=this.callbacks.onmessage}send(e){if(void 0===this.ws)throw new Error("WebSocket is not connected");this.ws.send(e)}close(){if(void 0===this.ws)throw new Error("WebSocket is not connected");this.ws.close()}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */const ql="x-goog-api-key";class Fl{constructor(e){this.apiKey=e}async addAuthHeaders(e){if(null===e.get(ql)){if(this.apiKey.startsWith("auth_tokens/"))throw new Error("Ephemeral tokens are only supported by the live API.");if(!this.apiKey)throw new Error("API key is missing. Please provide a valid API key.");e.append(ql,this.apiKey)}}}
/**
   * @license
   * Copyright 2025 Google LLC
   * SPDX-License-Identifier: Apache-2.0
   */class Bl{constructor(e){var t;if(null==e.apiKey)throw new Error("An API Key must be set when running in a browser");if(e.project||e.location)throw new Error("Vertex AI project based authentication is not supported on browser runtimes. Please do not provide a project or location.");this.vertexai=null!==(t=e.vertexai)&&void 0!==t&&t,this.apiKey=e.apiKey;const n=on(e,void 0,void 0);n&&(e.httpOptions?e.httpOptions.baseUrl=n:e.httpOptions={baseUrl:n}),this.apiVersion=e.apiVersion;const o=new Fl(this.apiKey);this.apiClient=new Ha({auth:o,apiVersion:this.apiVersion,apiKey:this.apiKey,vertexai:this.vertexai,httpOptions:e.httpOptions,userAgentExtra:"gl-node/web",uploader:new Ul,downloader:new Dl}),this.models=new al(this.apiClient),this.live=new tl(this.apiClient,o,new Ll),this.batches=new ms(this.apiClient),this.chats=new Js(this.models,this.apiClient),this.caches=new Us(this.apiClient),this.files=new zs(this.apiClient),this.operations=new pl(this.apiClient),this.authTokens=new Sl(this.apiClient),this.tunings=new Ml(this.apiClient)}}const Gl=de("gemini",(()=>{const n=e.ref(null),o=e.ref(!1),i=e.ref("disconnected"),s=e.ref(null),r=e.ref(0),a=e.ref(3),l=e.ref("idle"),c=e.ref(""),u=e.ref("技术面试"),d=e.ref([]),p=e.ref([]),m=e.ref(!1),f=e.ref(!1),h=e.ref(null),g=e.ref(null),v={format:"PCM_16",channels:1,sendSampleRate:16e3,receiveSampleRate:24e3,chunkSize:1024},y={model:"gemini-2.0-flash-live-001",config:{responseModalities:[Fn.AUDIO]}},_=e.computed((()=>o.value&&n.value)),E=async()=>{if(!n.value||!o.value){if(!c.value)throw new Error("临时令牌不存在");i.value="connecting";try{let s;t("log","at stores/gemini.js:85","尝试连接Gemini Live API，临时令牌:",c.value.substring(0,20)+"...");try{s=new Bl({apiKey:c.value})}catch(e){t("log","at stores/gemini.js:95","方法1失败，尝试方法2:",e.message),s=new Bl({apiKey:"placeholder"})}if(!s.live)throw new Error("GoogleGenAI SDK不支持live API，请检查SDK版本");t("log","at stores/gemini.js:107","GoogleGenAI实例创建成功，开始连接Live API...");const a=await s.live.connect({model:y.model,callbacks:{onopen:()=>{t("log","at stores/gemini.js:115","Live API WebSocket连接已打开")},onmessage:e=>{t("log","at stores/gemini.js:118","收到Live API消息:",e),C(e)},onclose:e=>{t("log","at stores/gemini.js:122","Live API WebSocket连接已关闭:",e),o.value=!1},onerror:e=>{t("error","at stores/gemini.js:126","Live API WebSocket错误:",e)}},config:{...y.config,httpOptions:{params:{access_token:c.value},headers:{Authorization:`Token ${c.value}`}}}});n.value=a,o.value=!0,i.value="connected",r.value=0,l.value="idle",b(),t("log","at stores/gemini.js:154","Gemini Live API连接成功")}catch(e){throw t("error","at stores/gemini.js:156","Gemini Live API连接失败:",e),t("error","at stores/gemini.js:157","错误详情:",e.message),i.value="error",N(),e}}},C=e=>{var n;try{if(t("log","at stores/gemini.js:167","处理Live API消息:",e),e.setupComplete&&t("log","at stores/gemini.js:170","Live API设置完成，会话ID:",e.setupComplete.sessionId),e.serverContent){const o=e.serverContent;if(o.modelTurn){const e=o.modelTurn;if(e.parts)for(const o of e.parts)o.text&&(t("log","at stores/gemini.js:184","AI回复文本:",o.text),s.value={text:o.text}),o.inlineData&&(null==(n=o.inlineData.mimeType)?void 0:n.includes("audio"))&&(t("log","at stores/gemini.js:190","收到AI音频回复"),d.value.push(o.inlineData.data),I(o.inlineData.data))}if(o.turnComplete&&(t("log","at stores/gemini.js:200","AI回合完成"),l.value="active"),o.interrupted)for(t("log","at stores/gemini.js:206","检测到中断信号，清空音频队列");d.value.length>0;)d.value.shift()}e.toolCall&&t("log","at stores/gemini.js:214","收到工具调用请求:",e.toolCall)}catch(o){t("error","at stores/gemini.js:219","处理Live API消息失败:",o)}},b=async()=>{t("log","at stores/gemini.js:226","Live API消息接收通过回调处理")},T=e=>{try{uni.createSpeechSynthesisUtterance&&uni.createSpeechSynthesisUtterance({text:e,lang:"zh-CN",rate:1,pitch:1,volume:1,success:()=>{t("log","at stores/gemini.js:282","语音播放成功"),l.value="active"},fail:e=>{t("error","at stores/gemini.js:286","语音播放失败:",e),l.value="active"}})}catch(n){t("error","at stores/gemini.js:291","语音合成失败:",n),l.value="active"}},S=async()=>{if(m.value||!o.value)return!1;try{h.value=uni.getRecorderManager();const e={duration:6e5,sampleRate:v.sendSampleRate,numberOfChannels:v.channels,encodeBitRate:48e3,format:"PCM",frameSize:v.chunkSize};return h.value.onStart((()=>{m.value=!0,t("log","at stores/gemini.js:314","开始录音")})),h.value.onFrameRecorded((e=>{A(e.frameBuffer)})),h.value.onError((e=>{t("error","at stores/gemini.js:323","录音错误:",e),m.value=!1})),h.value.start(e),!0}catch(e){return t("error","at stores/gemini.js:330","开始录音失败:",e),!1}},w=()=>{h.value&&m.value&&(h.value.stop(),m.value=!1,t("log","at stores/gemini.js:340","停止录音"))},A=async e=>{if(!_.value)return!1;try{const o=new Blob([e],{type:"audio/pcm"});return n.value.sendRealtimeInput({audio:o}),t("log","at stores/gemini.js:357","音频帧发送成功，大小:",e.byteLength),!0}catch(o){return t("error","at stores/gemini.js:360","发送音频帧失败:",o),!1}},I=async e=>{try{g.value||(g.value=uni.createInnerAudioContext(),g.value.onEnded((()=>{f.value=!1})),g.value.onError((e=>{t("error","at stores/gemini.js:419","音频播放失败:",e),f.value=!1})));const n=`${uni.env.USER_DATA_PATH}/temp_received_audio_${Date.now()}.pcm`,o=uni.arrayBufferToBase64(e);uni.getFileSystemManager().writeFile({filePath:n,data:o,encoding:"base64",success:()=>{g.value.src=n,g.value.play(),f.value=!0,g.value.onEnded((()=>{f.value=!1,uni.getFileSystemManager().unlink({filePath:n,success:()=>t("log","at stores/gemini.js:444","临时接收音频文件已删除"),fail:e=>t("warn","at stores/gemini.js:445","删除临时接收音频文件失败:",e)})}))},fail:e=>{t("error","at stores/gemini.js:450","写入接收音频文件失败:",e),f.value=!1}})}catch(n){t("error","at stores/gemini.js:456","播放接收音频失败:",n),f.value=!1}},O=()=>{g.value&&f.value&&(g.value.stop(),f.value=!1,t("log","at stores/gemini.js:466","停止播放"))},N=()=>{if(r.value>=a.value)return void t("log","at stores/gemini.js:543","达到最大重连次数，停止重连");const e=Math.min(1e3*Math.pow(2,r.value),1e4);setTimeout((()=>{r.value++,E().catch((e=>{t("error","at stores/gemini.js:552","重连失败:",e)}))}),e)};return{session:n,isConnected:o,connectionStatus:i,lastMessage:s,interviewStatus:l,ephemeralToken:c,currentDomain:u,audioInQueue:d,audioOutQueue:p,isRecording:m,isPlaying:f,AUDIO_CONFIG:v,LIVE_CONFIG:y,canSendMessage:_,setEphemeralToken:(e,t)=>{c.value=e,u.value=t},connect:E,disconnect:()=>{if(w(),O(),n.value){try{n.value.close&&n.value.close()}catch(e){t("warn","at stores/gemini.js:240","关闭Live API session失败:",e)}n.value=null}o.value=!1,i.value="disconnected",l.value="idle",c.value="",d.value=[],p.value=[]},startRecording:S,stopRecording:w,sendAudio:async e=>A(e),sendAudioFrame:A,sendText:async e=>{if(!_.value)return!1;try{const o=`你是一个专业的${u.value}面试官。请用自然、友好的语调回答以下问题或进行面试对话：${e}`;return(e=>{try{const t=e.response.text();s.value={text:t},t&&(T(t),l.value="speaking")}catch(n){t("error","at stores/gemini.js:267","处理响应失败:",n)}})(await n.value.generateContent(o)),!0}catch(o){return t("error","at stores/gemini.js:380","发送文本失败:",o),!1}},sendInterrupt:()=>{try{for(O(),uni.stopSpeechSynthesis&&uni.stopSpeechSynthesis();d.value.length>0;)d.value.shift();return l.value="active",t("log","at stores/gemini.js:400","发送打断信号成功"),!0}catch(e){return t("error","at stores/gemini.js:403","发送打断信号失败:",e),!1}},playReceivedAudio:I,stopPlaying:O,startInterview:async()=>{try{return l.value="active",await S(),t("log","at stores/gemini.js:515","Gemini Store: 面试已开始（实时模式）"),!0}catch(e){return t("error","at stores/gemini.js:518","开始面试失败:",e),l.value="idle",!1}},endInterview:()=>{try{return w(),O(),l.value="idle",t("log","at stores/gemini.js:532","Gemini Store: 面试已结束"),!0}catch(e){return t("error","at stores/gemini.js:535","结束面试失败:",e),!1}},playAudio:e=>{try{const n=`${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.wav`;uni.getFileSystemManager().writeFile({filePath:n,data:e,encoding:"base64",success:()=>{const e=uni.createInnerAudioContext();e.src=n,e.play(),e.onEnded((()=>{uni.getFileSystemManager().unlink({filePath:n,success:()=>t("log","at stores/gemini.js:487","临时音频文件已删除"),fail:e=>t("warn","at stores/gemini.js:488","删除临时音频文件失败:",e)}),e.destroy()})),e.onError((n=>{t("error","at stores/gemini.js:494","音频播放失败:",n),e.destroy()}))},fail:e=>{t("error","at stores/gemini.js:499","写入音频文件失败:",e)}})}catch(n){t("error","at stores/gemini.js:503","播放音频异常:",n)}}}})),$l=de("user",(()=>{const n=e.ref(""),o=e.ref({id:null,phone:"",nickname:"",balance_count:0,balance_duration:0,free_trial_count:3,ab_test_group:""}),i=e.computed((()=>!!n.value)),s=e=>{n.value=e,uni.setStorageSync("token",e)},r=e=>{o.value={...o.value,...e},uni.setStorageSync("userInfo",o.value)},a=e.ref(!1),l=e.ref(!1),c=async(e="技术面试")=>{if(!n.value)return void t("log","at stores/user.js:153","用户未登录，无法获取临时令牌");if(l.value)return void t("log","at stores/user.js:159","正在获取临时令牌，跳过重复请求");const o=Gl();if(o.ephemeralToken&&o.currentDomain===e)t("log","at stores/user.js:166","已有有效的临时令牌，无需重新获取");else{l.value=!0;try{t("log","at stores/user.js:173","开始获取临时令牌...");const i=await uni.request({url:`${pe}/api/v1/gemini/ephemeral-token`,method:"POST",header:{Authorization:`Bearer ${n.value}`,"Content-Type":"application/json"},data:{domain:e,expire_minutes:30,uses:1}});if(200!==i.statusCode||200!==i.data.code)throw t("error","at stores/user.js:193","获取临时令牌失败，响应:",i.data),new Error(i.data.message||"获取临时令牌失败");o.setEphemeralToken(i.data.data.token,e),t("log","at stores/user.js:191","成功获取并设置临时令牌")}catch(i){t("error","at stores/user.js:197","获取临时令牌失败:",i),"success"!==i.message&&uni.showToast({title:"获取临时令牌失败",icon:"none"})}finally{l.value=!1}}};return{token:n,userInfo:o,isGettingToken:l,isInitialized:a,isLoggedIn:i,setToken:s,setUserInfo:r,login:async(e,n)=>{try{const t=await uni.request({url:`${pe}/api/v1/auth/login`,method:"POST",data:{phone:e,code:n}});return 200===t.data.code?(s(t.data.data.token),r(t.data.data.user),await c(),{success:!0}):{success:!1,message:t.data.message}}catch(o){return t("error","at stores/user.js:56","登录失败:",o),{success:!1,message:"网络错误"}}},getUserInfo:async()=>{if(n.value)try{const e=await uni.request({url:`${pe}/api/v1/user/info`,method:"GET",header:{Authorization:`Bearer ${n.value}`}});200===e.data.code&&r(e.data.data)}catch(e){t("error","at stores/user.js:77","获取用户信息失败:",e)}},logout:()=>{n.value="",o.value={id:null,phone:"",nickname:"",balance_count:0,balance_duration:0,free_trial_count:3,ab_test_group:""},uni.removeStorageSync("token"),uni.removeStorageSync("userInfo")},sendSmsCode:async e=>{try{const t=await uni.request({url:`${pe}/api/v1/auth/sms`,method:"POST",data:{phone:e,type:2}});return 200===t.data.code?{success:!0}:{success:!1,message:t.data.message}}catch(n){return t("error","at stores/user.js:113","发送验证码失败:",n),{success:!1,message:"网络错误"}}},initFromStorage:async()=>{if(a.value)return void t("log","at stores/user.js:124","用户store已初始化，跳过重复初始化");t("log","at stores/user.js:128","开始初始化用户store...");const e=uni.getStorageSync("token"),i=uni.getStorageSync("userInfo");e&&(n.value=e,t("log","at stores/user.js:134","恢复用户token，准备获取临时令牌"),await c()),i&&(o.value={...o.value,...i},t("log","at stores/user.js:141","恢复用户信息")),a.value=!0,t("log","at stores/user.js:145","用户store初始化完成")},fetchAndSetEphemeralToken:c}})),Jl=(e,t)=>{const n=e.__vccOpts||e;for(const[o,i]of t)n[o]=i;return n};const Hl=Jl({__name:"index",setup(n,{expose:o}){o();const i=$l(),s=e.ref({});e.onMounted((()=>{r()}));const r=async()=>{try{await i.getUserInfo(),s.value=i.userInfo}catch(e){t("error","at pages/index/index.vue:65","获取用户信息失败:",e)}},a=()=>{i.isLoggedIn?uni.navigateTo({url:"/pages/payment/payment"}):uni.navigateTo({url:"/pages/login/login"})},l={userStore:i,userInfo:s,loadUserInfo:r,startInterview:()=>{i.isLoggedIn?s.value.balance_count<=0&&s.value.free_trial_count<=0?uni.showModal({title:"提示",content:"您的使用次数已用完，请购买套餐",success:e=>{e.confirm&&a()}}):uni.navigateTo({url:"/pages/interview/interview"}):uni.navigateTo({url:"/pages/login/login"})},goBuy:a,ref:e.ref,onMounted:e.onMounted,get useUserStore(){return $l}};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}},[["render",function(t,n,o,i,s,r){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"面试助手"),e.createElementVNode("text",{class:"subtitle"},"AI助力，面试无忧")]),e.createElementVNode("view",{class:"stats-card"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(i.userInfo.balance_count||0),1),e.createElementVNode("text",{class:"stat-label"},"剩余次数")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(i.userInfo.free_trial_count||3),1),e.createElementVNode("text",{class:"stat-label"},"免费试用")])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"start-btn",onClick:i.startInterview},[e.createElementVNode("text",{class:"btn-text"},"开始面试")]),e.createElementVNode("button",{class:"buy-btn",onClick:i.goBuy},[e.createElementVNode("text",{class:"btn-text"},"购买套餐")])]),e.createElementVNode("view",{class:"features"},[e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"🎯"),e.createElementVNode("text",{class:"feature-title"},"实时回答"),e.createElementVNode("text",{class:"feature-desc"},"AI实时生成专业回答")]),e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"🔊"),e.createElementVNode("text",{class:"feature-title"},"语音播报"),e.createElementVNode("text",{class:"feature-desc"},"听筒私密播放答案")]),e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"📚"),e.createElementVNode("text",{class:"feature-title"},"领域定制"),e.createElementVNode("text",{class:"feature-desc"},"针对不同技术领域优化")])])])}],["__scopeId","data-v-1cf27b2a"],["__file","/Users/<USER>/code/interviewMaster/app/pages/index/index.vue"]]);const Zl=Jl({__name:"login",setup(t,{expose:n}){n();const o=$l(),i=e.ref({phone:"",code:""}),s=e.ref("login"),r=e.ref(!1),a=e.ref(0),l=e.ref(!1),c=e.computed((()=>a.value>0?`${a.value}s后重发`:"获取验证码")),u=e.computed((()=>11===i.value.phone.length&&6===i.value.code.length&&l.value)),d=()=>{r.value=!0,a.value=60;const e=setInterval((()=>{a.value--,a.value<=0&&(clearInterval(e),r.value=!1)}),1e3)};e.onMounted((()=>{o.isLoggedIn&&uni.switchTab({url:"/pages/index/index"})}));const p={userStore:o,form:i,currentTab:s,codeDisabled:r,codeCountdown:a,agreed:l,codeText:c,canSubmit:u,sendCode:async()=>{if(i.value.phone)if(/^1[3-9]\d{9}$/.test(i.value.phone))try{const e=await o.sendSmsCode(i.value.phone,2);e.success?(uni.showToast({title:"验证码已发送",icon:"success"}),d()):uni.showToast({title:e.message||"发送失败",icon:"none"})}catch(e){uni.showToast({title:"网络错误",icon:"none"})}else uni.showToast({title:"手机号格式不正确",icon:"none"});else uni.showToast({title:"请输入手机号",icon:"none"})},startCountdown:d,handleSubmit:async()=>{if(u.value){uni.showLoading({title:"登录中..."});try{const e=await o.login(i.value.phone,i.value.code);uni.hideLoading(),e.success?(uni.showToast({title:"登录成功",icon:"success"}),setTimeout((()=>{uni.switchTab({url:"/pages/index/index"})}),1500)):uni.showToast({title:e.message||"操作失败",icon:"none"})}catch(e){uni.hideLoading(),uni.showToast({title:"网络错误",icon:"none"})}}},onAgreementChange:e=>{l.value=e.detail.value.includes("agree")},showAgreement:()=>{uni.showModal({title:"用户协议",content:"这里是用户协议内容...",showCancel:!1})},showPrivacy:()=>{uni.showModal({title:"隐私政策",content:"这里是隐私政策内容...",showCancel:!1})},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return $l}};return Object.defineProperty(p,"__isScriptSetup",{enumerable:!1,value:!0}),p}},[["render",function(t,n,o,i,s,r){return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"面试助手"),e.createElementVNode("text",{class:"subtitle"},"AI助力，面试无忧")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"手机号"),e.withDirectives(e.createElementVNode("input",{class:"input",type:"number",placeholder:"请输入手机号","onUpdate:modelValue":n[0]||(n[0]=e=>i.form.phone=e),maxlength:"11"},null,512),[[e.vModelText,i.form.phone]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"验证码"),e.createElementVNode("view",{class:"code-input-wrapper"},[e.withDirectives(e.createElementVNode("input",{class:"input code-input",type:"number",placeholder:"请输入验证码","onUpdate:modelValue":n[1]||(n[1]=e=>i.form.code=e),maxlength:"6"},null,512),[[e.vModelText,i.form.code]]),e.createElementVNode("button",{class:"code-btn",disabled:i.codeDisabled,onClick:i.sendCode},e.toDisplayString(i.codeText),9,["disabled"])])]),e.createElementVNode("button",{class:"submit-btn",disabled:!i.canSubmit,onClick:i.handleSubmit}," 登录 / 注册 ",8,["disabled"]),e.createElementVNode("view",{class:"agreement"},[e.createElementVNode("checkbox-group",{onChange:i.onAgreementChange},[e.createElementVNode("label",{class:"checkbox-label"},[e.createElementVNode("checkbox",{value:"agree",checked:i.agreed},null,8,["checked"]),e.createElementVNode("text",{class:"agreement-text"},[e.createTextVNode(" 我已阅读并同意 "),e.createElementVNode("text",{class:"link",onClick:i.showAgreement},"《用户协议》"),e.createTextVNode(" 和 "),e.createElementVNode("text",{class:"link",onClick:i.showPrivacy},"《隐私政策》")])])],32)])])])}],["__scopeId","data-v-e4e4508d"],["__file","/Users/<USER>/code/interviewMaster/app/pages/login/login.vue"]]);const Yl=Jl({__name:"interview",setup(n,{expose:o}){o();const i=$l(),s=Gl(),r=e.ref(""),a=e.ref("idle"),l=e.ref(!1),c=e.ref(!1),u=e.ref(1),d=e.ref([]),p=e.ref(!1),m=e.ref(!1),f=uni.getRecorderManager(),h=uni.createInnerAudioContext(),g=e.ref([]),v=e.computed((()=>({idle:"空闲",connecting:"连接中...",active:"面试进行中",speaking:"正在播报...",ended:"面试已结束",error:"连接错误"}[a.value]||"未知状态"))),y=e.computed((()=>`status-${a.value}`)),_=e.computed((()=>{switch(a.value){case"connecting":return"⏳";case"active":return"🎙️";case"speaking":return"🗣";case"ended":return"✅";default:return"🚀"}})),E=e.computed((()=>{switch(a.value){case"connecting":return"连接中...";case"active":return"面试进行中";case"speaking":return"正在回答...";case"ended":return"面试已结束";default:return"立即开始"}})),C=e.computed((()=>"idle"===a.value||!s.isConnected)),b=()=>{if(!s.isConnected)return a.value="connecting",void s.connect().then((()=>{C.value&&T()})).catch((e=>{t("error","at pages/interview/interview.vue:233","连接失败，无法开始面试",e),a.value="idle",uni.showToast({title:"连接失败，请重试",icon:"error"})}));C.value&&T()},T=()=>{m.value=!0,s.startInterview(),a.value="active",f.start({duration:6e5,sampleRate:16e3,numberOfChannels:1,encodeBitRate:48e3,format:"wav",frameSize:1024})},S=()=>{m.value=!1,s.endInterview(),f.stop(),a.value="idle"},w=()=>{c.value=!1,uni.setStorageSync("guide_shown",!0)};e.onMounted((async()=>{if(!i.isLoggedIn)return void uni.redirectTo({url:"/pages/login/login"});uni.getStorageSync("guide_shown")||(c.value=!0),s.$subscribe(((e,t)=>{"gemini"===e.storeId&&(a.value=t.interviewStatus)})),f.onStart((()=>{t("log","at pages/interview/interview.vue:363","面试录音开始（实时模式），等待音频帧...")})),f.onFrameRecorded&&f.onFrameRecorded((e=>{if(m.value&&!p.value&&s.isConnected){const o=e.frameBuffer;if(o&&o.byteLength>0){t("log","at pages/interview/interview.vue:372","收到音频帧，大小:",o.byteLength);try{s.sendAudio(o)}catch(n){t("error","at pages/interview/interview.vue:377","实时音频数据发送失败:",n)}}}})),f.onStop((e=>{t("log","at pages/interview/interview.vue:384","面试录音结束（实时模式），音频流已在实时处理中")})),f.onError((e=>{t("error","at pages/interview/interview.vue:390","面试录音错误",e),m.value=!1,a.value="idle",e.errMsg.includes("auth")||e.errMsg.includes("permission")?uni.showModal({title:"权限申请",content:"需要录音权限才能使用面试功能，请在设置中开启。",success:e=>{e.confirm&&uni.openSetting()}}):uni.showToast({title:"录音失败，请稍后重试",icon:"none"})})),h.onPlay((()=>{t("log","at pages/interview/interview.vue:415","音频播放开始")})),h.onEnded((()=>{t("log","at pages/interview/interview.vue:419","音频播放结束"),a.value=m.value?"active":"idle"})),h.onError((e=>{t("error","at pages/interview/interview.vue:424","音频播放错误",e),a.value=m.value?"active":"idle"}))})),e.onUnmounted((()=>{m.value&&S(),f.stop(),h.destroy(),s.disconnect()}));const A={userStore:i,geminiStore:s,sessionId:r,interviewStatus:a,showHistory:l,showGuide:c,guideStep:u,recentHistory:d,isMuted:p,isInterviewActive:m,recorderManager:f,innerAudioContext:h,audioChunks:g,statusText:v,statusClass:y,buttonIcon:_,buttonText:E,canStartInterview:C,toggleInterview:()=>{m.value?S():b()},startInterview:b,startRecording:T,endInterview:S,toggleMute:()=>{p.value=!p.value,t("log","at pages/interview/interview.vue:277",p.value?"已静音":"取消静音")},interruptAnswer:()=>{s.sendInterrupt(),h.stop()},endInterviewConfirm:()=>{uni.showModal({title:"确认",content:"确定要结束本次面试吗？",success:e=>{e.confirm&&(m.value&&S(),s.disconnect(),uni.navigateBack())}})},toggleHistory:()=>{l.value=!l.value},giveFeedback:(e,t)=>{const n=d.value.find((t=>t.id===e));n&&(n.feedback=n.feedback===t?0:t)},formatTime:e=>{const t=new Date(e);return`${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`},nextGuide:()=>{u.value<3?u.value++:w()},closeGuide:w,ref:e.ref,computed:e.computed,onMounted:e.onMounted,onUnmounted:e.onUnmounted,get useUserStore(){return $l},get useGeminiStore(){return Gl}};return Object.defineProperty(A,"__isScriptSetup",{enumerable:!1,value:!0}),A}},[["render",function(t,n,o,i,s,r){return e.openBlock(),e.createElementBlock("view",{class:"interview-container"},[e.createCommentVNode(" 状态栏 "),e.createElementVNode("view",{class:"status-bar"},[e.createElementVNode("view",{class:"status-info"},[e.createElementVNode("text",{class:"status-text"},e.toDisplayString(i.statusText),1),e.createElementVNode("view",{class:e.normalizeClass(["status-indicator",i.statusClass])},null,2)]),e.createElementVNode("view",{class:"session-info"},[e.createElementVNode("text",{class:"session-text"},"会话: "+e.toDisplayString(i.sessionId||"未连接"),1)])]),e.createCommentVNode(" 主控制区域 "),e.createElementVNode("view",{class:"control-area"},[e.createCommentVNode(" 中心按钮 "),e.createElementVNode("view",{class:"center-button-container"},[e.createElementVNode("button",{class:e.normalizeClass(["center-button",{active:i.isInterviewActive,disabled:!i.canStartInterview,connecting:"connecting"===i.interviewStatus}]),onClick:i.toggleInterview},[e.createElementVNode("view",{class:"button-icon"},[e.createElementVNode("text",{class:"icon"},e.toDisplayString(i.buttonIcon),1)]),e.createElementVNode("text",{class:"button-text"},e.toDisplayString(i.buttonText),1)],2),e.createCommentVNode(" 实时交互动画 "),i.isInterviewActive?(e.openBlock(),e.createElementBlock("view",{key:0,class:"realtime-animation"},[e.createElementVNode("view",{class:"wave wave1"}),e.createElementVNode("view",{class:"wave wave2"}),e.createElementVNode("view",{class:"wave wave3"})])):e.createCommentVNode("v-if",!0)]),e.createCommentVNode(" 控制按钮组 "),e.createElementVNode("view",{class:"control-buttons"},[i.isInterviewActive&&"speaking"===i.interviewStatus?(e.openBlock(),e.createElementBlock("button",{key:0,class:"control-btn interrupt-btn",onClick:i.interruptAnswer},[e.createElementVNode("text",{class:"btn-icon"},"⏸"),e.createElementVNode("text",{class:"btn-text"},"打断")])):e.createCommentVNode("v-if",!0),i.isInterviewActive?(e.openBlock(),e.createElementBlock("button",{key:1,class:e.normalizeClass(["control-btn mute-btn",{active:i.isMuted}]),onClick:i.toggleMute},[e.createElementVNode("text",{class:"btn-icon"},e.toDisplayString(i.isMuted?"🔇":"🔊"),1),e.createElementVNode("text",{class:"btn-text"},e.toDisplayString(i.isMuted?"取消静音":"静音"),1)],2)):e.createCommentVNode("v-if",!0),e.createElementVNode("button",{class:"control-btn end-btn",onClick:i.endInterviewConfirm},[e.createElementVNode("text",{class:"btn-icon"},"⏹"),e.createElementVNode("text",{class:"btn-text"},"结束")])])]),e.createCommentVNode(" 历史记录 "),e.createElementVNode("view",{class:"history-section"},[e.createElementVNode("view",{class:"history-header",onClick:i.toggleHistory},[e.createElementVNode("text",{class:"history-title"},"历史记录"),e.createElementVNode("text",{class:"history-toggle"},e.toDisplayString(i.showHistory?"收起":"展开"),1)]),i.showHistory?(e.openBlock(),e.createElementBlock("view",{key:0,class:"history-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.recentHistory,((t,n)=>(e.openBlock(),e.createElementBlock("view",{key:n,class:"history-item"},[e.createElementVNode("view",{class:"question"},[e.createElementVNode("text",{class:"label"},"问题:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.question),1)]),e.createElementVNode("view",{class:"answer"},[e.createElementVNode("text",{class:"label"},"回答:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.answer),1)]),e.createElementVNode("view",{class:"meta"},[e.createElementVNode("text",{class:"time"},e.toDisplayString(i.formatTime(t.time)),1),e.createElementVNode("view",{class:"feedback"},[e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:1===t.feedback}]),onClick:e=>i.giveFeedback(t.id,1)}," 👍 ",10,["onClick"]),e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:-1===t.feedback}]),onClick:e=>i.giveFeedback(t.id,-1)}," 👎 ",10,["onClick"])])])])))),128))])):e.createCommentVNode("v-if",!0)]),e.createCommentVNode(" 新手引导 "),i.showGuide?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guide-overlay",onClick:i.closeGuide},[e.createElementVNode("view",{class:"guide-content",onClick:n[0]||(n[0]=e.withModifiers((()=>{}),["stop"]))},[1===i.guideStep?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"欢迎使用面试助手"),e.createElementVNode("text",{class:"guide-text"},"按住中心按钮说话，AI会实时为您提供专业的回答建议"),e.createElementVNode("button",{class:"guide-btn",onClick:i.nextGuide},"下一步")])):e.createCommentVNode("v-if",!0),2===i.guideStep?(e.openBlock(),e.createElementBlock("view",{key:1,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"听筒播放"),e.createElementVNode("text",{class:"guide-text"},"答案会通过听筒播放，请将手机靠近耳朵以获得最佳体验"),e.createElementVNode("button",{class:"guide-btn",onClick:i.nextGuide},"下一步")])):e.createCommentVNode("v-if",!0),3===i.guideStep?(e.openBlock(),e.createElementBlock("view",{key:2,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"打断功能"),e.createElementVNode("text",{class:"guide-text"},'在AI回答时，您可以点击"打断"按钮来停止当前回答'),e.createElementVNode("button",{class:"guide-btn",onClick:i.closeGuide},"开始使用")])):e.createCommentVNode("v-if",!0)])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-99492b0e"],["__file","/Users/<USER>/code/interviewMaster/app/pages/interview/interview.vue"]]);const Kl=Jl({__name:"history",setup(n,{expose:o}){o();const i=$l(),s=e.ref([]),r=e.ref({}),a=e.ref(!1),l=e.ref(!0),c=e.ref(1),u=e.ref(20),d=e.ref(""),p=e.ref(!1),m=e.ref(null),f=async(e=!1)=>{if(!a.value){a.value=!0;try{const t={page:e?1:c.value,page_size:u.value};d.value&&(t.date=d.value);const n={list:[{id:1,session_id:"session_1234567890",question_text:"请介绍一下JavaScript的闭包概念",answer_text:"闭包是JavaScript中的一个重要概念，它指的是函数能够访问其外部作用域中的变量，即使在函数外部调用时也是如此...",response_time_ms:1200,user_feedback:1,prompt_version:"A",created_at:(new Date).toISOString()}],total:1,page:1,page_size:20};e?(s.value=n.list,c.value=1):s.value.push(...n.list),l.value=s.value.length<n.total,c.value++}catch(n){t("error","at pages/history/history.vue:218","加载历史记录失败:",n),uni.showToast({title:"加载失败",icon:"none"})}finally{a.value=!1}}},h=async()=>{try{r.value={total_count:25,month_count:8,today_count:2,avg_response_time:1350}}catch(e){t("error","at pages/history/history.vue:242","加载统计信息失败:",e)}};e.onMounted((()=>{i.isLoggedIn?(f(!0),h()):uni.redirectTo({url:"/pages/login/login"})}));const g={userStore:i,historyList:s,stats:r,loading:a,hasMore:l,page:c,pageSize:u,filterDate:d,showDetailModal:p,selectedItem:m,loadHistory:f,loadStats:h,loadMore:()=>{l.value&&!a.value&&f(!1)},onDateChange:e=>{d.value=e.detail.value,f(!0)},clearFilter:()=>{d.value="",f(!0)},giveFeedback:async(e,n)=>{try{const t=s.value.find((t=>t.id===e));t&&(t.user_feedback=t.user_feedback===n?0:n),uni.showToast({title:"反馈成功",icon:"success"})}catch(o){t("error","at pages/history/history.vue:280","反馈失败:",o),uni.showToast({title:"反馈失败",icon:"none"})}},showDetail:e=>{m.value=e,p.value=!0},closeDetail:()=>{p.value=!1,m.value=null},startInterview:()=>{uni.navigateTo({url:"/pages/interview/interview"})},formatDateTime:e=>{if(!e)return"";const t=new Date(e);return`${t.getMonth()+1}/${t.getDate()} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`},formatResponseTime:e=>e?e<1e3?`${e}ms`:`${(e/1e3).toFixed(1)}s`:"0ms",truncateText:(e,t)=>e?e.length<=t?e:e.substring(0,t)+"...":"",ref:e.ref,onMounted:e.onMounted,get useUserStore(){return $l}};return Object.defineProperty(g,"__isScriptSetup",{enumerable:!1,value:!0}),g}},[["render",function(t,n,o,i,s,r){var a,l,c,u,d,p;return e.openBlock(),e.createElementBlock("view",{class:"history-container"},[e.createCommentVNode(" 筛选栏 "),e.createElementVNode("view",{class:"filter-bar"},[e.createElementVNode("picker",{mode:"date",value:i.filterDate,onChange:i.onDateChange,class:"date-picker"},[e.createElementVNode("view",{class:"picker-text"},e.toDisplayString(i.filterDate||"选择日期"),1)],40,["value"]),e.createElementVNode("button",{class:"clear-filter",onClick:i.clearFilter}," 清除筛选 ")]),e.createCommentVNode(" 统计信息 "),e.createElementVNode("view",{class:"stats-card"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(i.stats.total_count||0),1),e.createElementVNode("text",{class:"stat-label"},"总次数")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(i.stats.month_count||0),1),e.createElementVNode("text",{class:"stat-label"},"本月")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(i.stats.today_count||0),1),e.createElementVNode("text",{class:"stat-label"},"今日")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(i.formatResponseTime(i.stats.avg_response_time)),1),e.createElementVNode("text",{class:"stat-label"},"平均响应")])]),e.createCommentVNode(" 历史记录列表 "),e.createElementVNode("view",{class:"history-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.historyList,((t,n)=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"history-item",onClick:e=>i.showDetail(t)},[e.createElementVNode("view",{class:"item-header"},[e.createElementVNode("text",{class:"session-id"},"会话 "+e.toDisplayString(t.session_id.slice(-8)),1),e.createElementVNode("text",{class:"time"},e.toDisplayString(i.formatDateTime(t.created_at)),1)]),e.createElementVNode("view",{class:"question-section"},[e.createElementVNode("text",{class:"label"},"问题:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.question_text||"语音问题"),1)]),e.createElementVNode("view",{class:"answer-section"},[e.createElementVNode("text",{class:"label"},"回答:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(i.truncateText(t.answer_text,100)),1)]),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"meta-info"},[e.createElementVNode("text",{class:"response-time"},"响应: "+e.toDisplayString(t.response_time_ms)+"ms",1),e.createElementVNode("text",{class:"prompt-version"},"版本: "+e.toDisplayString(t.prompt_version),1)]),e.createElementVNode("view",{class:"feedback-buttons"},[e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:1===t.user_feedback}]),onClick:e.withModifiers((e=>i.giveFeedback(t.id,1)),["stop"])}," 👍 ",10,["onClick"]),e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:-1===t.user_feedback}]),onClick:e.withModifiers((e=>i.giveFeedback(t.id,-1)),["stop"])}," 👎 ",10,["onClick"])])])],8,["onClick"])))),128)),e.createCommentVNode(" 加载更多 "),i.hasMore?(e.openBlock(),e.createElementBlock("view",{key:0,class:"load-more",onClick:i.loadMore},[e.createElementVNode("text",{class:"load-text"},e.toDisplayString(i.loading?"加载中...":"加载更多"),1)])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 空状态 "),i.loading||0!==i.historyList.length?e.createCommentVNode("v-if",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"empty-state"},[e.createElementVNode("text",{class:"empty-icon"},"📝"),e.createElementVNode("text",{class:"empty-text"},"暂无面试记录"),e.createElementVNode("button",{class:"start-btn",onClick:i.startInterview}," 开始面试 ")]))]),e.createCommentVNode(" 详情弹窗 "),i.showDetailModal?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-modal",onClick:i.closeDetail},[e.createElementVNode("view",{class:"detail-content",onClick:n[0]||(n[0]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"detail-header"},[e.createElementVNode("text",{class:"detail-title"},"面试记录详情"),e.createElementVNode("button",{class:"close-btn",onClick:i.closeDetail},"×")]),e.createElementVNode("view",{class:"detail-body"},[e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"会话ID:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(a=i.selectedItem)?void 0:a.session_id),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"时间:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(i.formatDateTime(null==(l=i.selectedItem)?void 0:l.created_at)),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"问题:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString((null==(c=i.selectedItem)?void 0:c.question_text)||"语音问题"),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"回答:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(u=i.selectedItem)?void 0:u.answer_text),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"响应时间:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(d=i.selectedItem)?void 0:d.response_time_ms)+"ms",1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"提示词版本:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(p=i.selectedItem)?void 0:p.prompt_version),1)])])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-b2d018fa"],["__file","/Users/<USER>/code/interviewMaster/app/pages/history/history.vue"]]);const Wl=Jl({__name:"profile",setup(n,{expose:o}){o();const i=$l(),s=e.computed((()=>i.userInfo)),r=e.ref(!1),a=e.ref({nickname:"",avatar:""}),l=()=>{r.value=!1};e.onMounted((async()=>{if(i.isLoggedIn)try{await i.getUserInfo()}catch(e){t("error","at pages/profile/profile.vue:278","获取用户信息失败:",e)}else uni.redirectTo({url:"/pages/login/login"})}));const c={userStore:i,userInfo:s,showEditModal:r,editForm:a,editProfile:()=>{a.value={nickname:s.value.nickname||"",avatar:s.value.avatar||""},r.value=!0},closeEdit:l,chooseAvatar:()=>{uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:e=>{a.value.avatar=e.tempFilePaths[0]}})},saveProfile:async()=>{try{uni.showLoading({title:"保存中..."}),i.setUserInfo(a.value),uni.hideLoading(),uni.showToast({title:"保存成功",icon:"success"}),l()}catch(e){uni.hideLoading(),uni.showToast({title:"保存失败",icon:"none"})}},goRecharge:()=>{uni.navigateTo({url:"/pages/payment/payment"})},goHistory:()=>{uni.switchTab({url:"/pages/history/history"})},goOrders:()=>{uni.navigateTo({url:"/pages/orders/orders"})},goSettings:()=>{uni.navigateTo({url:"/pages/settings/settings"})},goHelp:()=>{uni.navigateTo({url:"/pages/help/help"})},goAbout:()=>{uni.navigateTo({url:"/pages/about/about"})},logout:()=>{uni.showModal({title:"确认",content:"确定要退出登录吗？",success:e=>{e.confirm&&(i.logout(),uni.reLaunch({url:"/pages/login/login"}))}})},formatDuration:e=>{if(!e)return"0分钟";const t=Math.floor(e/3600),n=Math.floor(e%3600/60);return t>0?`${t}小时${n}分钟`:`${n}分钟`},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return $l}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}},[["render",function(t,n,o,i,s,r){return e.openBlock(),e.createElementBlock("view",{class:"profile-container"},[e.createCommentVNode(" 用户信息卡片 "),e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"avatar-section"},[e.createElementVNode("image",{class:"avatar",src:i.userInfo.avatar||"/static/default-avatar.png"},null,8,["src"]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"nickname"},e.toDisplayString(i.userInfo.nickname||"面试者"),1),e.createElementVNode("text",{class:"phone"},e.toDisplayString(i.userInfo.phone),1)])]),e.createElementVNode("button",{class:"edit-btn",onClick:i.editProfile}," 编辑 ")]),e.createCommentVNode(" 余额信息 "),e.createElementVNode("view",{class:"balance-card"},[e.createElementVNode("view",{class:"balance-header"},[e.createElementVNode("text",{class:"balance-title"},"我的余额"),e.createElementVNode("button",{class:"recharge-btn",onClick:i.goRecharge}," 充值 ")]),e.createElementVNode("view",{class:"balance-info"},[e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(i.userInfo.balance_count||0),1),e.createElementVNode("text",{class:"balance-label"},"剩余次数")]),e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(i.userInfo.free_trial_count||0),1),e.createElementVNode("text",{class:"balance-label"},"免费试用")]),e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(i.formatDuration(i.userInfo.balance_duration)),1),e.createElementVNode("text",{class:"balance-label"},"剩余时长")])])]),e.createCommentVNode(" 功能菜单 "),e.createElementVNode("view",{class:"menu-section"},[e.createElementVNode("view",{class:"menu-item",onClick:i.goHistory},[e.createElementVNode("view",{class:"menu-icon"},"📊"),e.createElementVNode("text",{class:"menu-text"},"面试历史"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:i.goOrders},[e.createElementVNode("view",{class:"menu-icon"},"📋"),e.createElementVNode("text",{class:"menu-text"},"我的订单"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:i.goSettings},[e.createElementVNode("view",{class:"menu-icon"},"⚙️"),e.createElementVNode("text",{class:"menu-text"},"设置"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:i.goHelp},[e.createElementVNode("view",{class:"menu-icon"},"❓"),e.createElementVNode("text",{class:"menu-text"},"帮助与反馈"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:i.goAbout},[e.createElementVNode("view",{class:"menu-icon"},"ℹ️"),e.createElementVNode("text",{class:"menu-text"},"关于我们"),e.createElementVNode("text",{class:"menu-arrow"},">")])]),e.createCommentVNode(" 退出登录 "),e.createElementVNode("view",{class:"logout-section"},[e.createElementVNode("button",{class:"logout-btn",onClick:i.logout}," 退出登录 ")]),e.createCommentVNode(" 编辑资料弹窗 "),i.showEditModal?(e.openBlock(),e.createElementBlock("view",{key:0,class:"edit-modal",onClick:i.closeEdit},[e.createElementVNode("view",{class:"edit-content",onClick:n[1]||(n[1]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"edit-header"},[e.createElementVNode("text",{class:"edit-title"},"编辑资料"),e.createElementVNode("button",{class:"close-btn",onClick:i.closeEdit},"×")]),e.createElementVNode("view",{class:"edit-body"},[e.createElementVNode("view",{class:"edit-item"},[e.createElementVNode("text",{class:"edit-label"},"昵称"),e.withDirectives(e.createElementVNode("input",{class:"edit-input","onUpdate:modelValue":n[0]||(n[0]=e=>i.editForm.nickname=e),placeholder:"请输入昵称",maxlength:"20"},null,512),[[e.vModelText,i.editForm.nickname]])]),e.createElementVNode("view",{class:"edit-item"},[e.createElementVNode("text",{class:"edit-label"},"头像"),e.createElementVNode("view",{class:"avatar-upload",onClick:i.chooseAvatar},[e.createElementVNode("image",{class:"upload-avatar",src:i.editForm.avatar||"/static/default-avatar.png"},null,8,["src"]),e.createElementVNode("text",{class:"upload-text"},"点击更换")])])]),e.createElementVNode("view",{class:"edit-footer"},[e.createElementVNode("button",{class:"cancel-btn",onClick:i.closeEdit},"取消"),e.createElementVNode("button",{class:"save-btn",onClick:i.saveProfile},"保存")])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-dd383ca2"],["__file","/Users/<USER>/code/interviewMaster/app/pages/profile/profile.vue"]]);const zl=Jl({__name:"payment",setup(n,{expose:o}){o();const i=$l(),s=e.ref([]),r=e.ref([]),a=e.ref(null),l=e.ref(""),c=e.ref(!1),u=e.ref(!1),d=e.ref(null),p=e.computed((()=>{const e=r.value.find((e=>e.type===l.value));return(null==e?void 0:e.name)||""})),m=async()=>{try{s.value=[{id:"trial_10",name:"新手体验包",description:"10次面试机会，适合初次体验",price:9.9,count:10,duration:0,type:1},{id:"standard_50",name:"标准套餐",description:"50次面试机会，适合求职准备",price:39.9,count:50,duration:0,type:1},{id:"premium_100",name:"高级套餐",description:"100次面试机会，适合长期使用",price:69.9,count:100,duration:0,type:1},{id:"monthly_unlimited",name:"包月畅享",description:"一个月内无限次使用",price:99.9,count:0,duration:2592e3,type:3}]}catch(e){t("error","at pages/payment/payment.vue:219","加载商品列表失败:",e),uni.showToast({title:"加载失败",icon:"none"})}},f=async()=>{try{r.value=[{type:"wechat",name:"微信支付",icon:"wechat",description:"使用微信扫码支付",enabled:!0},{type:"alipay",name:"支付宝",icon:"alipay",description:"使用支付宝扫码支付",enabled:!0}]}catch(e){t("error","at pages/payment/payment.vue:251","加载支付方式失败:",e)}},h=async()=>{try{return{success:!0,data:{order_no:"IM"+Date.now(),product_id:a.value.id,amount:a.value.price,status:0}}}catch(e){return{success:!1,message:"创建订单失败"}}},g=async e=>{try{return{success:!0,data:{code_url:"weixin://wxpay/bizpayurl?pr=mock_code"}}}catch(t){return{success:!1,message:"创建微信支付失败"}}},v=async e=>{try{return{success:!0,data:{qr_code:"https://qr.alipay.com/mock_code"}}}catch(t){return{success:!1,message:"创建支付宝支付失败"}}},y=()=>{const e=setInterval((async()=>{const t=await _();"paid"===t?(clearInterval(e),E()):"failed"===t&&(clearInterval(e),C())}),3e3);setTimeout((()=>{clearInterval(e)}),3e5)},_=async()=>{if(!d.value)return"pending";try{return Math.random()>.8?"paid":"pending"}catch(e){return t("error","at pages/payment/payment.vue:421","检查支付状态失败:",e),"pending"}},E=()=>{b(),uni.showToast({title:"支付成功",icon:"success"}),i.getUserInfo(),setTimeout((()=>{uni.navigateBack()}),2e3)},C=()=>{b(),uni.showToast({title:"支付失败",icon:"none"})},b=()=>{u.value=!1};e.onMounted((()=>{i.isLoggedIn?(m(),f()):uni.redirectTo({url:"/pages/login/login"})}));const T={userStore:i,products:s,paymentMethods:r,selectedProduct:a,selectedPayment:l,paying:c,showQRCode:u,currentOrder:d,paymentMethodName:p,loadProducts:m,loadPaymentMethods:f,selectProduct:e=>{a.value=e,r.value.length>0&&(l.value=r.value[0].type)},selectPayment:e=>{const t=r.value.find((t=>t.type===e));t&&t.enabled&&(l.value=e)},createPayment:async()=>{if(a.value&&l.value){c.value=!0;try{const e=await h();if(!e.success)throw new Error(e.message);let t;if(d.value=e.data,"wechat"===l.value?t=await g(d.value.order_no):"alipay"===l.value&&(t=await v(d.value.order_no)),!t.success)throw new Error(t.message);u.value=!0,y()}catch(e){t("error","at pages/payment/payment.vue:306","创建支付失败:",e),uni.showToast({title:e.message||"支付失败",icon:"none"})}finally{c.value=!1}}},createOrder:h,createWeChatPayment:g,createAlipayPayment:v,startPaymentPolling:y,checkPaymentStatus:_,handlePaymentSuccess:E,handlePaymentFailed:C,closeQRCode:b,formatDuration:e=>{if(!e)return"0分钟";const t=Math.floor(e/86400),n=Math.floor(e%86400/3600),o=Math.floor(e%3600/60);return t>0?`${t}天`:n>0?`${n}小时`:`${o}分钟`},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return $l}};return Object.defineProperty(T,"__isScriptSetup",{enumerable:!1,value:!0}),T}},[["render",function(t,n,o,i,s,r){var a;return e.openBlock(),e.createElementBlock("view",{class:"payment-container"},[e.createCommentVNode(" 商品列表 "),e.createElementVNode("view",{class:"products-section"},[e.createElementVNode("text",{class:"section-title"},"选择套餐"),e.createElementVNode("view",{class:"products-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.products,(t=>{var n,o;return e.openBlock(),e.createElementBlock("view",{key:t.id,class:e.normalizeClass(["product-item",{selected:(null==(n=i.selectedProduct)?void 0:n.id)===t.id}]),onClick:e=>i.selectProduct(t)},[e.createElementVNode("view",{class:"product-header"},[e.createElementVNode("text",{class:"product-name"},e.toDisplayString(t.name),1),"trial_10"===t.id?(e.openBlock(),e.createElementBlock("view",{key:0,class:"product-badge"}," 推荐 ")):e.createCommentVNode("v-if",!0)]),e.createElementVNode("text",{class:"product-desc"},e.toDisplayString(t.description),1),e.createElementVNode("view",{class:"product-info"},[e.createElementVNode("view",{class:"product-details"},[t.count>0?(e.openBlock(),e.createElementBlock("text",{key:0,class:"detail-item"},e.toDisplayString(t.count)+"次面试机会 ",1)):e.createCommentVNode("v-if",!0),t.duration>0?(e.openBlock(),e.createElementBlock("text",{key:1,class:"detail-item"},e.toDisplayString(i.formatDuration(t.duration))+"使用时长 ",1)):e.createCommentVNode("v-if",!0)]),e.createElementVNode("view",{class:"product-price"},[e.createElementVNode("text",{class:"price-symbol"},"¥"),e.createElementVNode("text",{class:"price-amount"},e.toDisplayString(t.price),1)])]),(null==(o=i.selectedProduct)?void 0:o.id)===t.id?(e.openBlock(),e.createElementBlock("view",{key:0,class:"select-indicator"}," ✓ ")):e.createCommentVNode("v-if",!0)],10,["onClick"])})),128))])]),e.createCommentVNode(" 支付方式 "),i.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:0,class:"payment-methods-section"},[e.createElementVNode("text",{class:"section-title"},"支付方式"),e.createElementVNode("view",{class:"payment-methods"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(i.paymentMethods,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.type,class:e.normalizeClass(["payment-method",{selected:i.selectedPayment===t.type,disabled:!t.enabled}]),onClick:e=>i.selectPayment(t.type)},[e.createElementVNode("view",{class:"method-icon"},["wechat"===t.type?(e.openBlock(),e.createElementBlock("text",{key:0},"💚")):e.createCommentVNode("v-if",!0),"alipay"===t.type?(e.openBlock(),e.createElementBlock("text",{key:1},"💙")):e.createCommentVNode("v-if",!0)]),e.createElementVNode("view",{class:"method-info"},[e.createElementVNode("text",{class:"method-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"method-desc"},e.toDisplayString(t.description),1)]),e.createElementVNode("view",{class:"method-radio"},[e.createElementVNode("view",{class:e.normalizeClass(["radio-circle",{checked:i.selectedPayment===t.type}])},null,2)])],10,["onClick"])))),128))])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 订单信息 "),i.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:1,class:"order-info-section"},[e.createElementVNode("text",{class:"section-title"},"订单信息"),e.createElementVNode("view",{class:"order-details"},[e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"商品名称:"),e.createElementVNode("text",{class:"order-value"},e.toDisplayString(i.selectedProduct.name),1)]),e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"商品价格:"),e.createElementVNode("text",{class:"order-value"},"¥"+e.toDisplayString(i.selectedProduct.price),1)]),e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"优惠金额:"),e.createElementVNode("text",{class:"order-value"},"¥0.00")]),e.createElementVNode("view",{class:"order-item total"},[e.createElementVNode("text",{class:"order-label"},"实付金额:"),e.createElementVNode("text",{class:"order-value price"},"¥"+e.toDisplayString(i.selectedProduct.price),1)])])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 支付按钮 "),i.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:2,class:"pay-button-section"},[e.createElementVNode("button",{class:"pay-button",disabled:!i.selectedPayment||i.paying,onClick:i.createPayment},e.toDisplayString(i.paying?"处理中...":`立即支付 ¥${i.selectedProduct.price}`),9,["disabled"])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 支付二维码弹窗 "),i.showQRCode?(e.openBlock(),e.createElementBlock("view",{key:3,class:"qr-modal",onClick:i.closeQRCode},[e.createElementVNode("view",{class:"qr-content",onClick:n[0]||(n[0]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"qr-header"},[e.createElementVNode("text",{class:"qr-title"},"扫码支付"),e.createElementVNode("button",{class:"close-btn",onClick:i.closeQRCode},"×")]),e.createElementVNode("view",{class:"qr-body"},[e.createElementVNode("view",{class:"qr-code"},[e.createCommentVNode(" 这里应该显示二维码图片 "),e.createElementVNode("text",{class:"qr-placeholder"},"二维码")]),e.createElementVNode("text",{class:"qr-tip"},"请使用"+e.toDisplayString(i.paymentMethodName)+"扫描二维码完成支付",1),e.createElementVNode("view",{class:"qr-amount"},[e.createElementVNode("text",{class:"amount-label"},"支付金额:"),e.createElementVNode("text",{class:"amount-value"},"¥"+e.toDisplayString(null==(a=i.selectedProduct)?void 0:a.price),1)])]),e.createElementVNode("view",{class:"qr-footer"},[e.createElementVNode("button",{class:"check-btn",onClick:i.checkPaymentStatus}," 检查支付状态 ")])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-eade9ab2"],["__file","/Users/<USER>/code/interviewMaster/app/pages/payment/payment.vue"]]);__definePage("pages/index/index",Hl),__definePage("pages/login/login",Zl),__definePage("pages/interview/interview",Yl),__definePage("pages/history/history",Kl),__definePage("pages/profile/profile",Wl),__definePage("pages/payment/payment",zl);const Xl=Jl({__name:"App",setup(e,{expose:n}){n(),s((()=>{t("log","at App.vue:13","App Launch"),r()})),o((()=>{t("log","at App.vue:19","App Show")})),i((()=>{t("log","at App.vue:24","App Hide")}));const r=async()=>{try{const e=$l();await e.initFromStorage(),t("log","at App.vue:33","应用初始化完成")}catch(e){t("error","at App.vue:35","应用初始化失败:",e)}},a={initApp:r,get onLaunch(){return s},get onShow(){return o},get onHide(){return i},get useUserStore(){return $l}};return Object.defineProperty(a,"__isScriptSetup",{enumerable:!1,value:!0}),a}},[["render",function(t,n,o,i,s,r){return e.openBlock(),e.createElementBlock("view",{id:"app"},[e.createCommentVNode(" 应用根组件 ")])}],["__file","/Users/<USER>/code/interviewMaster/app/App.vue"]]);const{app:Ql,Vuex:ec,Pinia:tc}=function(){const t=e.createVueApp(Xl),n=function(){const t=e.effectScope(!0),n=t.run((()=>e.ref({})));let o=[],i=[];const s=e.markRaw({install(e){g(s),s._a=e,e.provide(v,s),e.config.globalProperties.$pinia=s,b&&K(e,s),i.forEach((e=>o.push(e))),i=[]},use(e){return this._a?o.push(e):i.push(e),this},_p:o,_a:null,_e:t,_s:new Map,state:n});return b&&"undefined"!=typeof Proxy&&s.use(Q),s}();return t.use(n),{app:t,pinia:n}}();uni.Vuex=ec,uni.Pinia=tc,Ql.provide("__globalStyles",__uniConfig.styles),Ql._component.mpType="app",Ql._component.render=()=>{},Ql.mount("#app")}(Vue);
