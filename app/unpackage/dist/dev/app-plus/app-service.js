if("undefined"==typeof Promise||Promise.prototype.finally||(Promise.prototype.finally=function(e){const t=this.constructor;return this.then((n=>t.resolve(e()).then((()=>n))),(n=>t.resolve(e()).then((()=>{throw n}))))}),"undefined"!=typeof uni&&uni&&uni.requireGlobal){const e=uni.requireGlobal();ArrayBuffer=e.<PERSON>,Int8Array=e.Int8Array,Uint8Array=e.Uint8Array,Uint8ClampedArray=e.Uint8ClampedArray,Int16Array=e.Int16Array,Uint16Array=e.Uint16Array,Int32Array=e.Int32Array,Uint32Array=e.Uint32Array,Float32Array=e.Float32Array,Float64Array=e.Float64Array,BigInt64Array=e.BigInt64Array,BigUint64Array=e.BigUint64Array}uni.restoreGlobal&&uni.restoreGlobal(Vue,weex,plus,setTimeout,clearTimeout,setInterval,clearInterval),function(e){"use strict";function t(e,t,...n){uni.__log__&&uni.__log__(e,t,...n)}const n=t=>(n,o=e.getCurrentInstance())=>{!e.isInSSRComponentSetup&&e.injectHook(t,n,o)},o=n("onShow"),a=n("onHide"),s=n("onLaunch");function i(e,t,n){return Array.isArray(e)?(e.length=Math.max(e.length,t),e.splice(t,1,n),n):(e[t]=n,n)}function r(e,t){Array.isArray(e)?e.splice(t,1):delete e[t]}function c(){return"undefined"!=typeof navigator&&"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}}const l="function"==typeof Proxy;let d,u,m;function p(){return void 0!==d||("undefined"!=typeof window&&window.performance?(d=!0,u=window.performance):"undefined"!=typeof global&&(null===(e=global.perf_hooks)||void 0===e?void 0:e.performance)?(d=!0,u=global.perf_hooks.performance):d=!1),d?u.now():Date.now();var e}class g{constructor(e,t){this.target=null,this.targetQueue=[],this.onQueue=[],this.plugin=e,this.hook=t;const n={};if(e.settings)for(const i in e.settings){const t=e.settings[i];n[i]=t.defaultValue}const o=`__vue-devtools-plugin-settings__${e.id}`;let a=Object.assign({},n);try{const e=localStorage.getItem(o),t=JSON.parse(e);Object.assign(a,t)}catch(s){}this.fallbacks={getSettings:()=>a,setSettings(e){try{localStorage.setItem(o,JSON.stringify(e))}catch(s){}a=e},now:()=>p()},t&&t.on("plugin:settings:set",((e,t)=>{e===this.plugin.id&&this.fallbacks.setSettings(t)})),this.proxiedOn=new Proxy({},{get:(e,t)=>this.target?this.target.on[t]:(...e)=>{this.onQueue.push({method:t,args:e})}}),this.proxiedTarget=new Proxy({},{get:(e,t)=>this.target?this.target[t]:"on"===t?this.proxiedOn:Object.keys(this.fallbacks).includes(t)?(...e)=>(this.targetQueue.push({method:t,args:e,resolve:()=>{}}),this.fallbacks[t](...e)):(...e)=>new Promise((n=>{this.targetQueue.push({method:t,args:e,resolve:n})}))})}async setRealTarget(e){this.target=e;for(const t of this.onQueue)this.target.on[t.method](...t.args);for(const t of this.targetQueue)t.resolve(await this.target[t.method](...t.args))}}function f(e,t){const n=e,o=c(),a=c().__VUE_DEVTOOLS_GLOBAL_HOOK__,s=l&&n.enableEarlyProxy;if(!a||!o.__VUE_DEVTOOLS_PLUGIN_API_AVAILABLE__&&s){const e=s?new g(n,a):null;(o.__VUE_DEVTOOLS_PLUGINS__=o.__VUE_DEVTOOLS_PLUGINS__||[]).push({pluginDescriptor:n,setupFn:t,proxy:e}),e&&t(e.proxiedTarget)}else a.emit("devtools-plugin:setup",e,t)}
/*!
   * pinia v2.1.7
   * (c) 2023 Eduardo San Martin Morote
   * @license MIT
   */const v=e=>m=e,h=Symbol("pinia");function y(e){return e&&"object"==typeof e&&"[object Object]"===Object.prototype.toString.call(e)&&"function"!=typeof e.toJSON}var E,w;(w=E||(E={})).direct="direct",w.patchObject="patch object",w.patchFunction="patch function";const _="undefined"!=typeof window,N=_,b=(()=>"object"==typeof window&&window.window===window?window:"object"==typeof self&&self.self===self?self:"object"==typeof global&&global.global===global?global:"object"==typeof globalThis?globalThis:{HTMLElement:null})();function C(e,t,n){const o=new XMLHttpRequest;o.open("GET",e),o.responseType="blob",o.onload=function(){x(o.response,t,n)},o.onerror=function(){},o.send()}function S(e){const t=new XMLHttpRequest;t.open("HEAD",e,!1);try{t.send()}catch(n){}return t.status>=200&&t.status<=299}function V(e){try{e.dispatchEvent(new MouseEvent("click"))}catch(t){const n=document.createEvent("MouseEvents");n.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),e.dispatchEvent(n)}}const k="object"==typeof navigator?navigator:{userAgent:""},I=(()=>/Macintosh/.test(k.userAgent)&&/AppleWebKit/.test(k.userAgent)&&!/Safari/.test(k.userAgent))(),x=_?"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype&&!I?function(e,t="download",n){const o=document.createElement("a");o.download=t,o.rel="noopener","string"==typeof e?(o.href=e,o.origin!==location.origin?S(o.href)?C(e,t,n):(o.target="_blank",V(o)):V(o)):(o.href=URL.createObjectURL(e),setTimeout((function(){URL.revokeObjectURL(o.href)}),4e4),setTimeout((function(){V(o)}),0))}:"msSaveOrOpenBlob"in k?function(e,t="download",n){if("string"==typeof e)if(S(e))C(e,t,n);else{const t=document.createElement("a");t.href=e,t.target="_blank",setTimeout((function(){V(t)}))}else navigator.msSaveOrOpenBlob(function(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([String.fromCharCode(65279),e],{type:e.type}):e}(e,n),t)}:function(e,t,n,o){(o=o||open("","_blank"))&&(o.document.title=o.document.body.innerText="downloading...");if("string"==typeof e)return C(e,t,n);const a="application/octet-stream"===e.type,s=/constructor/i.test(String(b.HTMLElement))||"safari"in b,i=/CriOS\/[\d]+/.test(navigator.userAgent);if((i||a&&s||I)&&"undefined"!=typeof FileReader){const t=new FileReader;t.onloadend=function(){let e=t.result;if("string"!=typeof e)throw o=null,new Error("Wrong reader.result type");e=i?e:e.replace(/^data:[^;]*;/,"data:attachment/file;"),o?o.location.href=e:location.assign(e),o=null},t.readAsDataURL(e)}else{const t=URL.createObjectURL(e);o?o.location.assign(t):location.href=t,o=null,setTimeout((function(){URL.revokeObjectURL(t)}),4e4)}}:()=>{};function T(e,t){"function"==typeof __VUE_DEVTOOLS_TOAST__&&__VUE_DEVTOOLS_TOAST__("🍍 "+e,t)}function O(e){return"_a"in e&&"install"in e}function A(){if(!("clipboard"in navigator))return T("Your browser doesn't support the Clipboard API","error"),!0}function R(e){return!!(e instanceof Error&&e.message.toLowerCase().includes("document is not focused"))&&(T('You need to activate the "Emulate a focused page" setting in the "Rendering" panel of devtools.',"warn"),!0)}let P;async function M(e){try{const t=(P||(P=document.createElement("input"),P.type="file",P.accept=".json"),function(){return new Promise(((e,t)=>{P.onchange=async()=>{const t=P.files;if(!t)return e(null);const n=t.item(0);return e(n?{text:await n.text(),file:n}:null)},P.oncancel=()=>e(null),P.onerror=t,P.click()}))}),n=await t();if(!n)return;const{text:o,file:a}=n;D(e,JSON.parse(o)),T(`Global state imported from "${a.name}".`)}catch(t){T("Failed to import the state from JSON. Check the console for more details.","error")}}function D(e,t){for(const n in t){const o=e.state.value[n];o?Object.assign(o,t[n]):e.state.value[n]=t[n]}}function j(e){return{_custom:{display:e}}}const $="🍍 Pinia (root)",B="_root";function L(e){return O(e)?{id:B,label:$}:{id:e.$id,label:e.$id}}function U(e){return e?Array.isArray(e)?e.reduce(((e,t)=>(e.keys.push(t.key),e.operations.push(t.type),e.oldValue[t.key]=t.oldValue,e.newValue[t.key]=t.newValue,e)),{oldValue:{},keys:[],operations:[],newValue:{}}):{operation:j(e.type),key:j(e.key),oldValue:e.oldValue,newValue:e.newValue}:{}}function F(e){switch(e){case E.direct:return"mutation";case E.patchFunction:case E.patchObject:return"$patch";default:return"unknown"}}let H=!0;const G=[],q="pinia:mutations",Y="pinia",{assign:K}=Object,z=e=>"🍍 "+e;function J(t,n){f({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:G,app:t},(o=>{"function"!=typeof o.now&&T("You seem to be using an outdated version of Vue Devtools. Are you still using the Beta release instead of the stable one? You can find the links at https://devtools.vuejs.org/guide/installation.html."),o.addTimelineLayer({id:q,label:"Pinia 🍍",color:15064968}),o.addInspector({id:Y,label:"Pinia 🍍",icon:"storage",treeFilterPlaceholder:"Search stores",actions:[{icon:"content_copy",action:()=>{!async function(e){if(!A())try{await navigator.clipboard.writeText(JSON.stringify(e.state.value)),T("Global state copied to clipboard.")}catch(t){if(R(t))return;T("Failed to serialize the state. Check the console for more details.","error")}}(n)},tooltip:"Serialize and copy the state"},{icon:"content_paste",action:async()=>{await async function(e){if(!A())try{D(e,JSON.parse(await navigator.clipboard.readText())),T("Global state pasted from clipboard.")}catch(t){if(R(t))return;T("Failed to deserialize the state from clipboard. Check the console for more details.","error")}}(n),o.sendInspectorTree(Y),o.sendInspectorState(Y)},tooltip:"Replace the state with the content of your clipboard"},{icon:"save",action:()=>{!async function(e){try{x(new Blob([JSON.stringify(e.state.value)],{type:"text/plain;charset=utf-8"}),"pinia-state.json")}catch(t){T("Failed to export the state as JSON. Check the console for more details.","error")}}(n)},tooltip:"Save the state as a JSON file"},{icon:"folder_open",action:async()=>{await M(n),o.sendInspectorTree(Y),o.sendInspectorState(Y)},tooltip:"Import the state from a JSON file"}],nodeActions:[{icon:"restore",tooltip:'Reset the state (with "$reset")',action:e=>{const t=n._s.get(e);t?"function"!=typeof t.$reset?T(`Cannot reset "${e}" store because it doesn't have a "$reset" method implemented.`,"warn"):(t.$reset(),T(`Store "${e}" reset.`)):T(`Cannot reset "${e}" store because it wasn't found.`,"warn")}}]}),o.on.inspectComponent(((t,n)=>{const o=t.componentInstance&&t.componentInstance.proxy;if(o&&o._pStores){const n=t.componentInstance.proxy._pStores;Object.values(n).forEach((n=>{t.instanceData.state.push({type:z(n.$id),key:"state",editable:!0,value:n._isOptionsAPI?{_custom:{value:e.toRaw(n.$state),actions:[{icon:"restore",tooltip:"Reset the state of this store",action:()=>n.$reset()}]}}:Object.keys(n.$state).reduce(((e,t)=>(e[t]=n.$state[t],e)),{})}),n._getters&&n._getters.length&&t.instanceData.state.push({type:z(n.$id),key:"getters",editable:!1,value:n._getters.reduce(((e,t)=>{try{e[t]=n[t]}catch(o){e[t]=o}return e}),{})})}))}})),o.on.getInspectorTree((e=>{if(e.app===t&&e.inspectorId===Y){let t=[n];t=t.concat(Array.from(n._s.values())),e.rootNodes=(e.filter?t.filter((t=>"$id"in t?t.$id.toLowerCase().includes(e.filter.toLowerCase()):$.toLowerCase().includes(e.filter.toLowerCase()))):t).map(L)}})),o.on.getInspectorState((e=>{if(e.app===t&&e.inspectorId===Y){const t=e.nodeId===B?n:n._s.get(e.nodeId);if(!t)return;t&&(e.state=function(e){if(O(e)){const t=Array.from(e._s.keys()),n=e._s;return{state:t.map((t=>({editable:!0,key:t,value:e.state.value[t]}))),getters:t.filter((e=>n.get(e)._getters)).map((e=>{const t=n.get(e);return{editable:!1,key:e,value:t._getters.reduce(((e,n)=>(e[n]=t[n],e)),{})}}))}}const t={state:Object.keys(e.$state).map((t=>({editable:!0,key:t,value:e.$state[t]})))};return e._getters&&e._getters.length&&(t.getters=e._getters.map((t=>({editable:!1,key:t,value:e[t]})))),e._customProperties.size&&(t.customProperties=Array.from(e._customProperties).map((t=>({editable:!0,key:t,value:e[t]})))),t}(t))}})),o.on.editInspectorState(((e,o)=>{if(e.app===t&&e.inspectorId===Y){const t=e.nodeId===B?n:n._s.get(e.nodeId);if(!t)return T(`store "${e.nodeId}" not found`,"error");const{path:o}=e;O(t)?o.unshift("state"):1===o.length&&t._customProperties.has(o[0])&&!(o[0]in t.$state)||o.unshift("$state"),H=!1,e.set(t,o,e.state.value),H=!0}})),o.on.editComponentState((e=>{if(e.type.startsWith("🍍")){const t=e.type.replace(/^🍍\s*/,""),o=n._s.get(t);if(!o)return T(`store "${t}" not found`,"error");const{path:a}=e;if("state"!==a[0])return T(`Invalid path for store "${t}":\n${a}\nOnly state can be modified.`);a[0]="$state",H=!1,e.set(o,a,e.state.value),H=!0}}))}))}let Q,W=0;function X(t,n,o){const a=n.reduce(((n,o)=>(n[o]=e.toRaw(t)[o],n)),{});for(const e in a)t[e]=function(){const n=W,s=o?new Proxy(t,{get:(...e)=>(Q=n,Reflect.get(...e)),set:(...e)=>(Q=n,Reflect.set(...e))}):t;Q=n;const i=a[e].apply(s,arguments);return Q=void 0,i}}function Z({app:t,store:n,options:o}){if(n.$id.startsWith("__hot:"))return;n._isOptionsAPI=!!o.state,X(n,Object.keys(o.actions),n._isOptionsAPI);const a=n._hotUpdate;e.toRaw(n)._hotUpdate=function(e){a.apply(this,arguments),X(n,Object.keys(e._hmrPayload.actions),!!n._isOptionsAPI)},function(t,n){G.includes(z(n.$id))||G.push(z(n.$id)),f({id:"dev.esm.pinia",label:"Pinia 🍍",logo:"https://pinia.vuejs.org/logo.svg",packageName:"pinia",homepage:"https://pinia.vuejs.org",componentStateTypes:G,app:t,settings:{logStoreChanges:{label:"Notify about new/deleted stores",type:"boolean",defaultValue:!0}}},(t=>{const o="function"==typeof t.now?t.now.bind(t):Date.now;n.$onAction((({after:e,onError:a,name:s,args:i})=>{const r=W++;t.addTimelineEvent({layerId:q,event:{time:o(),title:"🛫 "+s,subtitle:"start",data:{store:j(n.$id),action:j(s),args:i},groupId:r}}),e((e=>{Q=void 0,t.addTimelineEvent({layerId:q,event:{time:o(),title:"🛬 "+s,subtitle:"end",data:{store:j(n.$id),action:j(s),args:i,result:e},groupId:r}})})),a((e=>{Q=void 0,t.addTimelineEvent({layerId:q,event:{time:o(),logType:"error",title:"💥 "+s,subtitle:"end",data:{store:j(n.$id),action:j(s),args:i,error:e},groupId:r}})}))}),!0),n._customProperties.forEach((a=>{e.watch((()=>e.unref(n[a])),((e,n)=>{t.notifyComponentUpdate(),t.sendInspectorState(Y),H&&t.addTimelineEvent({layerId:q,event:{time:o(),title:"Change",subtitle:a,data:{newValue:e,oldValue:n},groupId:Q}})}),{deep:!0})})),n.$subscribe((({events:e,type:a},s)=>{if(t.notifyComponentUpdate(),t.sendInspectorState(Y),!H)return;const i={time:o(),title:F(a),data:K({store:j(n.$id)},U(e)),groupId:Q};a===E.patchFunction?i.subtitle="⤵️":a===E.patchObject?i.subtitle="🧩":e&&!Array.isArray(e)&&(i.subtitle=e.type),e&&(i.data["rawEvent(s)"]={_custom:{display:"DebuggerEvent",type:"object",tooltip:"raw DebuggerEvent[]",value:e}}),t.addTimelineEvent({layerId:q,event:i})}),{detached:!0,flush:"sync"});const a=n._hotUpdate;n._hotUpdate=e.markRaw((e=>{a(e),t.addTimelineEvent({layerId:q,event:{time:o(),title:"🔥 "+n.$id,subtitle:"HMR update",data:{store:j(n.$id),info:j("HMR update")}}}),t.notifyComponentUpdate(),t.sendInspectorTree(Y),t.sendInspectorState(Y)}));const{$dispose:s}=n;n.$dispose=()=>{s(),t.notifyComponentUpdate(),t.sendInspectorTree(Y),t.sendInspectorState(Y),t.getSettings().logStoreChanges&&T(`Disposed "${n.$id}" store 🗑`)},t.notifyComponentUpdate(),t.sendInspectorTree(Y),t.sendInspectorState(Y),t.getSettings().logStoreChanges&&T(`"${n.$id}" store installed 🆕`)}))}(t,n)}function ee(t,n){for(const o in n){const a=n[o];if(!(o in t))continue;const s=t[o];y(s)&&y(a)&&!e.isRef(a)&&!e.isReactive(a)?t[o]=ee(s,a):t[o]=a}return t}const te=()=>{};function ne(t,n,o,a=te){t.push(n);const s=()=>{const e=t.indexOf(n);e>-1&&(t.splice(e,1),a())};return!o&&e.getCurrentScope()&&e.onScopeDispose(s),s}function oe(e,...t){e.slice().forEach((e=>{e(...t)}))}const ae=e=>e();function se(t,n){t instanceof Map&&n instanceof Map&&n.forEach(((e,n)=>t.set(n,e))),t instanceof Set&&n instanceof Set&&n.forEach(t.add,t);for(const o in n){if(!n.hasOwnProperty(o))continue;const a=n[o],s=t[o];y(s)&&y(a)&&t.hasOwnProperty(o)&&!e.isRef(a)&&!e.isReactive(a)?t[o]=se(s,a):t[o]=a}return t}const ie=Symbol("pinia:skipHydration");const{assign:re}=Object;function ce(t){return!(!e.isRef(t)||!t.effect)}function le(t,n,o,a){const{state:s,actions:i,getters:r}=n,c=o.state.value[t];let l;return l=de(t,(function(){c||a||(o.state.value[t]=s?s():{});const n=a?e.toRefs(e.ref(s?s():{}).value):e.toRefs(o.state.value[t]);return re(n,i,Object.keys(r||{}).reduce(((n,a)=>(n[a]=e.markRaw(e.computed((()=>{v(o);const e=o._s.get(t);return r[a].call(e,e)}))),n)),{}))}),n,o,a,!0),l}function de(t,n,o={},a,s,c){let l;const d=re({actions:{}},o);if(!a._e.active)throw new Error("Pinia destroyed");const u={deep:!0};let m,p;u.onTrigger=e=>{m?g=e:0!=m||T._hotUpdating||Array.isArray(g)&&g.push(e)};let g,f=[],h=[];const w=a.state.value[t];c||w||s||(a.state.value[t]={});const b=e.ref({});let C;function S(n){let o;m=p=!1,g=[],"function"==typeof n?(n(a.state.value[t]),o={type:E.patchFunction,storeId:t,events:g}):(se(a.state.value[t],n),o={type:E.patchObject,payload:n,storeId:t,events:g});const s=C=Symbol();e.nextTick().then((()=>{C===s&&(m=!0)})),p=!0,oe(f,o,a.state.value[t])}const V=c?function(){const{state:e}=o,t=e?e():{};this.$patch((e=>{re(e,t)}))}:()=>{throw new Error(`🍍: Store "${t}" is built using the setup syntax and does not implement $reset().`)};function k(e,n){return function(){v(a);const o=Array.from(arguments),s=[],i=[];function r(e){s.push(e)}function c(e){i.push(e)}let l;oe(h,{args:o,name:e,store:T,after:r,onError:c});try{l=n.apply(this&&this.$id===t?this:T,o)}catch(d){throw oe(i,d),d}return l instanceof Promise?l.then((e=>(oe(s,e),e))).catch((e=>(oe(i,e),Promise.reject(e)))):(oe(s,l),l)}}const I=e.markRaw({actions:{},getters:{},state:[],hotState:b}),x={_p:a,$id:t,$onAction:ne.bind(null,h),$patch:S,$reset:V,$subscribe(n,o={}){const s=ne(f,n,o.detached,(()=>i())),i=l.run((()=>e.watch((()=>a.state.value[t]),(e=>{("sync"===o.flush?p:m)&&n({storeId:t,type:E.direct,events:g},e)}),re({},u,o))));return s},$dispose:function(){l.stop(),f=[],h=[],a._s.delete(t)}},T=e.reactive(re({_hmrPayload:I,_customProperties:e.markRaw(new Set)},x));a._s.set(t,T);const O=(a._a&&a._a.runWithContext||ae)((()=>a._e.run((()=>(l=e.effectScope()).run(n)))));for(const r in O){const n=O[r];if(e.isRef(n)&&!ce(n)||e.isReactive(n))s?i(b.value,r,e.toRef(O,r)):c||(!w||y(A=n)&&A.hasOwnProperty(ie)||(e.isRef(n)?n.value=w[r]:se(n,w[r])),a.state.value[t][r]=n),I.state.push(r);else if("function"==typeof n){const e=s?n:k(r,n);O[r]=e,I.actions[r]=n,d.actions[r]=n}else if(ce(n)&&(I.getters[r]=c?o.getters[r]:n,_)){(O._getters||(O._getters=e.markRaw([]))).push(r)}}var A;if(re(T,O),re(e.toRaw(T),O),Object.defineProperty(T,"$state",{get:()=>s?b.value:a.state.value[t],set:e=>{if(s)throw new Error("cannot set hotState");S((t=>{re(t,e)}))}}),T._hotUpdate=e.markRaw((n=>{T._hotUpdating=!0,n._hmrPayload.state.forEach((t=>{if(t in T.$state){const e=n.$state[t],o=T.$state[t];"object"==typeof e&&y(e)&&y(o)?ee(e,o):n.$state[t]=o}i(T,t,e.toRef(n.$state,t))})),Object.keys(T.$state).forEach((e=>{e in n.$state||r(T,e)})),m=!1,p=!1,a.state.value[t]=e.toRef(n._hmrPayload,"hotState"),p=!0,e.nextTick().then((()=>{m=!0}));for(const e in n._hmrPayload.actions){const t=n[e];i(T,e,k(e,t))}for(const t in n._hmrPayload.getters){const o=n._hmrPayload.getters[t],s=c?e.computed((()=>(v(a),o.call(T,T)))):o;i(T,t,s)}Object.keys(T._hmrPayload.getters).forEach((e=>{e in n._hmrPayload.getters||r(T,e)})),Object.keys(T._hmrPayload.actions).forEach((e=>{e in n._hmrPayload.actions||r(T,e)})),T._hmrPayload=n._hmrPayload,T._getters=n._getters,T._hotUpdating=!1})),N){const e={writable:!0,configurable:!0,enumerable:!1};["_p","_hmrPayload","_getters","_customProperties"].forEach((t=>{Object.defineProperty(T,t,re({value:T[t]},e))}))}return a._p.forEach((e=>{if(N){const t=l.run((()=>e({store:T,app:a._a,pinia:a,options:d})));Object.keys(t||{}).forEach((e=>T._customProperties.add(e))),re(T,t)}else re(T,l.run((()=>e({store:T,app:a._a,pinia:a,options:d}))))})),T.$state&&"object"==typeof T.$state&&"function"==typeof T.$state.constructor&&T.$state.constructor.toString().includes("[native code]"),w&&c&&o.hydrate&&o.hydrate(T.$state,w),m=!0,p=!0,T}function ue(t,n,o){let a,s;const i="function"==typeof n;if("string"==typeof t)a=t,s=i?o:n;else if(s=t,a=t.id,"string"!=typeof a)throw new Error('[🍍]: "defineStore()" must be passed a store id as its first argument.');function r(t,o){const c=e.hasInjectionContext();if((t=t||(c?e.inject(h,null):null))&&v(t),!m)throw new Error('[🍍]: "getActivePinia()" was called but there was no active Pinia. Are you trying to use a store before calling "app.use(pinia)"?\nSee https://pinia.vuejs.org/core-concepts/outside-component-usage.html for help.\nThis will fail in production.');(t=m)._s.has(a)||(i?de(a,n,s,t):le(a,s,t),r._pinia=t);const l=t._s.get(a);if(o){const e="__hot:"+a,r=i?de(e,n,s,t,!0):le(e,re({},s),t,!0);o._hotUpdate(r),delete t.state.value[e],t._s.delete(e)}if(_){const t=e.getCurrentInstance();if(t&&t.proxy&&!o){const e=t.proxy;("_pStores"in e?e._pStores:e._pStores={})[a]=l}}return l}return r.$id=a,r}const me="http://192.168.101.18:8090";var pe,ge,fe,ve,he,ye;(ge=pe||(pe={})).STRING="string",ge.NUMBER="number",ge.INTEGER="integer",ge.BOOLEAN="boolean",ge.ARRAY="array",ge.OBJECT="object",(ve=fe||(fe={})).LANGUAGE_UNSPECIFIED="language_unspecified",ve.PYTHON="python",(ye=he||(he={})).OUTCOME_UNSPECIFIED="outcome_unspecified",ye.OUTCOME_OK="outcome_ok",ye.OUTCOME_FAILED="outcome_failed",ye.OUTCOME_DEADLINE_EXCEEDED="outcome_deadline_exceeded";
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */
const Ee=["user","model","function","system"];var we,_e,Ne,be,Ce,Se,Ve,ke,Ie,xe,Te,Oe,Ae,Re,Pe,Me;(_e=we||(we={})).HARM_CATEGORY_UNSPECIFIED="HARM_CATEGORY_UNSPECIFIED",_e.HARM_CATEGORY_HATE_SPEECH="HARM_CATEGORY_HATE_SPEECH",_e.HARM_CATEGORY_SEXUALLY_EXPLICIT="HARM_CATEGORY_SEXUALLY_EXPLICIT",_e.HARM_CATEGORY_HARASSMENT="HARM_CATEGORY_HARASSMENT",_e.HARM_CATEGORY_DANGEROUS_CONTENT="HARM_CATEGORY_DANGEROUS_CONTENT",_e.HARM_CATEGORY_CIVIC_INTEGRITY="HARM_CATEGORY_CIVIC_INTEGRITY",(be=Ne||(Ne={})).HARM_BLOCK_THRESHOLD_UNSPECIFIED="HARM_BLOCK_THRESHOLD_UNSPECIFIED",be.BLOCK_LOW_AND_ABOVE="BLOCK_LOW_AND_ABOVE",be.BLOCK_MEDIUM_AND_ABOVE="BLOCK_MEDIUM_AND_ABOVE",be.BLOCK_ONLY_HIGH="BLOCK_ONLY_HIGH",be.BLOCK_NONE="BLOCK_NONE",(Se=Ce||(Ce={})).HARM_PROBABILITY_UNSPECIFIED="HARM_PROBABILITY_UNSPECIFIED",Se.NEGLIGIBLE="NEGLIGIBLE",Se.LOW="LOW",Se.MEDIUM="MEDIUM",Se.HIGH="HIGH",(ke=Ve||(Ve={})).BLOCKED_REASON_UNSPECIFIED="BLOCKED_REASON_UNSPECIFIED",ke.SAFETY="SAFETY",ke.OTHER="OTHER",(xe=Ie||(Ie={})).FINISH_REASON_UNSPECIFIED="FINISH_REASON_UNSPECIFIED",xe.STOP="STOP",xe.MAX_TOKENS="MAX_TOKENS",xe.SAFETY="SAFETY",xe.RECITATION="RECITATION",xe.LANGUAGE="LANGUAGE",xe.BLOCKLIST="BLOCKLIST",xe.PROHIBITED_CONTENT="PROHIBITED_CONTENT",xe.SPII="SPII",xe.MALFORMED_FUNCTION_CALL="MALFORMED_FUNCTION_CALL",xe.OTHER="OTHER",(Oe=Te||(Te={})).TASK_TYPE_UNSPECIFIED="TASK_TYPE_UNSPECIFIED",Oe.RETRIEVAL_QUERY="RETRIEVAL_QUERY",Oe.RETRIEVAL_DOCUMENT="RETRIEVAL_DOCUMENT",Oe.SEMANTIC_SIMILARITY="SEMANTIC_SIMILARITY",Oe.CLASSIFICATION="CLASSIFICATION",Oe.CLUSTERING="CLUSTERING",(Re=Ae||(Ae={})).MODE_UNSPECIFIED="MODE_UNSPECIFIED",Re.AUTO="AUTO",Re.ANY="ANY",Re.NONE="NONE",(Me=Pe||(Pe={})).MODE_UNSPECIFIED="MODE_UNSPECIFIED",Me.MODE_DYNAMIC="MODE_DYNAMIC";
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */
class De extends Error{constructor(e){super(`[GoogleGenerativeAI Error]: ${e}`)}}class je extends De{constructor(e,t){super(e),this.response=t}}class $e extends De{constructor(e,t,n,o){super(e),this.status=t,this.statusText=n,this.errorDetails=o}}class Be extends De{}class Le extends De{}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */var Ue,Fe;(Fe=Ue||(Ue={})).GENERATE_CONTENT="generateContent",Fe.STREAM_GENERATE_CONTENT="streamGenerateContent",Fe.COUNT_TOKENS="countTokens",Fe.EMBED_CONTENT="embedContent",Fe.BATCH_EMBED_CONTENTS="batchEmbedContents";class He{constructor(e,t,n,o,a){this.model=e,this.task=t,this.apiKey=n,this.stream=o,this.requestOptions=a}toString(){var e,t;const n=(null===(e=this.requestOptions)||void 0===e?void 0:e.apiVersion)||"v1beta";let o=`${(null===(t=this.requestOptions)||void 0===t?void 0:t.baseUrl)||"https://generativelanguage.googleapis.com"}/${n}/${this.model}:${this.task}`;return this.stream&&(o+="?alt=sse"),o}}async function Ge(e){var t;const n=new Headers;n.append("Content-Type","application/json"),n.append("x-goog-api-client",function(e){const t=[];return(null==e?void 0:e.apiClient)&&t.push(e.apiClient),t.push("genai-js/0.24.1"),t.join(" ")}(e.requestOptions)),n.append("x-goog-api-key",e.apiKey);let o=null===(t=e.requestOptions)||void 0===t?void 0:t.customHeaders;if(o){if(!(o instanceof Headers))try{o=new Headers(o)}catch(a){throw new Be(`unable to convert customHeaders value ${JSON.stringify(o)} to Headers: ${a.message}`)}for(const[e,t]of o.entries()){if("x-goog-api-key"===e)throw new Be(`Cannot set reserved header name ${e}`);if("x-goog-api-client"===e)throw new Be(`Header name ${e} can only be set using the apiClient field`);n.append(e,t)}}return n}async function qe(e,t,n,o,a,s={},i=fetch){const{url:r,fetchOptions:c}=await async function(e,t,n,o,a,s){const i=new He(e,t,n,o,s);return{url:i.toString(),fetchOptions:Object.assign(Object.assign({},Ye(s)),{method:"POST",headers:await Ge(i),body:a})}}(e,t,n,o,a,s);return async function(e,t,n=fetch){let o;try{o=await n(e,t)}catch(a){!function(e,t){let n=e;"AbortError"===n.name?(n=new Le(`Request aborted when fetching ${t.toString()}: ${e.message}`),n.stack=e.stack):e instanceof $e||e instanceof Be||(n=new De(`Error fetching from ${t.toString()}: ${e.message}`),n.stack=e.stack);throw n}(a,e)}o.ok||await async function(e,t){let n,o="";try{const t=await e.json();o=t.error.message,t.error.details&&(o+=` ${JSON.stringify(t.error.details)}`,n=t.error.details)}catch(a){}throw new $e(`Error fetching from ${t.toString()}: [${e.status} ${e.statusText}] ${o}`,e.status,e.statusText,n)}(o,e);return o}(r,c,i)}function Ye(e){const t={};if(void 0!==(null==e?void 0:e.signal)||(null==e?void 0:e.timeout)>=0){const n=new AbortController;(null==e?void 0:e.timeout)>=0&&setTimeout((()=>n.abort()),e.timeout),(null==e?void 0:e.signal)&&e.signal.addEventListener("abort",(()=>{n.abort()})),t.signal=n.signal}return t}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */function Ke(e){return e.text=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&t("warn","at node_modules/@google/generative-ai/dist/index.mjs:480",`This response had ${e.candidates.length} candidates. Returning text from the first candidate only. Access response.candidates directly to use the other candidates.`),Qe(e.candidates[0]))throw new je(`${We(e)}`,e);return function(e){var t,n,o,a;const s=[];if(null===(n=null===(t=e.candidates)||void 0===t?void 0:t[0].content)||void 0===n?void 0:n.parts)for(const i of null===(a=null===(o=e.candidates)||void 0===o?void 0:o[0].content)||void 0===a?void 0:a.parts)i.text&&s.push(i.text),i.executableCode&&s.push("\n```"+i.executableCode.language+"\n"+i.executableCode.code+"\n```\n"),i.codeExecutionResult&&s.push("\n```\n"+i.codeExecutionResult.output+"\n```\n");return s.length>0?s.join(""):""}(e)}if(e.promptFeedback)throw new je(`Text not available. ${We(e)}`,e);return""},e.functionCall=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&t("warn","at node_modules/@google/generative-ai/dist/index.mjs:500",`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),Qe(e.candidates[0]))throw new je(`${We(e)}`,e);return t("warn","at node_modules/@google/generative-ai/dist/index.mjs:507","response.functionCall() is deprecated. Use response.functionCalls() instead."),ze(e)[0]}if(e.promptFeedback)throw new je(`Function call not available. ${We(e)}`,e)},e.functionCalls=()=>{if(e.candidates&&e.candidates.length>0){if(e.candidates.length>1&&t("warn","at node_modules/@google/generative-ai/dist/index.mjs:519",`This response had ${e.candidates.length} candidates. Returning function calls from the first candidate only. Access response.candidates directly to use the other candidates.`),Qe(e.candidates[0]))throw new je(`${We(e)}`,e);return ze(e)}if(e.promptFeedback)throw new je(`Function call not available. ${We(e)}`,e)},e}function ze(e){var t,n,o,a;const s=[];if(null===(n=null===(t=e.candidates)||void 0===t?void 0:t[0].content)||void 0===n?void 0:n.parts)for(const i of null===(a=null===(o=e.candidates)||void 0===o?void 0:o[0].content)||void 0===a?void 0:a.parts)i.functionCall&&s.push(i.functionCall);return s.length>0?s:void 0}const Je=[Ie.RECITATION,Ie.SAFETY,Ie.LANGUAGE];function Qe(e){return!!e.finishReason&&Je.includes(e.finishReason)}function We(e){var t,n,o;let a="";if(e.candidates&&0!==e.candidates.length||!e.promptFeedback){if(null===(o=e.candidates)||void 0===o?void 0:o[0]){const t=e.candidates[0];Qe(t)&&(a+=`Candidate was blocked due to ${t.finishReason}`,t.finishMessage&&(a+=`: ${t.finishMessage}`))}}else a+="Response was blocked",(null===(t=e.promptFeedback)||void 0===t?void 0:t.blockReason)&&(a+=` due to ${e.promptFeedback.blockReason}`),(null===(n=e.promptFeedback)||void 0===n?void 0:n.blockReasonMessage)&&(a+=`: ${e.promptFeedback.blockReasonMessage}`);return a}function Xe(e){return this instanceof Xe?(this.v=e,this):new Xe(e)}function Ze(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var o,a=n.apply(e,t||[]),s=[];return o={},i("next"),i("throw"),i("return"),o[Symbol.asyncIterator]=function(){return this},o;function i(e){a[e]&&(o[e]=function(t){return new Promise((function(n,o){s.push([e,t,n,o])>1||r(e,t)}))})}function r(e,t){try{(n=a[e](t)).value instanceof Xe?Promise.resolve(n.value.v).then(c,l):d(s[0][2],n)}catch(o){d(s[0][3],o)}var n}function c(e){r("next",e)}function l(e){r("throw",e)}function d(e,t){e(t),s.shift(),s.length&&r(s[0][0],s[0][1])}}"function"==typeof SuppressedError&&SuppressedError;
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */
const et=/^data\: (.*)(?:\n\n|\r\r|\r\n\r\n)/;function tt(e){const t=function(e){const t=e.getReader();return new ReadableStream({start(e){let n="";return o();function o(){return t.read().then((({value:t,done:a})=>{if(a)return n.trim()?void e.error(new De("Failed to parse stream")):void e.close();n+=t;let s,i=n.match(et);for(;i;){try{s=JSON.parse(i[1])}catch(r){return void e.error(new De(`Error parsing JSON response: "${i[1]}"`))}e.enqueue(s),n=n.substring(i[0].length),i=n.match(et)}return o()})).catch((e=>{let t=e;throw t.stack=e.stack,t="AbortError"===t.name?new Le("Request aborted when reading from the stream"):new De("Error reading from the stream"),t}))}}})}(e.body.pipeThrough(new TextDecoderStream("utf8",{fatal:!0}))),[n,o]=t.tee();return{stream:ot(n),response:nt(o)}}async function nt(e){const t=[],n=e.getReader();for(;;){const{done:e,value:o}=await n.read();if(e)return Ke(at(t));t.push(o)}}function ot(e){return Ze(this,arguments,(function*(){const t=e.getReader();for(;;){const{value:e,done:n}=yield Xe(t.read());if(n)break;yield yield Xe(Ke(e))}}))}function at(e){const t=e[e.length-1],n={promptFeedback:null==t?void 0:t.promptFeedback};for(const o of e){if(o.candidates){let e=0;for(const t of o.candidates)if(n.candidates||(n.candidates=[]),n.candidates[e]||(n.candidates[e]={index:e}),n.candidates[e].citationMetadata=t.citationMetadata,n.candidates[e].groundingMetadata=t.groundingMetadata,n.candidates[e].finishReason=t.finishReason,n.candidates[e].finishMessage=t.finishMessage,n.candidates[e].safetyRatings=t.safetyRatings,t.content&&t.content.parts){n.candidates[e].content||(n.candidates[e].content={role:t.content.role||"user",parts:[]});const o={};for(const a of t.content.parts)a.text&&(o.text=a.text),a.functionCall&&(o.functionCall=a.functionCall),a.executableCode&&(o.executableCode=a.executableCode),a.codeExecutionResult&&(o.codeExecutionResult=a.codeExecutionResult),0===Object.keys(o).length&&(o.text=""),n.candidates[e].content.parts.push(o)}e++}o.usageMetadata&&(n.usageMetadata=o.usageMetadata)}return n}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */async function st(e,t,n,o){return tt(await qe(t,Ue.STREAM_GENERATE_CONTENT,e,!0,JSON.stringify(n),o))}async function it(e,t,n,o){const a=await qe(t,Ue.GENERATE_CONTENT,e,!1,JSON.stringify(n),o);return{response:Ke(await a.json())}}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */function rt(e){if(null!=e)return"string"==typeof e?{role:"system",parts:[{text:e}]}:e.text?{role:"system",parts:[e]}:e.parts?e.role?e:{role:"system",parts:e.parts}:void 0}function ct(e){let t=[];if("string"==typeof e)t=[{text:e}];else for(const n of e)"string"==typeof n?t.push({text:n}):t.push(n);return function(e){const t={role:"user",parts:[]},n={role:"function",parts:[]};let o=!1,a=!1;for(const s of e)"functionResponse"in s?(n.parts.push(s),a=!0):(t.parts.push(s),o=!0);if(o&&a)throw new De("Within a single message, FunctionResponse cannot be mixed with other type of part in the request for sending chat message.");if(!o&&!a)throw new De("No content is provided for sending chat message.");if(o)return t;return n}(t)}function lt(e){let t;if(e.contents)t=e;else{t={contents:[ct(e)]}}return e.systemInstruction&&(t.systemInstruction=rt(e.systemInstruction)),t}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */
const dt=["text","inlineData","functionCall","functionResponse","executableCode","codeExecutionResult"],ut={user:["text","inlineData"],function:["functionResponse"],model:["text","functionCall","executableCode","codeExecutionResult"],system:["text"]};function mt(e){var t;if(void 0===e.candidates||0===e.candidates.length)return!1;const n=null===(t=e.candidates[0])||void 0===t?void 0:t.content;if(void 0===n)return!1;if(void 0===n.parts||0===n.parts.length)return!1;for(const o of n.parts){if(void 0===o||0===Object.keys(o).length)return!1;if(void 0!==o.text&&""===o.text)return!1}return!0}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */const pt="SILENT_ERROR";class gt{constructor(e,t,n,o={}){this.model=t,this.params=n,this._requestOptions=o,this._history=[],this._sendPromise=Promise.resolve(),this._apiKey=e,(null==n?void 0:n.history)&&(!function(e){let t=!1;for(const n of e){const{role:e,parts:o}=n;if(!t&&"user"!==e)throw new De(`First content should be with role 'user', got ${e}`);if(!Ee.includes(e))throw new De(`Each item should include role field. Got ${e} but valid roles are: ${JSON.stringify(Ee)}`);if(!Array.isArray(o))throw new De("Content should have 'parts' property with an array of Parts");if(0===o.length)throw new De("Each Content should have at least one part");const a={text:0,inlineData:0,functionCall:0,functionResponse:0,fileData:0,executableCode:0,codeExecutionResult:0};for(const t of o)for(const e of dt)e in t&&(a[e]+=1);const s=ut[e];for(const t of dt)if(!s.includes(t)&&a[t]>0)throw new De(`Content with role '${e}' can't contain '${t}' part`);t=!0}}(n.history),this._history=n.history)}async getHistory(){return await this._sendPromise,this._history}async sendMessage(e,n={}){var o,a,s,i,r,c;await this._sendPromise;const l=ct(e),d={safetySettings:null===(o=this.params)||void 0===o?void 0:o.safetySettings,generationConfig:null===(a=this.params)||void 0===a?void 0:a.generationConfig,tools:null===(s=this.params)||void 0===s?void 0:s.tools,toolConfig:null===(i=this.params)||void 0===i?void 0:i.toolConfig,systemInstruction:null===(r=this.params)||void 0===r?void 0:r.systemInstruction,cachedContent:null===(c=this.params)||void 0===c?void 0:c.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),n);let m;return this._sendPromise=this._sendPromise.then((()=>it(this._apiKey,this.model,d,u))).then((e=>{var n;if(mt(e.response)){this._history.push(l);const t=Object.assign({parts:[],role:"model"},null===(n=e.response.candidates)||void 0===n?void 0:n[0].content);this._history.push(t)}else{const n=We(e.response);n&&t("warn","at node_modules/@google/generative-ai/dist/index.mjs:1198",`sendMessage() was unsuccessful. ${n}. Inspect response object for details.`)}m=e})).catch((e=>{throw this._sendPromise=Promise.resolve(),e})),await this._sendPromise,m}async sendMessageStream(e,n={}){var o,a,s,i,r,c;await this._sendPromise;const l=ct(e),d={safetySettings:null===(o=this.params)||void 0===o?void 0:o.safetySettings,generationConfig:null===(a=this.params)||void 0===a?void 0:a.generationConfig,tools:null===(s=this.params)||void 0===s?void 0:s.tools,toolConfig:null===(i=this.params)||void 0===i?void 0:i.toolConfig,systemInstruction:null===(r=this.params)||void 0===r?void 0:r.systemInstruction,cachedContent:null===(c=this.params)||void 0===c?void 0:c.cachedContent,contents:[...this._history,l]},u=Object.assign(Object.assign({},this._requestOptions),n),m=st(this._apiKey,this.model,d,u);return this._sendPromise=this._sendPromise.then((()=>m)).catch((e=>{throw new Error(pt)})).then((e=>e.response)).then((e=>{if(mt(e)){this._history.push(l);const t=Object.assign({},e.candidates[0].content);t.role||(t.role="model"),this._history.push(t)}else{const n=We(e);n&&t("warn","at node_modules/@google/generative-ai/dist/index.mjs:1257",`sendMessageStream() was unsuccessful. ${n}. Inspect response object for details.`)}})).catch((e=>{e.message!==pt&&t("error","at node_modules/@google/generative-ai/dist/index.mjs:1268",e)})),m}}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */
class ft{constructor(e,t,n={}){this.apiKey=e,this._requestOptions=n,t.model.includes("/")?this.model=t.model:this.model=`models/${t.model}`,this.generationConfig=t.generationConfig||{},this.safetySettings=t.safetySettings||[],this.tools=t.tools,this.toolConfig=t.toolConfig,this.systemInstruction=rt(t.systemInstruction),this.cachedContent=t.cachedContent}async generateContent(e,t={}){var n;const o=lt(e),a=Object.assign(Object.assign({},this._requestOptions),t);return it(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(n=this.cachedContent)||void 0===n?void 0:n.name},o),a)}async generateContentStream(e,t={}){var n;const o=lt(e),a=Object.assign(Object.assign({},this._requestOptions),t);return st(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(n=this.cachedContent)||void 0===n?void 0:n.name},o),a)}startChat(e){var t;return new gt(this.apiKey,this.model,Object.assign({generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:null===(t=this.cachedContent)||void 0===t?void 0:t.name},e),this._requestOptions)}async countTokens(e,t={}){const n=function(e,t){var n;let o={model:null==t?void 0:t.model,generationConfig:null==t?void 0:t.generationConfig,safetySettings:null==t?void 0:t.safetySettings,tools:null==t?void 0:t.tools,toolConfig:null==t?void 0:t.toolConfig,systemInstruction:null==t?void 0:t.systemInstruction,cachedContent:null===(n=null==t?void 0:t.cachedContent)||void 0===n?void 0:n.name,contents:[]};const a=null!=e.generateContentRequest;if(e.contents){if(a)throw new Be("CountTokensRequest must have one of contents or generateContentRequest, not both.");o.contents=e.contents}else if(a)o=Object.assign(Object.assign({},o),e.generateContentRequest);else{const t=ct(e);o.contents=[t]}return{generateContentRequest:o}}(e,{model:this.model,generationConfig:this.generationConfig,safetySettings:this.safetySettings,tools:this.tools,toolConfig:this.toolConfig,systemInstruction:this.systemInstruction,cachedContent:this.cachedContent}),o=Object.assign(Object.assign({},this._requestOptions),t);return async function(e,t,n,o){return(await qe(t,Ue.COUNT_TOKENS,e,!1,JSON.stringify(n),o)).json()}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */(this.apiKey,this.model,n,o)}async embedContent(e,t={}){const n=function(e){if("string"==typeof e||Array.isArray(e))return{content:ct(e)};return e}(e),o=Object.assign(Object.assign({},this._requestOptions),t);return async function(e,t,n,o){return(await qe(t,Ue.EMBED_CONTENT,e,!1,JSON.stringify(n),o)).json()}(this.apiKey,this.model,n,o)}async batchEmbedContents(e,t={}){const n=Object.assign(Object.assign({},this._requestOptions),t);return async function(e,t,n,o){const a=n.requests.map((e=>Object.assign(Object.assign({},e),{model:t})));return(await qe(t,Ue.BATCH_EMBED_CONTENTS,e,!1,JSON.stringify({requests:a}),o)).json()}(this.apiKey,this.model,e,n)}}
/**
   * @license
   * Copyright 2024 Google LLC
   *
   * Licensed under the Apache License, Version 2.0 (the "License");
   * you may not use this file except in compliance with the License.
   * You may obtain a copy of the License at
   *
   *   http://www.apache.org/licenses/LICENSE-2.0
   *
   * Unless required by applicable law or agreed to in writing, software
   * distributed under the License is distributed on an "AS IS" BASIS,
   * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
   * See the License for the specific language governing permissions and
   * limitations under the License.
   */class vt{constructor(e){this.apiKey=e}getGenerativeModel(e,t){if(!e.model)throw new De("Must provide a model name. Example: genai.getGenerativeModel({ model: 'my-model-name' })");return new ft(this.apiKey,e,t)}getGenerativeModelFromCachedContent(e,t,n){if(!e.name)throw new Be("Cached content must contain a `name` field.");if(!e.model)throw new Be("Cached content must contain a `model` field.");const o=["model","systemInstruction"];for(const s of o)if((null==t?void 0:t[s])&&e[s]&&(null==t?void 0:t[s])!==e[s]){if("model"===s){if((t.model.startsWith("models/")?t.model.replace("models/",""):t.model)===(e.model.startsWith("models/")?e.model.replace("models/",""):e.model))continue}throw new Be(`Different value for "${s}" specified in modelParams (${t[s]}) and cachedContent (${e[s]})`)}const a=Object.assign(Object.assign({},t),{model:e.model,tools:e.tools,toolConfig:e.toolConfig,systemInstruction:e.systemInstruction,cachedContent:e});return new ft(this.apiKey,a,n)}}const ht=ue("gemini",(()=>{const n=e.ref(null),o=e.ref(!1),a=e.ref("disconnected"),s=e.ref(null),i=e.ref(0),r=e.ref(3),c=e.ref("idle"),l=e.ref(""),d=e.ref("技术面试"),u=e.ref([]),m=e.ref([]),p=e.ref(!1),g=e.ref(!1),f=e.ref(null),v=e.ref(null),h={format:"PCM_16",channels:1,sendSampleRate:16e3,receiveSampleRate:24e3,chunkSize:1024},y={model:"models/gemini-2.0-flash-live-001",config:{response_modalities:["AUDIO"]}},E=e.computed((()=>o.value&&n.value)),w=async()=>{var e,s,r;if(!n.value||!o.value){if(!l.value)throw new Error("临时令牌不存在");a.value="connecting";try{const d=new vt(l.value),u=await(null==(s=null==(e=d.aio)?void 0:e.live)?void 0:s.connect(y.model,y.config))||await(null==(r=d.live)?void 0:r.connect(y.model,y.config));if(!u)throw new Error("Live API连接方法不可用，请检查SDK版本");n.value=u,o.value=!0,a.value="connected",i.value=0,c.value="idle",_(),t("log","at stores/gemini.js:105","Gemini Live API连接成功")}catch(d){throw t("error","at stores/gemini.js:107","Gemini Live API连接失败:",d),t("error","at stores/gemini.js:108","错误详情:",d.message),a.value="error",I(),d}}},_=async()=>{if(n.value)try{for(;o.value&&n.value;){const e=n.value.receive();for await(const n of e)n.data&&(u.value.push(n.data),V(n.data)),n.text&&(t("log","at stores/gemini.js:132","AI回复文本:",n.text),s.value={text:n.text});for(;u.value.length>0;)u.value.shift()}}catch(e){t("error","at stores/gemini.js:143","音频接收任务失败:",e)}},N=e=>{try{uni.createSpeechSynthesisUtterance&&uni.createSpeechSynthesisUtterance({text:e,lang:"zh-CN",rate:1,pitch:1,volume:1,success:()=>{t("log","at stores/gemini.js:200","语音播放成功"),c.value="active"},fail:e=>{t("error","at stores/gemini.js:204","语音播放失败:",e),c.value="active"}})}catch(n){t("error","at stores/gemini.js:209","语音合成失败:",n),c.value="active"}},b=async()=>{if(p.value||!o.value)return!1;try{f.value=uni.getRecorderManager();const e={duration:6e5,sampleRate:h.sendSampleRate,numberOfChannels:h.channels,encodeBitRate:48e3,format:"PCM",frameSize:h.chunkSize};return f.value.onStart((()=>{p.value=!0,t("log","at stores/gemini.js:232","开始录音")})),f.value.onFrameRecorded((e=>{S(e.frameBuffer)})),f.value.onError((e=>{t("error","at stores/gemini.js:241","录音错误:",e),p.value=!1})),f.value.start(e),!0}catch(e){return t("error","at stores/gemini.js:248","开始录音失败:",e),!1}},C=()=>{f.value&&p.value&&(f.value.stop(),p.value=!1,t("log","at stores/gemini.js:258","停止录音"))},S=async e=>{if(!E.value)return!1;try{const t={data:e,mime_type:"audio/pcm"};return await n.value.send({input:t}),!0}catch(o){return t("error","at stores/gemini.js:276","发送音频帧失败:",o),!1}},V=async e=>{try{v.value||(v.value=uni.createInnerAudioContext(),v.value.onEnded((()=>{g.value=!1})),v.value.onError((e=>{t("error","at stores/gemini.js:335","音频播放失败:",e),g.value=!1})));const n=`${uni.env.USER_DATA_PATH}/temp_received_audio_${Date.now()}.pcm`,o=uni.arrayBufferToBase64(e);uni.getFileSystemManager().writeFile({filePath:n,data:o,encoding:"base64",success:()=>{v.value.src=n,v.value.play(),g.value=!0,v.value.onEnded((()=>{g.value=!1,uni.getFileSystemManager().unlink({filePath:n,success:()=>t("log","at stores/gemini.js:360","临时接收音频文件已删除"),fail:e=>t("warn","at stores/gemini.js:361","删除临时接收音频文件失败:",e)})}))},fail:e=>{t("error","at stores/gemini.js:366","写入接收音频文件失败:",e),g.value=!1}})}catch(n){t("error","at stores/gemini.js:372","播放接收音频失败:",n),g.value=!1}},k=()=>{v.value&&g.value&&(v.value.stop(),g.value=!1,t("log","at stores/gemini.js:382","停止播放"))},I=()=>{if(i.value>=r.value)return void t("log","at stores/gemini.js:459","达到最大重连次数，停止重连");const e=Math.min(1e3*Math.pow(2,i.value),1e4);setTimeout((()=>{i.value++,w().catch((e=>{t("error","at stores/gemini.js:468","重连失败:",e)}))}),e)};return{session:n,isConnected:o,connectionStatus:a,lastMessage:s,interviewStatus:c,ephemeralToken:l,currentDomain:d,audioInQueue:u,audioOutQueue:m,isRecording:p,isPlaying:g,AUDIO_CONFIG:h,LIVE_CONFIG:y,canSendMessage:E,setEphemeralToken:(e,t)=>{l.value=e,d.value=t},connect:w,disconnect:()=>{if(C(),k(),n.value){try{n.value.close&&n.value.close()}catch(e){t("warn","at stores/gemini.js:158","关闭Live API session失败:",e)}n.value=null}o.value=!1,a.value="disconnected",c.value="idle",l.value="",u.value=[],m.value=[]},startRecording:b,stopRecording:C,sendAudio:async e=>S(e),sendAudioFrame:S,sendText:async e=>{if(!E.value)return!1;try{const o=`你是一个专业的${d.value}面试官。请用自然、友好的语调回答以下问题或进行面试对话：${e}`;return(e=>{try{const t=e.response.text();s.value={text:t},t&&(N(t),c.value="speaking")}catch(n){t("error","at stores/gemini.js:185","处理响应失败:",n)}})(await n.value.generateContent(o)),!0}catch(o){return t("error","at stores/gemini.js:296","发送文本失败:",o),!1}},sendInterrupt:()=>{try{for(k(),uni.stopSpeechSynthesis&&uni.stopSpeechSynthesis();u.value.length>0;)u.value.shift();return c.value="active",t("log","at stores/gemini.js:316","发送打断信号成功"),!0}catch(e){return t("error","at stores/gemini.js:319","发送打断信号失败:",e),!1}},playReceivedAudio:V,stopPlaying:k,startInterview:async()=>{try{return c.value="active",await b(),t("log","at stores/gemini.js:431","Gemini Store: 面试已开始（实时模式）"),!0}catch(e){return t("error","at stores/gemini.js:434","开始面试失败:",e),c.value="idle",!1}},endInterview:()=>{try{return C(),k(),c.value="idle",t("log","at stores/gemini.js:448","Gemini Store: 面试已结束"),!0}catch(e){return t("error","at stores/gemini.js:451","结束面试失败:",e),!1}},playAudio:e=>{try{const n=`${uni.env.USER_DATA_PATH}/temp_audio_${Date.now()}.wav`;uni.getFileSystemManager().writeFile({filePath:n,data:e,encoding:"base64",success:()=>{const e=uni.createInnerAudioContext();e.src=n,e.play(),e.onEnded((()=>{uni.getFileSystemManager().unlink({filePath:n,success:()=>t("log","at stores/gemini.js:403","临时音频文件已删除"),fail:e=>t("warn","at stores/gemini.js:404","删除临时音频文件失败:",e)}),e.destroy()})),e.onError((n=>{t("error","at stores/gemini.js:410","音频播放失败:",n),e.destroy()}))},fail:e=>{t("error","at stores/gemini.js:415","写入音频文件失败:",e)}})}catch(n){t("error","at stores/gemini.js:419","播放音频异常:",n)}}}})),yt=ue("user",(()=>{const n=e.ref(""),o=e.ref({id:null,phone:"",nickname:"",balance_count:0,balance_duration:0,free_trial_count:3,ab_test_group:""}),a=e.computed((()=>!!n.value)),s=e=>{n.value=e,uni.setStorageSync("token",e)},i=e=>{o.value={...o.value,...e},uni.setStorageSync("userInfo",o.value)},r=e.ref(!1),c=e.ref(!1),l=async(e="技术面试")=>{if(!n.value)return void t("log","at stores/user.js:153","用户未登录，无法获取临时令牌");if(c.value)return void t("log","at stores/user.js:159","正在获取临时令牌，跳过重复请求");const o=ht();if(o.ephemeralToken&&o.currentDomain===e)t("log","at stores/user.js:166","已有有效的临时令牌，无需重新获取");else{c.value=!0;try{t("log","at stores/user.js:173","开始获取临时令牌...");const a=await uni.request({url:`${me}/api/v1/gemini/ephemeral-token`,method:"POST",header:{Authorization:`Bearer ${n.value}`,"Content-Type":"application/json"},data:{domain:e,expire_minutes:30,uses:1}});if(200!==a.statusCode||200!==a.data.code)throw t("error","at stores/user.js:193","获取临时令牌失败，响应:",a.data),new Error(a.data.message||"获取临时令牌失败");o.setEphemeralToken(a.data.data.token,e),t("log","at stores/user.js:191","成功获取并设置临时令牌")}catch(a){t("error","at stores/user.js:197","获取临时令牌失败:",a),"success"!==a.message&&uni.showToast({title:"获取临时令牌失败",icon:"none"})}finally{c.value=!1}}};return{token:n,userInfo:o,isGettingToken:c,isInitialized:r,isLoggedIn:a,setToken:s,setUserInfo:i,login:async(e,n)=>{try{const t=await uni.request({url:`${me}/api/v1/auth/login`,method:"POST",data:{phone:e,code:n}});return 200===t.data.code?(s(t.data.data.token),i(t.data.data.user),await l(),{success:!0}):{success:!1,message:t.data.message}}catch(o){return t("error","at stores/user.js:56","登录失败:",o),{success:!1,message:"网络错误"}}},getUserInfo:async()=>{if(n.value)try{const e=await uni.request({url:`${me}/api/v1/user/info`,method:"GET",header:{Authorization:`Bearer ${n.value}`}});200===e.data.code&&i(e.data.data)}catch(e){t("error","at stores/user.js:77","获取用户信息失败:",e)}},logout:()=>{n.value="",o.value={id:null,phone:"",nickname:"",balance_count:0,balance_duration:0,free_trial_count:3,ab_test_group:""},uni.removeStorageSync("token"),uni.removeStorageSync("userInfo")},sendSmsCode:async e=>{try{const t=await uni.request({url:`${me}/api/v1/auth/sms`,method:"POST",data:{phone:e,type:2}});return 200===t.data.code?{success:!0}:{success:!1,message:t.data.message}}catch(n){return t("error","at stores/user.js:113","发送验证码失败:",n),{success:!1,message:"网络错误"}}},initFromStorage:async()=>{if(r.value)return void t("log","at stores/user.js:124","用户store已初始化，跳过重复初始化");t("log","at stores/user.js:128","开始初始化用户store...");const e=uni.getStorageSync("token"),a=uni.getStorageSync("userInfo");e&&(n.value=e,t("log","at stores/user.js:134","恢复用户token，准备获取临时令牌"),await l()),a&&(o.value={...o.value,...a},t("log","at stores/user.js:141","恢复用户信息")),r.value=!0,t("log","at stores/user.js:145","用户store初始化完成")},fetchAndSetEphemeralToken:l}})),Et=(e,t)=>{const n=e.__vccOpts||e;for(const[o,a]of t)n[o]=a;return n};const wt=Et({__name:"index",setup(n,{expose:o}){o();const a=yt(),s=e.ref({});e.onMounted((()=>{i()}));const i=async()=>{try{await a.getUserInfo(),s.value=a.userInfo}catch(e){t("error","at pages/index/index.vue:65","获取用户信息失败:",e)}},r=()=>{a.isLoggedIn?uni.navigateTo({url:"/pages/payment/payment"}):uni.navigateTo({url:"/pages/login/login"})},c={userStore:a,userInfo:s,loadUserInfo:i,startInterview:()=>{a.isLoggedIn?s.value.balance_count<=0&&s.value.free_trial_count<=0?uni.showModal({title:"提示",content:"您的使用次数已用完，请购买套餐",success:e=>{e.confirm&&r()}}):uni.navigateTo({url:"/pages/interview/interview"}):uni.navigateTo({url:"/pages/login/login"})},goBuy:r,ref:e.ref,onMounted:e.onMounted,get useUserStore(){return yt}};return Object.defineProperty(c,"__isScriptSetup",{enumerable:!1,value:!0}),c}},[["render",function(t,n,o,a,s,i){return e.openBlock(),e.createElementBlock("view",{class:"container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"面试助手"),e.createElementVNode("text",{class:"subtitle"},"AI助力，面试无忧")]),e.createElementVNode("view",{class:"stats-card"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(a.userInfo.balance_count||0),1),e.createElementVNode("text",{class:"stat-label"},"剩余次数")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(a.userInfo.free_trial_count||3),1),e.createElementVNode("text",{class:"stat-label"},"免费试用")])]),e.createElementVNode("view",{class:"action-buttons"},[e.createElementVNode("button",{class:"start-btn",onClick:a.startInterview},[e.createElementVNode("text",{class:"btn-text"},"开始面试")]),e.createElementVNode("button",{class:"buy-btn",onClick:a.goBuy},[e.createElementVNode("text",{class:"btn-text"},"购买套餐")])]),e.createElementVNode("view",{class:"features"},[e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"🎯"),e.createElementVNode("text",{class:"feature-title"},"实时回答"),e.createElementVNode("text",{class:"feature-desc"},"AI实时生成专业回答")]),e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"🔊"),e.createElementVNode("text",{class:"feature-title"},"语音播报"),e.createElementVNode("text",{class:"feature-desc"},"听筒私密播放答案")]),e.createElementVNode("view",{class:"feature-item"},[e.createElementVNode("text",{class:"feature-icon"},"📚"),e.createElementVNode("text",{class:"feature-title"},"领域定制"),e.createElementVNode("text",{class:"feature-desc"},"针对不同技术领域优化")])])])}],["__scopeId","data-v-1cf27b2a"],["__file","/Users/<USER>/code/interviewMaster/app/pages/index/index.vue"]]);const _t=Et({__name:"login",setup(t,{expose:n}){n();const o=yt(),a=e.ref({phone:"",code:""}),s=e.ref("login"),i=e.ref(!1),r=e.ref(0),c=e.ref(!1),l=e.computed((()=>r.value>0?`${r.value}s后重发`:"获取验证码")),d=e.computed((()=>11===a.value.phone.length&&6===a.value.code.length&&c.value)),u=()=>{i.value=!0,r.value=60;const e=setInterval((()=>{r.value--,r.value<=0&&(clearInterval(e),i.value=!1)}),1e3)};e.onMounted((()=>{o.isLoggedIn&&uni.switchTab({url:"/pages/index/index"})}));const m={userStore:o,form:a,currentTab:s,codeDisabled:i,codeCountdown:r,agreed:c,codeText:l,canSubmit:d,sendCode:async()=>{if(a.value.phone)if(/^1[3-9]\d{9}$/.test(a.value.phone))try{const e=await o.sendSmsCode(a.value.phone,2);e.success?(uni.showToast({title:"验证码已发送",icon:"success"}),u()):uni.showToast({title:e.message||"发送失败",icon:"none"})}catch(e){uni.showToast({title:"网络错误",icon:"none"})}else uni.showToast({title:"手机号格式不正确",icon:"none"});else uni.showToast({title:"请输入手机号",icon:"none"})},startCountdown:u,handleSubmit:async()=>{if(d.value){uni.showLoading({title:"登录中..."});try{const e=await o.login(a.value.phone,a.value.code);uni.hideLoading(),e.success?(uni.showToast({title:"登录成功",icon:"success"}),setTimeout((()=>{uni.switchTab({url:"/pages/index/index"})}),1500)):uni.showToast({title:e.message||"操作失败",icon:"none"})}catch(e){uni.hideLoading(),uni.showToast({title:"网络错误",icon:"none"})}}},onAgreementChange:e=>{c.value=e.detail.value.includes("agree")},showAgreement:()=>{uni.showModal({title:"用户协议",content:"这里是用户协议内容...",showCancel:!1})},showPrivacy:()=>{uni.showModal({title:"隐私政策",content:"这里是隐私政策内容...",showCancel:!1})},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return yt}};return Object.defineProperty(m,"__isScriptSetup",{enumerable:!1,value:!0}),m}},[["render",function(t,n,o,a,s,i){return e.openBlock(),e.createElementBlock("view",{class:"login-container"},[e.createElementVNode("view",{class:"header"},[e.createElementVNode("text",{class:"title"},"面试助手"),e.createElementVNode("text",{class:"subtitle"},"AI助力，面试无忧")]),e.createElementVNode("view",{class:"form-container"},[e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"手机号"),e.withDirectives(e.createElementVNode("input",{class:"input",type:"number",placeholder:"请输入手机号","onUpdate:modelValue":n[0]||(n[0]=e=>a.form.phone=e),maxlength:"11"},null,512),[[e.vModelText,a.form.phone]])]),e.createElementVNode("view",{class:"input-group"},[e.createElementVNode("text",{class:"label"},"验证码"),e.createElementVNode("view",{class:"code-input-wrapper"},[e.withDirectives(e.createElementVNode("input",{class:"input code-input",type:"number",placeholder:"请输入验证码","onUpdate:modelValue":n[1]||(n[1]=e=>a.form.code=e),maxlength:"6"},null,512),[[e.vModelText,a.form.code]]),e.createElementVNode("button",{class:"code-btn",disabled:a.codeDisabled,onClick:a.sendCode},e.toDisplayString(a.codeText),9,["disabled"])])]),e.createElementVNode("button",{class:"submit-btn",disabled:!a.canSubmit,onClick:a.handleSubmit}," 登录 / 注册 ",8,["disabled"]),e.createElementVNode("view",{class:"agreement"},[e.createElementVNode("checkbox-group",{onChange:a.onAgreementChange},[e.createElementVNode("label",{class:"checkbox-label"},[e.createElementVNode("checkbox",{value:"agree",checked:a.agreed},null,8,["checked"]),e.createElementVNode("text",{class:"agreement-text"},[e.createTextVNode(" 我已阅读并同意 "),e.createElementVNode("text",{class:"link",onClick:a.showAgreement},"《用户协议》"),e.createTextVNode(" 和 "),e.createElementVNode("text",{class:"link",onClick:a.showPrivacy},"《隐私政策》")])])],32)])])])}],["__scopeId","data-v-e4e4508d"],["__file","/Users/<USER>/code/interviewMaster/app/pages/login/login.vue"]]);const Nt=Et({__name:"interview",setup(n,{expose:o}){o();const a=yt(),s=ht(),i=e.ref(""),r=e.ref("idle"),c=e.ref(!1),l=e.ref(!1),d=e.ref(1),u=e.ref([]),m=e.ref(!1),p=e.ref(!1),g=uni.getRecorderManager(),f=uni.createInnerAudioContext(),v=e.ref([]),h=e.computed((()=>({idle:"空闲",connecting:"连接中...",active:"面试进行中",speaking:"正在播报...",ended:"面试已结束",error:"连接错误"}[r.value]||"未知状态"))),y=e.computed((()=>`status-${r.value}`)),E=e.computed((()=>{switch(r.value){case"connecting":return"⏳";case"active":return"🎙️";case"speaking":return"🗣";case"ended":return"✅";default:return"🚀"}})),w=e.computed((()=>{switch(r.value){case"connecting":return"连接中...";case"active":return"面试进行中";case"speaking":return"正在回答...";case"ended":return"面试已结束";default:return"立即开始"}})),_=e.computed((()=>"idle"===r.value||!s.isConnected)),N=()=>{if(!s.isConnected)return r.value="connecting",void s.connect().then((()=>{_.value&&b()})).catch((e=>{t("error","at pages/interview/interview.vue:233","连接失败，无法开始面试",e),r.value="idle",uni.showToast({title:"连接失败，请重试",icon:"error"})}));_.value&&b()},b=()=>{p.value=!0,s.startInterview(),r.value="active",g.start({duration:6e5,sampleRate:16e3,numberOfChannels:1,encodeBitRate:48e3,format:"wav",frameSize:1024})},C=()=>{p.value=!1,s.endInterview(),g.stop(),r.value="idle"},S=()=>{l.value=!1,uni.setStorageSync("guide_shown",!0)};e.onMounted((async()=>{if(!a.isLoggedIn)return void uni.redirectTo({url:"/pages/login/login"});uni.getStorageSync("guide_shown")||(l.value=!0),s.$subscribe(((e,t)=>{"gemini"===e.storeId&&(r.value=t.interviewStatus)})),g.onStart((()=>{t("log","at pages/interview/interview.vue:363","面试录音开始（实时模式），等待音频帧...")})),g.onFrameRecorded&&g.onFrameRecorded((e=>{if(p.value&&!m.value&&s.isConnected){const o=e.frameBuffer;if(o&&o.byteLength>0){t("log","at pages/interview/interview.vue:372","收到音频帧，大小:",o.byteLength);try{s.sendAudio(o)}catch(n){t("error","at pages/interview/interview.vue:377","实时音频数据发送失败:",n)}}}})),g.onStop((e=>{t("log","at pages/interview/interview.vue:384","面试录音结束（实时模式），音频流已在实时处理中")})),g.onError((e=>{t("error","at pages/interview/interview.vue:390","面试录音错误",e),p.value=!1,r.value="idle",e.errMsg.includes("auth")||e.errMsg.includes("permission")?uni.showModal({title:"权限申请",content:"需要录音权限才能使用面试功能，请在设置中开启。",success:e=>{e.confirm&&uni.openSetting()}}):uni.showToast({title:"录音失败，请稍后重试",icon:"none"})})),f.onPlay((()=>{t("log","at pages/interview/interview.vue:415","音频播放开始")})),f.onEnded((()=>{t("log","at pages/interview/interview.vue:419","音频播放结束"),r.value=p.value?"active":"idle"})),f.onError((e=>{t("error","at pages/interview/interview.vue:424","音频播放错误",e),r.value=p.value?"active":"idle"}))})),e.onUnmounted((()=>{p.value&&C(),g.stop(),f.destroy(),s.disconnect()}));const V={userStore:a,geminiStore:s,sessionId:i,interviewStatus:r,showHistory:c,showGuide:l,guideStep:d,recentHistory:u,isMuted:m,isInterviewActive:p,recorderManager:g,innerAudioContext:f,audioChunks:v,statusText:h,statusClass:y,buttonIcon:E,buttonText:w,canStartInterview:_,toggleInterview:()=>{p.value?C():N()},startInterview:N,startRecording:b,endInterview:C,toggleMute:()=>{m.value=!m.value,t("log","at pages/interview/interview.vue:277",m.value?"已静音":"取消静音")},interruptAnswer:()=>{s.sendInterrupt(),f.stop()},endInterviewConfirm:()=>{uni.showModal({title:"确认",content:"确定要结束本次面试吗？",success:e=>{e.confirm&&(p.value&&C(),s.disconnect(),uni.navigateBack())}})},toggleHistory:()=>{c.value=!c.value},giveFeedback:(e,t)=>{const n=u.value.find((t=>t.id===e));n&&(n.feedback=n.feedback===t?0:t)},formatTime:e=>{const t=new Date(e);return`${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`},nextGuide:()=>{d.value<3?d.value++:S()},closeGuide:S,ref:e.ref,computed:e.computed,onMounted:e.onMounted,onUnmounted:e.onUnmounted,get useUserStore(){return yt},get useGeminiStore(){return ht}};return Object.defineProperty(V,"__isScriptSetup",{enumerable:!1,value:!0}),V}},[["render",function(t,n,o,a,s,i){return e.openBlock(),e.createElementBlock("view",{class:"interview-container"},[e.createCommentVNode(" 状态栏 "),e.createElementVNode("view",{class:"status-bar"},[e.createElementVNode("view",{class:"status-info"},[e.createElementVNode("text",{class:"status-text"},e.toDisplayString(a.statusText),1),e.createElementVNode("view",{class:e.normalizeClass(["status-indicator",a.statusClass])},null,2)]),e.createElementVNode("view",{class:"session-info"},[e.createElementVNode("text",{class:"session-text"},"会话: "+e.toDisplayString(a.sessionId||"未连接"),1)])]),e.createCommentVNode(" 主控制区域 "),e.createElementVNode("view",{class:"control-area"},[e.createCommentVNode(" 中心按钮 "),e.createElementVNode("view",{class:"center-button-container"},[e.createElementVNode("button",{class:e.normalizeClass(["center-button",{active:a.isInterviewActive,disabled:!a.canStartInterview,connecting:"connecting"===a.interviewStatus}]),onClick:a.toggleInterview},[e.createElementVNode("view",{class:"button-icon"},[e.createElementVNode("text",{class:"icon"},e.toDisplayString(a.buttonIcon),1)]),e.createElementVNode("text",{class:"button-text"},e.toDisplayString(a.buttonText),1)],2),e.createCommentVNode(" 实时交互动画 "),a.isInterviewActive?(e.openBlock(),e.createElementBlock("view",{key:0,class:"realtime-animation"},[e.createElementVNode("view",{class:"wave wave1"}),e.createElementVNode("view",{class:"wave wave2"}),e.createElementVNode("view",{class:"wave wave3"})])):e.createCommentVNode("v-if",!0)]),e.createCommentVNode(" 控制按钮组 "),e.createElementVNode("view",{class:"control-buttons"},[a.isInterviewActive&&"speaking"===a.interviewStatus?(e.openBlock(),e.createElementBlock("button",{key:0,class:"control-btn interrupt-btn",onClick:a.interruptAnswer},[e.createElementVNode("text",{class:"btn-icon"},"⏸"),e.createElementVNode("text",{class:"btn-text"},"打断")])):e.createCommentVNode("v-if",!0),a.isInterviewActive?(e.openBlock(),e.createElementBlock("button",{key:1,class:e.normalizeClass(["control-btn mute-btn",{active:a.isMuted}]),onClick:a.toggleMute},[e.createElementVNode("text",{class:"btn-icon"},e.toDisplayString(a.isMuted?"🔇":"🔊"),1),e.createElementVNode("text",{class:"btn-text"},e.toDisplayString(a.isMuted?"取消静音":"静音"),1)],2)):e.createCommentVNode("v-if",!0),e.createElementVNode("button",{class:"control-btn end-btn",onClick:a.endInterviewConfirm},[e.createElementVNode("text",{class:"btn-icon"},"⏹"),e.createElementVNode("text",{class:"btn-text"},"结束")])])]),e.createCommentVNode(" 历史记录 "),e.createElementVNode("view",{class:"history-section"},[e.createElementVNode("view",{class:"history-header",onClick:a.toggleHistory},[e.createElementVNode("text",{class:"history-title"},"历史记录"),e.createElementVNode("text",{class:"history-toggle"},e.toDisplayString(a.showHistory?"收起":"展开"),1)]),a.showHistory?(e.openBlock(),e.createElementBlock("view",{key:0,class:"history-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.recentHistory,((t,n)=>(e.openBlock(),e.createElementBlock("view",{key:n,class:"history-item"},[e.createElementVNode("view",{class:"question"},[e.createElementVNode("text",{class:"label"},"问题:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.question),1)]),e.createElementVNode("view",{class:"answer"},[e.createElementVNode("text",{class:"label"},"回答:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.answer),1)]),e.createElementVNode("view",{class:"meta"},[e.createElementVNode("text",{class:"time"},e.toDisplayString(a.formatTime(t.time)),1),e.createElementVNode("view",{class:"feedback"},[e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:1===t.feedback}]),onClick:e=>a.giveFeedback(t.id,1)}," 👍 ",10,["onClick"]),e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:-1===t.feedback}]),onClick:e=>a.giveFeedback(t.id,-1)}," 👎 ",10,["onClick"])])])])))),128))])):e.createCommentVNode("v-if",!0)]),e.createCommentVNode(" 新手引导 "),a.showGuide?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guide-overlay",onClick:a.closeGuide},[e.createElementVNode("view",{class:"guide-content",onClick:n[0]||(n[0]=e.withModifiers((()=>{}),["stop"]))},[1===a.guideStep?(e.openBlock(),e.createElementBlock("view",{key:0,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"欢迎使用面试助手"),e.createElementVNode("text",{class:"guide-text"},"按住中心按钮说话，AI会实时为您提供专业的回答建议"),e.createElementVNode("button",{class:"guide-btn",onClick:a.nextGuide},"下一步")])):e.createCommentVNode("v-if",!0),2===a.guideStep?(e.openBlock(),e.createElementBlock("view",{key:1,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"听筒播放"),e.createElementVNode("text",{class:"guide-text"},"答案会通过听筒播放，请将手机靠近耳朵以获得最佳体验"),e.createElementVNode("button",{class:"guide-btn",onClick:a.nextGuide},"下一步")])):e.createCommentVNode("v-if",!0),3===a.guideStep?(e.openBlock(),e.createElementBlock("view",{key:2,class:"guide-step"},[e.createElementVNode("text",{class:"guide-title"},"打断功能"),e.createElementVNode("text",{class:"guide-text"},'在AI回答时，您可以点击"打断"按钮来停止当前回答'),e.createElementVNode("button",{class:"guide-btn",onClick:a.closeGuide},"开始使用")])):e.createCommentVNode("v-if",!0)])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-99492b0e"],["__file","/Users/<USER>/code/interviewMaster/app/pages/interview/interview.vue"]]);const bt=Et({__name:"history",setup(n,{expose:o}){o();const a=yt(),s=e.ref([]),i=e.ref({}),r=e.ref(!1),c=e.ref(!0),l=e.ref(1),d=e.ref(20),u=e.ref(""),m=e.ref(!1),p=e.ref(null),g=async(e=!1)=>{if(!r.value){r.value=!0;try{const t={page:e?1:l.value,page_size:d.value};u.value&&(t.date=u.value);const n={list:[{id:1,session_id:"session_1234567890",question_text:"请介绍一下JavaScript的闭包概念",answer_text:"闭包是JavaScript中的一个重要概念，它指的是函数能够访问其外部作用域中的变量，即使在函数外部调用时也是如此...",response_time_ms:1200,user_feedback:1,prompt_version:"A",created_at:(new Date).toISOString()}],total:1,page:1,page_size:20};e?(s.value=n.list,l.value=1):s.value.push(...n.list),c.value=s.value.length<n.total,l.value++}catch(n){t("error","at pages/history/history.vue:218","加载历史记录失败:",n),uni.showToast({title:"加载失败",icon:"none"})}finally{r.value=!1}}},f=async()=>{try{i.value={total_count:25,month_count:8,today_count:2,avg_response_time:1350}}catch(e){t("error","at pages/history/history.vue:242","加载统计信息失败:",e)}};e.onMounted((()=>{a.isLoggedIn?(g(!0),f()):uni.redirectTo({url:"/pages/login/login"})}));const v={userStore:a,historyList:s,stats:i,loading:r,hasMore:c,page:l,pageSize:d,filterDate:u,showDetailModal:m,selectedItem:p,loadHistory:g,loadStats:f,loadMore:()=>{c.value&&!r.value&&g(!1)},onDateChange:e=>{u.value=e.detail.value,g(!0)},clearFilter:()=>{u.value="",g(!0)},giveFeedback:async(e,n)=>{try{const t=s.value.find((t=>t.id===e));t&&(t.user_feedback=t.user_feedback===n?0:n),uni.showToast({title:"反馈成功",icon:"success"})}catch(o){t("error","at pages/history/history.vue:280","反馈失败:",o),uni.showToast({title:"反馈失败",icon:"none"})}},showDetail:e=>{p.value=e,m.value=!0},closeDetail:()=>{m.value=!1,p.value=null},startInterview:()=>{uni.navigateTo({url:"/pages/interview/interview"})},formatDateTime:e=>{if(!e)return"";const t=new Date(e);return`${t.getMonth()+1}/${t.getDate()} ${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`},formatResponseTime:e=>e?e<1e3?`${e}ms`:`${(e/1e3).toFixed(1)}s`:"0ms",truncateText:(e,t)=>e?e.length<=t?e:e.substring(0,t)+"...":"",ref:e.ref,onMounted:e.onMounted,get useUserStore(){return yt}};return Object.defineProperty(v,"__isScriptSetup",{enumerable:!1,value:!0}),v}},[["render",function(t,n,o,a,s,i){var r,c,l,d,u,m;return e.openBlock(),e.createElementBlock("view",{class:"history-container"},[e.createCommentVNode(" 筛选栏 "),e.createElementVNode("view",{class:"filter-bar"},[e.createElementVNode("picker",{mode:"date",value:a.filterDate,onChange:a.onDateChange,class:"date-picker"},[e.createElementVNode("view",{class:"picker-text"},e.toDisplayString(a.filterDate||"选择日期"),1)],40,["value"]),e.createElementVNode("button",{class:"clear-filter",onClick:a.clearFilter}," 清除筛选 ")]),e.createCommentVNode(" 统计信息 "),e.createElementVNode("view",{class:"stats-card"},[e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(a.stats.total_count||0),1),e.createElementVNode("text",{class:"stat-label"},"总次数")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(a.stats.month_count||0),1),e.createElementVNode("text",{class:"stat-label"},"本月")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(a.stats.today_count||0),1),e.createElementVNode("text",{class:"stat-label"},"今日")]),e.createElementVNode("view",{class:"stat-item"},[e.createElementVNode("text",{class:"stat-number"},e.toDisplayString(a.formatResponseTime(a.stats.avg_response_time)),1),e.createElementVNode("text",{class:"stat-label"},"平均响应")])]),e.createCommentVNode(" 历史记录列表 "),e.createElementVNode("view",{class:"history-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.historyList,((t,n)=>(e.openBlock(),e.createElementBlock("view",{key:t.id,class:"history-item",onClick:e=>a.showDetail(t)},[e.createElementVNode("view",{class:"item-header"},[e.createElementVNode("text",{class:"session-id"},"会话 "+e.toDisplayString(t.session_id.slice(-8)),1),e.createElementVNode("text",{class:"time"},e.toDisplayString(a.formatDateTime(t.created_at)),1)]),e.createElementVNode("view",{class:"question-section"},[e.createElementVNode("text",{class:"label"},"问题:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(t.question_text||"语音问题"),1)]),e.createElementVNode("view",{class:"answer-section"},[e.createElementVNode("text",{class:"label"},"回答:"),e.createElementVNode("text",{class:"content"},e.toDisplayString(a.truncateText(t.answer_text,100)),1)]),e.createElementVNode("view",{class:"item-footer"},[e.createElementVNode("view",{class:"meta-info"},[e.createElementVNode("text",{class:"response-time"},"响应: "+e.toDisplayString(t.response_time_ms)+"ms",1),e.createElementVNode("text",{class:"prompt-version"},"版本: "+e.toDisplayString(t.prompt_version),1)]),e.createElementVNode("view",{class:"feedback-buttons"},[e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:1===t.user_feedback}]),onClick:e.withModifiers((e=>a.giveFeedback(t.id,1)),["stop"])}," 👍 ",10,["onClick"]),e.createElementVNode("button",{class:e.normalizeClass(["feedback-btn",{active:-1===t.user_feedback}]),onClick:e.withModifiers((e=>a.giveFeedback(t.id,-1)),["stop"])}," 👎 ",10,["onClick"])])])],8,["onClick"])))),128)),e.createCommentVNode(" 加载更多 "),a.hasMore?(e.openBlock(),e.createElementBlock("view",{key:0,class:"load-more",onClick:a.loadMore},[e.createElementVNode("text",{class:"load-text"},e.toDisplayString(a.loading?"加载中...":"加载更多"),1)])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 空状态 "),a.loading||0!==a.historyList.length?e.createCommentVNode("v-if",!0):(e.openBlock(),e.createElementBlock("view",{key:1,class:"empty-state"},[e.createElementVNode("text",{class:"empty-icon"},"📝"),e.createElementVNode("text",{class:"empty-text"},"暂无面试记录"),e.createElementVNode("button",{class:"start-btn",onClick:a.startInterview}," 开始面试 ")]))]),e.createCommentVNode(" 详情弹窗 "),a.showDetailModal?(e.openBlock(),e.createElementBlock("view",{key:0,class:"detail-modal",onClick:a.closeDetail},[e.createElementVNode("view",{class:"detail-content",onClick:n[0]||(n[0]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"detail-header"},[e.createElementVNode("text",{class:"detail-title"},"面试记录详情"),e.createElementVNode("button",{class:"close-btn",onClick:a.closeDetail},"×")]),e.createElementVNode("view",{class:"detail-body"},[e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"会话ID:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(r=a.selectedItem)?void 0:r.session_id),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"时间:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(a.formatDateTime(null==(c=a.selectedItem)?void 0:c.created_at)),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"问题:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString((null==(l=a.selectedItem)?void 0:l.question_text)||"语音问题"),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"回答:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(d=a.selectedItem)?void 0:d.answer_text),1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"响应时间:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(u=a.selectedItem)?void 0:u.response_time_ms)+"ms",1)]),e.createElementVNode("view",{class:"detail-item"},[e.createElementVNode("text",{class:"detail-label"},"提示词版本:"),e.createElementVNode("text",{class:"detail-value"},e.toDisplayString(null==(m=a.selectedItem)?void 0:m.prompt_version),1)])])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-b2d018fa"],["__file","/Users/<USER>/code/interviewMaster/app/pages/history/history.vue"]]);const Ct=Et({__name:"profile",setup(n,{expose:o}){o();const a=yt(),s=e.computed((()=>a.userInfo)),i=e.ref(!1),r=e.ref({nickname:"",avatar:""}),c=()=>{i.value=!1};e.onMounted((async()=>{if(a.isLoggedIn)try{await a.getUserInfo()}catch(e){t("error","at pages/profile/profile.vue:278","获取用户信息失败:",e)}else uni.redirectTo({url:"/pages/login/login"})}));const l={userStore:a,userInfo:s,showEditModal:i,editForm:r,editProfile:()=>{r.value={nickname:s.value.nickname||"",avatar:s.value.avatar||""},i.value=!0},closeEdit:c,chooseAvatar:()=>{uni.chooseImage({count:1,sizeType:["compressed"],sourceType:["album","camera"],success:e=>{r.value.avatar=e.tempFilePaths[0]}})},saveProfile:async()=>{try{uni.showLoading({title:"保存中..."}),a.setUserInfo(r.value),uni.hideLoading(),uni.showToast({title:"保存成功",icon:"success"}),c()}catch(e){uni.hideLoading(),uni.showToast({title:"保存失败",icon:"none"})}},goRecharge:()=>{uni.navigateTo({url:"/pages/payment/payment"})},goHistory:()=>{uni.switchTab({url:"/pages/history/history"})},goOrders:()=>{uni.navigateTo({url:"/pages/orders/orders"})},goSettings:()=>{uni.navigateTo({url:"/pages/settings/settings"})},goHelp:()=>{uni.navigateTo({url:"/pages/help/help"})},goAbout:()=>{uni.navigateTo({url:"/pages/about/about"})},logout:()=>{uni.showModal({title:"确认",content:"确定要退出登录吗？",success:e=>{e.confirm&&(a.logout(),uni.reLaunch({url:"/pages/login/login"}))}})},formatDuration:e=>{if(!e)return"0分钟";const t=Math.floor(e/3600),n=Math.floor(e%3600/60);return t>0?`${t}小时${n}分钟`:`${n}分钟`},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return yt}};return Object.defineProperty(l,"__isScriptSetup",{enumerable:!1,value:!0}),l}},[["render",function(t,n,o,a,s,i){return e.openBlock(),e.createElementBlock("view",{class:"profile-container"},[e.createCommentVNode(" 用户信息卡片 "),e.createElementVNode("view",{class:"user-card"},[e.createElementVNode("view",{class:"avatar-section"},[e.createElementVNode("image",{class:"avatar",src:a.userInfo.avatar||"/static/default-avatar.png"},null,8,["src"]),e.createElementVNode("view",{class:"user-info"},[e.createElementVNode("text",{class:"nickname"},e.toDisplayString(a.userInfo.nickname||"面试者"),1),e.createElementVNode("text",{class:"phone"},e.toDisplayString(a.userInfo.phone),1)])]),e.createElementVNode("button",{class:"edit-btn",onClick:a.editProfile}," 编辑 ")]),e.createCommentVNode(" 余额信息 "),e.createElementVNode("view",{class:"balance-card"},[e.createElementVNode("view",{class:"balance-header"},[e.createElementVNode("text",{class:"balance-title"},"我的余额"),e.createElementVNode("button",{class:"recharge-btn",onClick:a.goRecharge}," 充值 ")]),e.createElementVNode("view",{class:"balance-info"},[e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(a.userInfo.balance_count||0),1),e.createElementVNode("text",{class:"balance-label"},"剩余次数")]),e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(a.userInfo.free_trial_count||0),1),e.createElementVNode("text",{class:"balance-label"},"免费试用")]),e.createElementVNode("view",{class:"balance-item"},[e.createElementVNode("text",{class:"balance-number"},e.toDisplayString(a.formatDuration(a.userInfo.balance_duration)),1),e.createElementVNode("text",{class:"balance-label"},"剩余时长")])])]),e.createCommentVNode(" 功能菜单 "),e.createElementVNode("view",{class:"menu-section"},[e.createElementVNode("view",{class:"menu-item",onClick:a.goHistory},[e.createElementVNode("view",{class:"menu-icon"},"📊"),e.createElementVNode("text",{class:"menu-text"},"面试历史"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:a.goOrders},[e.createElementVNode("view",{class:"menu-icon"},"📋"),e.createElementVNode("text",{class:"menu-text"},"我的订单"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:a.goSettings},[e.createElementVNode("view",{class:"menu-icon"},"⚙️"),e.createElementVNode("text",{class:"menu-text"},"设置"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:a.goHelp},[e.createElementVNode("view",{class:"menu-icon"},"❓"),e.createElementVNode("text",{class:"menu-text"},"帮助与反馈"),e.createElementVNode("text",{class:"menu-arrow"},">")]),e.createElementVNode("view",{class:"menu-item",onClick:a.goAbout},[e.createElementVNode("view",{class:"menu-icon"},"ℹ️"),e.createElementVNode("text",{class:"menu-text"},"关于我们"),e.createElementVNode("text",{class:"menu-arrow"},">")])]),e.createCommentVNode(" 退出登录 "),e.createElementVNode("view",{class:"logout-section"},[e.createElementVNode("button",{class:"logout-btn",onClick:a.logout}," 退出登录 ")]),e.createCommentVNode(" 编辑资料弹窗 "),a.showEditModal?(e.openBlock(),e.createElementBlock("view",{key:0,class:"edit-modal",onClick:a.closeEdit},[e.createElementVNode("view",{class:"edit-content",onClick:n[1]||(n[1]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"edit-header"},[e.createElementVNode("text",{class:"edit-title"},"编辑资料"),e.createElementVNode("button",{class:"close-btn",onClick:a.closeEdit},"×")]),e.createElementVNode("view",{class:"edit-body"},[e.createElementVNode("view",{class:"edit-item"},[e.createElementVNode("text",{class:"edit-label"},"昵称"),e.withDirectives(e.createElementVNode("input",{class:"edit-input","onUpdate:modelValue":n[0]||(n[0]=e=>a.editForm.nickname=e),placeholder:"请输入昵称",maxlength:"20"},null,512),[[e.vModelText,a.editForm.nickname]])]),e.createElementVNode("view",{class:"edit-item"},[e.createElementVNode("text",{class:"edit-label"},"头像"),e.createElementVNode("view",{class:"avatar-upload",onClick:a.chooseAvatar},[e.createElementVNode("image",{class:"upload-avatar",src:a.editForm.avatar||"/static/default-avatar.png"},null,8,["src"]),e.createElementVNode("text",{class:"upload-text"},"点击更换")])])]),e.createElementVNode("view",{class:"edit-footer"},[e.createElementVNode("button",{class:"cancel-btn",onClick:a.closeEdit},"取消"),e.createElementVNode("button",{class:"save-btn",onClick:a.saveProfile},"保存")])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-dd383ca2"],["__file","/Users/<USER>/code/interviewMaster/app/pages/profile/profile.vue"]]);const St=Et({__name:"payment",setup(n,{expose:o}){o();const a=yt(),s=e.ref([]),i=e.ref([]),r=e.ref(null),c=e.ref(""),l=e.ref(!1),d=e.ref(!1),u=e.ref(null),m=e.computed((()=>{const e=i.value.find((e=>e.type===c.value));return(null==e?void 0:e.name)||""})),p=async()=>{try{s.value=[{id:"trial_10",name:"新手体验包",description:"10次面试机会，适合初次体验",price:9.9,count:10,duration:0,type:1},{id:"standard_50",name:"标准套餐",description:"50次面试机会，适合求职准备",price:39.9,count:50,duration:0,type:1},{id:"premium_100",name:"高级套餐",description:"100次面试机会，适合长期使用",price:69.9,count:100,duration:0,type:1},{id:"monthly_unlimited",name:"包月畅享",description:"一个月内无限次使用",price:99.9,count:0,duration:2592e3,type:3}]}catch(e){t("error","at pages/payment/payment.vue:219","加载商品列表失败:",e),uni.showToast({title:"加载失败",icon:"none"})}},g=async()=>{try{i.value=[{type:"wechat",name:"微信支付",icon:"wechat",description:"使用微信扫码支付",enabled:!0},{type:"alipay",name:"支付宝",icon:"alipay",description:"使用支付宝扫码支付",enabled:!0}]}catch(e){t("error","at pages/payment/payment.vue:251","加载支付方式失败:",e)}},f=async()=>{try{return{success:!0,data:{order_no:"IM"+Date.now(),product_id:r.value.id,amount:r.value.price,status:0}}}catch(e){return{success:!1,message:"创建订单失败"}}},v=async e=>{try{return{success:!0,data:{code_url:"weixin://wxpay/bizpayurl?pr=mock_code"}}}catch(t){return{success:!1,message:"创建微信支付失败"}}},h=async e=>{try{return{success:!0,data:{qr_code:"https://qr.alipay.com/mock_code"}}}catch(t){return{success:!1,message:"创建支付宝支付失败"}}},y=()=>{const e=setInterval((async()=>{const t=await E();"paid"===t?(clearInterval(e),w()):"failed"===t&&(clearInterval(e),_())}),3e3);setTimeout((()=>{clearInterval(e)}),3e5)},E=async()=>{if(!u.value)return"pending";try{return Math.random()>.8?"paid":"pending"}catch(e){return t("error","at pages/payment/payment.vue:421","检查支付状态失败:",e),"pending"}},w=()=>{N(),uni.showToast({title:"支付成功",icon:"success"}),a.getUserInfo(),setTimeout((()=>{uni.navigateBack()}),2e3)},_=()=>{N(),uni.showToast({title:"支付失败",icon:"none"})},N=()=>{d.value=!1};e.onMounted((()=>{a.isLoggedIn?(p(),g()):uni.redirectTo({url:"/pages/login/login"})}));const b={userStore:a,products:s,paymentMethods:i,selectedProduct:r,selectedPayment:c,paying:l,showQRCode:d,currentOrder:u,paymentMethodName:m,loadProducts:p,loadPaymentMethods:g,selectProduct:e=>{r.value=e,i.value.length>0&&(c.value=i.value[0].type)},selectPayment:e=>{const t=i.value.find((t=>t.type===e));t&&t.enabled&&(c.value=e)},createPayment:async()=>{if(r.value&&c.value){l.value=!0;try{const e=await f();if(!e.success)throw new Error(e.message);let t;if(u.value=e.data,"wechat"===c.value?t=await v(u.value.order_no):"alipay"===c.value&&(t=await h(u.value.order_no)),!t.success)throw new Error(t.message);d.value=!0,y()}catch(e){t("error","at pages/payment/payment.vue:306","创建支付失败:",e),uni.showToast({title:e.message||"支付失败",icon:"none"})}finally{l.value=!1}}},createOrder:f,createWeChatPayment:v,createAlipayPayment:h,startPaymentPolling:y,checkPaymentStatus:E,handlePaymentSuccess:w,handlePaymentFailed:_,closeQRCode:N,formatDuration:e=>{if(!e)return"0分钟";const t=Math.floor(e/86400),n=Math.floor(e%86400/3600),o=Math.floor(e%3600/60);return t>0?`${t}天`:n>0?`${n}小时`:`${o}分钟`},ref:e.ref,computed:e.computed,onMounted:e.onMounted,get useUserStore(){return yt}};return Object.defineProperty(b,"__isScriptSetup",{enumerable:!1,value:!0}),b}},[["render",function(t,n,o,a,s,i){var r;return e.openBlock(),e.createElementBlock("view",{class:"payment-container"},[e.createCommentVNode(" 商品列表 "),e.createElementVNode("view",{class:"products-section"},[e.createElementVNode("text",{class:"section-title"},"选择套餐"),e.createElementVNode("view",{class:"products-list"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.products,(t=>{var n,o;return e.openBlock(),e.createElementBlock("view",{key:t.id,class:e.normalizeClass(["product-item",{selected:(null==(n=a.selectedProduct)?void 0:n.id)===t.id}]),onClick:e=>a.selectProduct(t)},[e.createElementVNode("view",{class:"product-header"},[e.createElementVNode("text",{class:"product-name"},e.toDisplayString(t.name),1),"trial_10"===t.id?(e.openBlock(),e.createElementBlock("view",{key:0,class:"product-badge"}," 推荐 ")):e.createCommentVNode("v-if",!0)]),e.createElementVNode("text",{class:"product-desc"},e.toDisplayString(t.description),1),e.createElementVNode("view",{class:"product-info"},[e.createElementVNode("view",{class:"product-details"},[t.count>0?(e.openBlock(),e.createElementBlock("text",{key:0,class:"detail-item"},e.toDisplayString(t.count)+"次面试机会 ",1)):e.createCommentVNode("v-if",!0),t.duration>0?(e.openBlock(),e.createElementBlock("text",{key:1,class:"detail-item"},e.toDisplayString(a.formatDuration(t.duration))+"使用时长 ",1)):e.createCommentVNode("v-if",!0)]),e.createElementVNode("view",{class:"product-price"},[e.createElementVNode("text",{class:"price-symbol"},"¥"),e.createElementVNode("text",{class:"price-amount"},e.toDisplayString(t.price),1)])]),(null==(o=a.selectedProduct)?void 0:o.id)===t.id?(e.openBlock(),e.createElementBlock("view",{key:0,class:"select-indicator"}," ✓ ")):e.createCommentVNode("v-if",!0)],10,["onClick"])})),128))])]),e.createCommentVNode(" 支付方式 "),a.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:0,class:"payment-methods-section"},[e.createElementVNode("text",{class:"section-title"},"支付方式"),e.createElementVNode("view",{class:"payment-methods"},[(e.openBlock(!0),e.createElementBlock(e.Fragment,null,e.renderList(a.paymentMethods,(t=>(e.openBlock(),e.createElementBlock("view",{key:t.type,class:e.normalizeClass(["payment-method",{selected:a.selectedPayment===t.type,disabled:!t.enabled}]),onClick:e=>a.selectPayment(t.type)},[e.createElementVNode("view",{class:"method-icon"},["wechat"===t.type?(e.openBlock(),e.createElementBlock("text",{key:0},"💚")):e.createCommentVNode("v-if",!0),"alipay"===t.type?(e.openBlock(),e.createElementBlock("text",{key:1},"💙")):e.createCommentVNode("v-if",!0)]),e.createElementVNode("view",{class:"method-info"},[e.createElementVNode("text",{class:"method-name"},e.toDisplayString(t.name),1),e.createElementVNode("text",{class:"method-desc"},e.toDisplayString(t.description),1)]),e.createElementVNode("view",{class:"method-radio"},[e.createElementVNode("view",{class:e.normalizeClass(["radio-circle",{checked:a.selectedPayment===t.type}])},null,2)])],10,["onClick"])))),128))])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 订单信息 "),a.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:1,class:"order-info-section"},[e.createElementVNode("text",{class:"section-title"},"订单信息"),e.createElementVNode("view",{class:"order-details"},[e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"商品名称:"),e.createElementVNode("text",{class:"order-value"},e.toDisplayString(a.selectedProduct.name),1)]),e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"商品价格:"),e.createElementVNode("text",{class:"order-value"},"¥"+e.toDisplayString(a.selectedProduct.price),1)]),e.createElementVNode("view",{class:"order-item"},[e.createElementVNode("text",{class:"order-label"},"优惠金额:"),e.createElementVNode("text",{class:"order-value"},"¥0.00")]),e.createElementVNode("view",{class:"order-item total"},[e.createElementVNode("text",{class:"order-label"},"实付金额:"),e.createElementVNode("text",{class:"order-value price"},"¥"+e.toDisplayString(a.selectedProduct.price),1)])])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 支付按钮 "),a.selectedProduct?(e.openBlock(),e.createElementBlock("view",{key:2,class:"pay-button-section"},[e.createElementVNode("button",{class:"pay-button",disabled:!a.selectedPayment||a.paying,onClick:a.createPayment},e.toDisplayString(a.paying?"处理中...":`立即支付 ¥${a.selectedProduct.price}`),9,["disabled"])])):e.createCommentVNode("v-if",!0),e.createCommentVNode(" 支付二维码弹窗 "),a.showQRCode?(e.openBlock(),e.createElementBlock("view",{key:3,class:"qr-modal",onClick:a.closeQRCode},[e.createElementVNode("view",{class:"qr-content",onClick:n[0]||(n[0]=e.withModifiers((()=>{}),["stop"]))},[e.createElementVNode("view",{class:"qr-header"},[e.createElementVNode("text",{class:"qr-title"},"扫码支付"),e.createElementVNode("button",{class:"close-btn",onClick:a.closeQRCode},"×")]),e.createElementVNode("view",{class:"qr-body"},[e.createElementVNode("view",{class:"qr-code"},[e.createCommentVNode(" 这里应该显示二维码图片 "),e.createElementVNode("text",{class:"qr-placeholder"},"二维码")]),e.createElementVNode("text",{class:"qr-tip"},"请使用"+e.toDisplayString(a.paymentMethodName)+"扫描二维码完成支付",1),e.createElementVNode("view",{class:"qr-amount"},[e.createElementVNode("text",{class:"amount-label"},"支付金额:"),e.createElementVNode("text",{class:"amount-value"},"¥"+e.toDisplayString(null==(r=a.selectedProduct)?void 0:r.price),1)])]),e.createElementVNode("view",{class:"qr-footer"},[e.createElementVNode("button",{class:"check-btn",onClick:a.checkPaymentStatus}," 检查支付状态 ")])])])):e.createCommentVNode("v-if",!0)])}],["__scopeId","data-v-eade9ab2"],["__file","/Users/<USER>/code/interviewMaster/app/pages/payment/payment.vue"]]);__definePage("pages/index/index",wt),__definePage("pages/login/login",_t),__definePage("pages/interview/interview",Nt),__definePage("pages/history/history",bt),__definePage("pages/profile/profile",Ct),__definePage("pages/payment/payment",St);const Vt=Et({__name:"App",setup(e,{expose:n}){n(),s((()=>{t("log","at App.vue:13","App Launch"),i()})),o((()=>{t("log","at App.vue:19","App Show")})),a((()=>{t("log","at App.vue:24","App Hide")}));const i=async()=>{try{const e=yt();await e.initFromStorage(),t("log","at App.vue:33","应用初始化完成")}catch(e){t("error","at App.vue:35","应用初始化失败:",e)}},r={initApp:i,get onLaunch(){return s},get onShow(){return o},get onHide(){return a},get useUserStore(){return yt}};return Object.defineProperty(r,"__isScriptSetup",{enumerable:!1,value:!0}),r}},[["render",function(t,n,o,a,s,i){return e.openBlock(),e.createElementBlock("view",{id:"app"},[e.createCommentVNode(" 应用根组件 ")])}],["__file","/Users/<USER>/code/interviewMaster/app/App.vue"]]);const{app:kt,Vuex:It,Pinia:xt}=function(){const t=e.createVueApp(Vt),n=function(){const t=e.effectScope(!0),n=t.run((()=>e.ref({})));let o=[],a=[];const s=e.markRaw({install(e){v(s),s._a=e,e.provide(h,s),e.config.globalProperties.$pinia=s,N&&J(e,s),a.forEach((e=>o.push(e))),a=[]},use(e){return this._a?o.push(e):a.push(e),this},_p:o,_a:null,_e:t,_s:new Map,state:n});return N&&"undefined"!=typeof Proxy&&s.use(Z),s}();return t.use(n),{app:t,pinia:n}}();uni.Vuex=It,uni.Pinia=xt,kt.provide("__globalStyles",__uniConfig.styles),kt._component.mpType="app",kt._component.render=()=>{},kt.mount("#app")}(Vue);
