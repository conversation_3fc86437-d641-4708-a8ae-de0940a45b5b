<template>
  <view class="interview-container">
    <!-- 状态栏 -->
    <view class="status-bar">
      <view class="status-info">
        <text class="status-text">{{ statusText }}</text>
        <view class="status-indicator" :class="statusClass"></view>
      </view>
      <view class="session-info">
        <text class="session-text">会话: {{ sessionId || '未连接' }}</text>
      </view>
    </view>
    
    <!-- 主控制区域 -->
    <view class="control-area">
      <!-- 中心按钮 -->
      <view class="center-button-container">
        <button
          class="center-button"
          :class="{
            active: isInterviewActive,
            disabled: !canStartInterview,
            connecting: interviewStatus === 'connecting'
          }"
          @click="toggleInterview"
        >
          <view class="button-icon">
            <text class="icon">{{ buttonIcon }}</text>
          </view>
          <text class="button-text">{{ buttonText }}</text>
        </button>

        <!-- 实时交互动画 -->
        <view v-if="isInterviewActive" class="realtime-animation">
          <view class="wave wave1"></view>
          <view class="wave wave2"></view>
          <view class="wave wave3"></view>
        </view>
      </view>
      
      <!-- 控制按钮组 -->
      <view class="control-buttons">
        <button
          v-if="isInterviewActive && interviewStatus === 'speaking'"
          class="control-btn interrupt-btn"
          @click="interruptAnswer"
        >
          <text class="btn-icon">⏸</text>
          <text class="btn-text">打断</text>
        </button>

        <button
          v-if="isInterviewActive"
          class="control-btn mute-btn"
          :class="{ active: isMuted }"
          @click="toggleMute"
        >
          <text class="btn-icon">{{ isMuted ? '🔇' : '🔊' }}</text>
          <text class="btn-text">{{ isMuted ? '取消静音' : '静音' }}</text>
        </button>

        <button
          class="control-btn end-btn"
          @click="endInterviewConfirm"
        >
          <text class="btn-icon">⏹</text>
          <text class="btn-text">结束</text>
        </button>
      </view>
    </view>
    
    <!-- 历史记录 -->
    <view class="history-section">
      <view class="history-header" @click="toggleHistory">
        <text class="history-title">历史记录</text>
        <text class="history-toggle">{{ showHistory ? '收起' : '展开' }}</text>
      </view>
      
      <view v-if="showHistory" class="history-list">
        <view 
          v-for="(item, index) in recentHistory" 
          :key="index"
          class="history-item"
        >
          <view class="question">
            <text class="label">问题:</text>
            <text class="content">{{ item.question }}</text>
          </view>
          <view class="answer">
            <text class="label">回答:</text>
            <text class="content">{{ item.answer }}</text>
          </view>
          <view class="meta">
            <text class="time">{{ formatTime(item.time) }}</text>
            <view class="feedback">
              <button 
                class="feedback-btn"
                :class="{ active: item.feedback === 1 }"
                @click="giveFeedback(item.id, 1)"
              >
                👍
              </button>
              <button 
                class="feedback-btn"
                :class="{ active: item.feedback === -1 }"
                @click="giveFeedback(item.id, -1)"
              >
                👎
              </button>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 新手引导 -->
    <view v-if="showGuide" class="guide-overlay" @click="closeGuide">
      <view class="guide-content" @click.stop>
        <view class="guide-step" v-if="guideStep === 1">
          <text class="guide-title">欢迎使用面试助手</text>
          <text class="guide-text">按住中心按钮说话，AI会实时为您提供专业的回答建议</text>
          <button class="guide-btn" @click="nextGuide">下一步</button>
        </view>
        
        <view class="guide-step" v-if="guideStep === 2">
          <text class="guide-title">听筒播放</text>
          <text class="guide-text">答案会通过听筒播放，请将手机靠近耳朵以获得最佳体验</text>
          <button class="guide-btn" @click="nextGuide">下一步</button>
        </view>
        
        <view class="guide-step" v-if="guideStep === 3">
          <text class="guide-title">打断功能</text>
          <text class="guide-text">在AI回答时，您可以点击"打断"按钮来停止当前回答</text>
          <button class="guide-btn" @click="closeGuide">开始使用</button>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/stores/user'
import { useGeminiStore } from '@/stores/gemini'

const userStore = useUserStore()
const geminiStore = useGeminiStore()

// 状态数据
const sessionId = ref('')
const interviewStatus = ref('idle') // idle, connecting, active, speaking, ended
const showHistory = ref(false)
const showGuide = ref(false)
const guideStep = ref(1)
const recentHistory = ref([])
const isMuted = ref(false)
const isInterviewActive = ref(false) // 系统只有实时模式，重命名为更清晰的名称

// 录音相关
const recorderManager = uni.getRecorderManager()
const innerAudioContext = uni.createInnerAudioContext()
const audioChunks = ref([]) // 存储音频块用于实时传输

// 计算属性
const statusText = computed(() => {
  const statusMap = {
    idle: '空闲',
    connecting: '连接中...',
    active: '面试进行中',
    speaking: '正在播报...',
    ended: '面试已结束',
    error: '连接错误'
  }
  return statusMap[interviewStatus.value] || '未知状态'
})

const statusClass = computed(() => {
  return `status-${interviewStatus.value}`
})

const buttonIcon = computed(() => {
  switch (interviewStatus.value) {
    case 'connecting':
      return '⏳'
    case 'active':
      return '🎙️'
    case 'speaking':
      return '🗣'
    case 'ended':
      return '✅'
    default:
      return '🚀'
  }
})

const buttonText = computed(() => {
  switch (interviewStatus.value) {
    case 'connecting':
      return '连接中...'
    case 'active':
      return '面试进行中'
    case 'speaking':
      return '正在回答...'
    case 'ended':
      return '面试已结束'
    default:
      return '立即开始'
  }
})

const canStartInterview = computed(() => {
  return interviewStatus.value === 'idle' || !geminiStore.isConnected
})

// 切换面试状态（系统只有实时模式）
const toggleInterview = () => {
  if (!isInterviewActive.value) {
    startInterview()
  } else {
    endInterview()
  }
}

// 开始面试
const startInterview = () => {
  if (!geminiStore.isConnected) {
    interviewStatus.value = 'connecting'
    geminiStore.connect().then(() => {
      if (canStartInterview.value) {
        startRecording()
      }
    }).catch(err => {
      console.error("连接失败，无法开始面试", err)
      interviewStatus.value = 'idle'
      uni.showToast({
        title: '连接失败，请重试',
        icon: 'error'
      })
    })
    return
  }

  if (!canStartInterview.value) return

  startRecording()
}

// 开始录音（系统只有实时模式）
const startRecording = () => {
  isInterviewActive.value = true
  geminiStore.startInterview()
  interviewStatus.value = 'active'

  // 开始连续录音，用于实时传输
  recorderManager.start({
    duration: 600000, // 10分钟最大时长
    sampleRate: 16000,
    numberOfChannels: 1,
    encodeBitRate: 48000,
    format: 'wav',
    frameSize: 1024 // 设置帧大小以启用 onFrameRecorded 回调
  })
}

// 结束面试
const endInterview = () => {
  isInterviewActive.value = false
  geminiStore.endInterview()
  recorderManager.stop()
  interviewStatus.value = 'idle'
}

// 静音/取消静音
const toggleMute = () => {
  isMuted.value = !isMuted.value
  // 静音状态会在音频帧处理中生效，停止发送音频数据
  console.log(isMuted.value ? '已静音' : '取消静音')
}

// 打断回答
const interruptAnswer = () => {
  geminiStore.sendInterrupt()
  innerAudioContext.stop()
}

// 结束面试
const endInterviewConfirm = () => {
  uni.showModal({
    title: '确认',
    content: '确定要结束本次面试吗？',
    success: (res) => {
      if (res.confirm) {
        if (isInterviewActive.value) {
          endInterview()
        }
        geminiStore.disconnect()
        uni.navigateBack()
      }
    }
  })
}

// 切换历史记录显示
const toggleHistory = () => {
  showHistory.value = !showHistory.value
}

// 给出反馈
const giveFeedback = (logId, feedback) => {
  // TODO: 调用API给出反馈
  const item = recentHistory.value.find(item => item.id === logId)
  if (item) {
    item.feedback = item.feedback === feedback ? 0 : feedback
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
}

// 新手引导
const nextGuide = () => {
  if (guideStep.value < 3) {
    guideStep.value++
  } else {
    closeGuide()
  }
}

const closeGuide = () => {
  showGuide.value = false
  // 保存引导状态
  uni.setStorageSync('guide_shown', true)
}

// 初始化
onMounted(async () => {
  // 检查登录状态
  if (!userStore.isLoggedIn) {
    uni.redirectTo({
      url: '/pages/login/login'
    })
    return
  }
  
  // 检查是否显示新手引导
  const guideShown = uni.getStorageSync('guide_shown')
  if (!guideShown) {
    showGuide.value = true
  }
  
  // 监听Gemini状态变化
  geminiStore.$subscribe((mutation, state) => {
    if (mutation.storeId === 'gemini') {
      interviewStatus.value = state.interviewStatus
    }
  })
  
  // 设置录音事件监听
  recorderManager.onStart(() => {
    console.log('面试录音开始（实时模式），等待音频帧...')
  })

  // 实时音频数据处理
  recorderManager.onFrameRecorded && recorderManager.onFrameRecorded((res) => {
    if (isInterviewActive.value && !isMuted.value && geminiStore.isConnected) {
      // 实时发送音频帧
      const audioData = res.frameBuffer
      if (audioData && audioData.byteLength > 0) {
        console.log('收到音频帧，大小:', audioData.byteLength)
        // 发送原始音频数据到Gemini Live API
        try {
          geminiStore.sendAudio(audioData)
        } catch (error) {
          console.error('实时音频数据发送失败:', error)
        }
      }
    }
  })

  recorderManager.onStop((res) => {
    console.log('面试录音结束（实时模式），音频流已在实时处理中')
    // 系统只有实时模式，录音结束时不需要额外处理
    // 所有音频数据已通过 onFrameRecorded 实时发送
  })
  
  recorderManager.onError((err) => {
    console.error('面试录音错误', err)
    isInterviewActive.value = false
    interviewStatus.value = 'idle'
    
    // 判断是否为权限错误
    if (err.errMsg.includes('auth') || err.errMsg.includes('permission')) {
       uni.showModal({
        title: '权限申请',
        content: '需要录音权限才能使用面试功能，请在设置中开启。',
        success: (res) => {
          if (res.confirm) {
            uni.openSetting();
          }
        }
      });
    } else {
      uni.showToast({
        title: '录音失败，请稍后重试',
        icon: 'none'
      })
    }
  })
  
  // 设置音频播放事件监听
  innerAudioContext.onPlay(() => {
    console.log('音频播放开始')
  })
  
  innerAudioContext.onEnded(() => {
    console.log('音频播放结束')
    interviewStatus.value = isInterviewActive.value ? 'active' : 'idle'
  })

  innerAudioContext.onError((err) => {
    console.error('音频播放错误', err)
    interviewStatus.value = isInterviewActive.value ? 'active' : 'idle'
  })
})

onUnmounted(() => {
  // 清理资源
  if (isInterviewActive.value) {
    endInterview()
  }
  recorderManager.stop()
  innerAudioContext.destroy()
  geminiStore.disconnect()
})
</script>

<style lang="scss" scoped>
.interview-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  padding: 40rpx 30rpx;
  position: relative;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  padding: 20rpx 30rpx;
  margin-bottom: 60rpx;
  
  .status-info {
    display: flex;
    align-items: center;
    gap: 15rpx;
    
    .status-text {
      color: white;
      font-size: 28rpx;
    }
    
    .status-indicator {
      width: 20rpx;
      height: 20rpx;
      border-radius: 50%;
      
      &.status-idle {
        background: #4CAF50;
      }
      
      &.status-connecting {
        background: #FF9800;
        animation: pulse 1s infinite;
      }
      
      &.status-listening {
        background: #2196F3;
        animation: pulse 0.5s infinite;
      }
      
      &.status-thinking {
        background: #9C27B0;
        animation: pulse 0.8s infinite;
      }
      
      &.status-speaking {
        background: #F44336;
        animation: pulse 0.3s infinite;
      }
      
      &.status-error {
        background: #F44336;
      }
    }
  }
  
  .session-info {
    .session-text {
      color: rgba(255, 255, 255, 0.8);
      font-size: 24rpx;
    }
  }
}

.control-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
}

.center-button-container {
  position: relative;
  margin-bottom: 60rpx;
  
  .center-button {
    width: 300rpx;
    height: 300rpx;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    
    &.active {
      background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
      transform: scale(1.1);
    }

    &.connecting {
      background: linear-gradient(135deg, #ffa726 0%, #ff9800 100%);
      animation: pulse 1.5s infinite;
    }

    &.disabled {
      opacity: 0.5;
    }
    
    .button-icon {
      font-size: 80rpx;
      margin-bottom: 10rpx;
    }
    
    .button-text {
      color: white;
      font-size: 24rpx;
      font-weight: bold;
    }
  }
  
  .realtime-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);

    .wave {
      position: absolute;
      border: 4rpx solid rgba(255, 107, 107, 0.4);
      border-radius: 50%;
      animation: realtimeWave 1.5s infinite;

      &.wave1 {
        width: 320rpx;
        height: 320rpx;
        margin: -160rpx 0 0 -160rpx;
      }

      &.wave2 {
        width: 360rpx;
        height: 360rpx;
        margin: -180rpx 0 0 -180rpx;
        animation-delay: 0.2s;
      }

      &.wave3 {
        width: 400rpx;
        height: 400rpx;
        margin: -200rpx 0 0 -200rpx;
        animation-delay: 0.4s;
      }
    }
  }
}

.control-buttons {
  display: flex;
  gap: 40rpx;
  
  .control-btn {
    width: 120rpx;
    height: 120rpx;
    border-radius: 50%;
    border: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    
    .btn-icon {
      font-size: 40rpx;
      margin-bottom: 5rpx;
    }
    
    .btn-text {
      font-size: 20rpx;
      color: white;
    }
    
    &.interrupt-btn {
      background: #FF9800;
    }

    &.mute-btn {
      background: #2196F3;

      &.active {
        background: #F44336;
      }
    }

    &.end-btn {
      background: #F44336;
    }
  }
}

.history-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15rpx;
  overflow: hidden;
  
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
    
    .history-title {
      color: white;
      font-size: 28rpx;
      font-weight: bold;
    }
    
    .history-toggle {
      color: rgba(255, 255, 255, 0.8);
      font-size: 24rpx;
    }
  }
  
  .history-list {
    max-height: 400rpx;
    overflow-y: auto;
    
    .history-item {
      padding: 30rpx;
      border-bottom: 1rpx solid rgba(255, 255, 255, 0.05);
      
      .question, .answer {
        margin-bottom: 15rpx;
        
        .label {
          color: rgba(255, 255, 255, 0.8);
          font-size: 24rpx;
          margin-right: 10rpx;
        }
        
        .content {
          color: white;
          font-size: 26rpx;
        }
      }
      
      .meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .time {
          color: rgba(255, 255, 255, 0.6);
          font-size: 22rpx;
        }
        
        .feedback {
          display: flex;
          gap: 10rpx;
          
          .feedback-btn {
            width: 60rpx;
            height: 60rpx;
            border-radius: 50%;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            font-size: 24rpx;
            
            &.active {
              background: rgba(255, 255, 255, 0.3);
            }
          }
        }
      }
    }
  }
}

.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .guide-content {
    background: white;
    border-radius: 20rpx;
    padding: 60rpx 40rpx;
    margin: 0 60rpx;
    text-align: center;
    
    .guide-step {
      .guide-title {
        display: block;
        font-size: 36rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 30rpx;
      }
      
      .guide-text {
        display: block;
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
        margin-bottom: 40rpx;
      }
      
      .guide-btn {
        width: 200rpx;
        height: 80rpx;
        background: #667eea;
        color: white;
        border: none;
        border-radius: 10rpx;
        font-size: 28rpx;
      }
    }
  }
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

@keyframes wave {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

@keyframes realtimeWave {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.15);
    opacity: 0.4;
  }
  100% {
    transform: scale(1.3);
    opacity: 0;
  }
}
</style>
