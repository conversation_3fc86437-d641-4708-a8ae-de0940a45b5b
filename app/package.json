{"name": "interview-master-app", "version": "1.0.0", "description": "面试官见招拆招App前端项目", "main": "main.js", "scripts": {"dev": "uni", "build": "uni build", "build:h5": "uni build:h5", "build:mp-weixin": "uni build:mp-weixin", "build:app": "uni build:app", "serve": "npm run dev"}, "dependencies": {"@google/genai": "^1.8.0", "@google/generative-ai": "^0.24.1", "pinia": "^2.1.0", "vue": "^3.3.0"}, "devDependencies": {"@types/node": "^20.0.0", "sass": "^1.60.0", "typescript": "^5.0.0", "vite": "^4.3.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"]}