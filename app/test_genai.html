<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 Google GenAI SDK</title>
</head>
<body>
    <h1>测试 Google GenAI SDK</h1>
    <button id="testBtn">测试连接</button>
    <div id="output"></div>

    <script type="module">
        import { GoogleGenAI, Modality } from './node_modules/@google/genai/dist/web/index.mjs';
        
        const output = document.getElementById('output');
        const testBtn = document.getElementById('testBtn');
        
        function log(message) {
            output.innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }
        
        testBtn.addEventListener('click', async () => {
            try {
                log('开始测试 GoogleGenAI...');

                // 添加Web API polyfill测试
                log('URL 构造函数: ' + (typeof URL !== 'undefined' ? '存在' : '不存在'));
                log('Headers 构造函数: ' + (typeof Headers !== 'undefined' ? '存在' : '不存在'));
                log('WebSocket 构造函数: ' + (typeof WebSocket !== 'undefined' ? '存在' : '不存在'));

                // 测试创建实例
                const ai = new GoogleGenAI({
                    apiKey: 'test-key'
                });
                log('GoogleGenAI 实例创建成功');

                // 检查是否有live API
                if (ai.live) {
                    log('ai.live 存在，支持Live API');
                    log('ai.live 方法: ' + Object.getOwnPropertyNames(ai.live));
                } else {
                    log('ai.live 不存在，不支持Live API');
                }

                // 检查Modality
                log('Modality.AUDIO: ' + Modality.AUDIO);

                // 测试URL构造
                try {
                    const testUrl = new URL('https://example.com/test');
                    log('URL 构造测试成功: ' + testUrl.href);
                } catch (urlError) {
                    log('URL 构造测试失败: ' + urlError.message);
                }

                // 测试Headers构造
                try {
                    const testHeaders = new Headers({'Content-Type': 'application/json'});
                    log('Headers 构造测试成功: ' + testHeaders.get('content-type'));
                } catch (headersError) {
                    log('Headers 构造测试失败: ' + headersError.message);
                }

            } catch (error) {
                log('错误: ' + error.message);
                log('错误堆栈: ' + error.stack);
                console.error(error);
            }
        });
    </script>
</body>
</html>
